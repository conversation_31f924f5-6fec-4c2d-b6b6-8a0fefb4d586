// API客户端配置和类型定义
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  code?: string;
  errors?: Record<string, string[]>;
}

export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  errors?: Record<string, string[]>;
}

export class ApiClientError extends Error {
  public code?: string;
  public status?: number;
  public errors?: Record<string, string[]>;

  constructor(message: string, code?: string, status?: number, errors?: Record<string, string[]>) {
    super(message);
    this.name = 'ApiClientError';
    this.code = code;
    this.status = status;
    this.errors = errors;
  }
}

// API客户端配置
interface ApiClientConfig {
  baseURL?: string;
  timeout?: number;
  headers?: Record<string, string>;
  onError?: (error: ApiClientError) => void;
  onSuccess?: (response: any) => void;
}

class ApiClient {
  private baseURL: string;
  private timeout: number;
  private defaultHeaders: Record<string, string>;
  private onError?: (error: ApiClientError) => void;
  private onSuccess?: (response: any) => void;

  constructor(config: ApiClientConfig = {}) {
    this.baseURL = config.baseURL || '/api';
    this.timeout = config.timeout || 10000;
    this.defaultHeaders = {
      'Content-Type': 'application/json',
      ...config.headers,
    };
    this.onError = config.onError;
    this.onSuccess = config.onSuccess;
  }

  // 设置认证token
  setAuthToken(token: string) {
    this.defaultHeaders['Authorization'] = `Bearer ${token}`;
  }

  // 移除认证token
  removeAuthToken() {
    delete this.defaultHeaders['Authorization'];
  }

  // 通用请求方法
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    // 创建AbortController用于超时控制
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const response = await fetch(url, {
        ...options,
        headers: {
          ...this.defaultHeaders,
          ...options.headers,
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // 解析响应
      let responseData: ApiResponse<T>;
      
      try {
        responseData = await response.json();
      } catch {
        // 如果响应不是JSON，创建默认响应结构
        responseData = {
          success: response.ok,
          message: response.ok ? 'Success' : 'Request failed',
        };
      }

      // 检查HTTP状态码
      if (!response.ok) {
        const error = new ApiClientError(
          responseData.message || `HTTP ${response.status}: ${response.statusText}`,
          responseData.code,
          response.status,
          responseData.errors
        );
        
        this.onError?.(error);
        throw error;
      }

      // 检查业务状态码
      if (!responseData.success) {
        const error = new ApiClientError(
          responseData.message || 'Request failed',
          responseData.code,
          response.status,
          responseData.errors
        );
        
        this.onError?.(error);
        throw error;
      }

      this.onSuccess?.(responseData);
      return responseData;

    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof ApiClientError) {
        throw error;
      }

      // 处理网络错误、超时等
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          const timeoutError = new ApiClientError('Request timeout', 'TIMEOUT', 408);
          this.onError?.(timeoutError);
          throw timeoutError;
        }
        
        const networkError = new ApiClientError(
          error.message || 'Network error',
          'NETWORK_ERROR',
          0
        );
        this.onError?.(networkError);
        throw networkError;
      }

      // 未知错误
      const unknownError = new ApiClientError('Unknown error', 'UNKNOWN_ERROR', 0);
      this.onError?.(unknownError);
      throw unknownError;
    }
  }

  // GET请求
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    let url = endpoint;
    
    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, String(value));
        }
      });
      
      if (searchParams.toString()) {
        url += `?${searchParams.toString()}`;
      }
    }

    return this.request<T>(url, {
      method: 'GET',
    });
  }

  // POST请求
  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // PUT请求
  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // PATCH请求
  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    });
  }

  // DELETE请求
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
    });
  }

  // 文件上传
  async upload<T>(endpoint: string, formData: FormData): Promise<ApiResponse<T>> {
    const headers = { ...this.defaultHeaders };
    delete headers['Content-Type']; // 让浏览器自动设置multipart/form-data

    return this.request<T>(endpoint, {
      method: 'POST',
      body: formData,
      headers,
    });
  }
}

// 创建默认的API客户端实例
export const apiClient = new ApiClient({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
  timeout: 10000,
});

// 导出类型和客户端
export { ApiClient };
export default apiClient;
