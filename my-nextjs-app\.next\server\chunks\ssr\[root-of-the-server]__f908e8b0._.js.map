{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/NavigationV80.tsx"], "sourcesContent": ["import { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Package, Search, Smartphone, Palette, Compass } from \"lucide-react\";\n\ninterface NavigationV80Props {\n  onNavigate?: (page: string) => void;\n  onCategoryChange?: (category: string) => void;\n  onLoginClick?: () => void;\n}\n\nexport function NavigationV80({ onNavigate, onCategoryChange, onLoginClick }: NavigationV80Props) {\n  const handleNavigate = (page: string) => {\n    onNavigate?.(page);\n  };\n\n  const handleDownloadApp = () => {\n    console.log(\"下载APP\");\n    // 这里可以添加实际的下载逻辑，比如跳转到应用商店或显示下载选项\n  };\n\n  return (\n    <nav className=\"bg-white/70 backdrop-blur-xl border-b border-slate-200/60 sticky top-0 z-[70]\">\n      <div className=\"max-w-7xl mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* 左侧 - Logo */}\n          <div className=\"flex items-center\">\n            <div className=\"flex items-center gap-3 cursor-pointer\" onClick={() => handleNavigate(\"home\")}>\n              <div className=\"relative\">\n                <div className=\"absolute inset-0 bg-blue-500/10 rounded-xl blur-sm\"></div>\n                <div className=\"relative p-2 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-xl border border-blue-200/30\">\n                  <Package className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              </div>\n              <span className=\"text-slate-900 text-lg font-bold tracking-tight\">U-bund</span>\n            </div>\n          </div>\n\n          {/* 右侧 - 导航按钮和登录 */}\n          <div className=\"flex items-center gap-6\">\n            {/* 导航按钮组 */}\n            <div className=\"hidden md:flex items-center gap-2\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => handleNavigate(\"services\")}\n                className=\"flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 text-slate-600 hover:text-slate-900 hover:bg-slate-100/80\"\n              >\n                <Search className=\"h-4 w-4\" />\n                找服务\n              </Button>\n              \n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={handleDownloadApp}\n                className=\"flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 text-slate-600 hover:text-slate-900 hover:bg-slate-100/80\"\n              >\n                <Smartphone className=\"h-4 w-4\" />\n                下载APP\n              </Button>\n              \n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => handleNavigate(\"support\")}\n                className=\"flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 text-slate-600 hover:text-slate-900 hover:bg-slate-100/80\"\n              >\n                <Palette className=\"h-4 w-4\" />\n                定制服务*\n              </Button>\n              \n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => handleNavigate(\"gallery\")}\n                className=\"flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 text-slate-600 hover:text-slate-900 hover:bg-slate-100/80\"\n              >\n                <Compass className=\"h-4 w-4\" />\n                探索*\n              </Button>\n            </div>\n\n            {/* 分隔线 */}\n            <div className=\"hidden md:block w-px h-6 bg-slate-200/60\"></div>\n\n            {/* 登录/注册按钮 */}\n            <Button\n              size=\"sm\"\n              onClick={onLoginClick}\n              className=\"bg-blue-600 hover:bg-blue-700 text-white px-6\"\n            >\n              登录 / 注册\n            </Button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;;;;AAQO,SAAS,cAAc,EAAE,UAAU,EAAE,gBAAgB,EAAE,YAAY,EAAsB;IAC9F,MAAM,iBAAiB,CAAC;QACtB,aAAa;IACf;IAEA,MAAM,oBAAoB;QACxB,QAAQ,GAAG,CAAC;IACZ,iCAAiC;IACnC;IAEA,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;4BAAyC,SAAS,IAAM,eAAe;;8CACpF,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;;;;;sDACf,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC,4RAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGvB,6WAAC;oCAAK,WAAU;8CAAkD;;;;;;;;;;;;;;;;;kCAKtE,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,WAAU;;0DAEV,6WAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAIhC,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,6WAAC,kSAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAIpC,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,WAAU;;0DAEV,6WAAC,4RAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAIjC,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,WAAU;;0DAEV,6WAAC,4RAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;0CAMnC,6WAAC;gCAAI,WAAU;;;;;;0CAGf,6WAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/CategoryNavigation.tsx"], "sourcesContent": ["import { useState, useRef, useEffect } from \"react\";\nimport { Button } from \"@/components/ui/button\";\nimport { \n  TrendingUp,\n  Palette, \n  Code, \n  Megaphone, \n  Camera, \n  PenTool,\n  Music,\n  Briefcase,\n  DollarSign,\n  Scale,\n  GraduationCap\n} from \"lucide-react\";\n\ninterface CategoryNavigationProps {\n  activeCategory?: string;\n  onCategoryChange?: (category: string) => void;\n  onSubCategoryClick?: (category: string, subCategory: string) => void;\n}\n\nexport function CategoryNavigation({ activeCategory = \"popular\", onCategoryChange, onSubCategoryClick }: CategoryNavigationProps) {\n  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);\n  const [isHovering, setIsHovering] = useState(false);\n  const [activeSubCategory, setActiveSubCategory] = useState<string | null>(null);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const timeoutRef = useRef<NodeJS.Timeout | null>(null);\n\n  const mainCategories = [\n    { id: \"popular\", label: \"热门\", icon: TrendingUp },\n    { id: \"design\", label: \"设计\", icon: Palette },\n    { id: \"tech\", label: \"技术\", icon: Code },\n    { id: \"marketing\", label: \"营销\", icon: Megaphone },\n    { id: \"video\", label: \"视频\", icon: Camera },\n    { id: \"writing\", label: \"文案\", icon: PenTool },\n    { id: \"music\", label: \"音乐\", icon: Music },\n    { id: \"business\", label: \"商务\", icon: Briefcase },\n    { id: \"finance\", label: \"金融\", icon: DollarSign },\n    { id: \"legal\", label: \"法律\", icon: Scale },\n    { id: \"academic\", label: \"学业\", icon: GraduationCap }\n  ];\n\n  // 子分类数据\n  const subCategories = {\n    popular: {\n      title: \"热门服务\",\n      sections: [\n        {\n          title: \"设计类\",\n          items: [\"Logo设计\", \"海报设计\", \"包装设计\", \"UI设计\"]\n        },\n        {\n          title: \"技术类\", \n          items: [\"网站开发\", \"小程序开发\", \"APP开发\", \"系统开发\"]\n        }\n      ]\n    },\n    design: {\n      title: \"设计\",\n      sections: [\n        {\n          title: \"平面设计\",\n          items: [\"Logo设计\", \"海报设计\", \"名片设计\", \"宣传册设计\"]\n        },\n        {\n          title: \"UI/UX设计\",\n          items: [\"网页设计\", \"APP界面\", \"小程序界面\", \"交互设计\"]\n        }\n      ]\n    },\n    tech: {\n      title: \"技术\",\n      sections: [\n        {\n          title: \"网站开发\",\n          items: [\"企业官网\", \"电商网站\", \"响应式网站\", \"定制开发\"]\n        },\n        {\n          title: \"移动开发\",\n          items: [\"微信小程序\", \"APP开发\", \"H5开发\", \"跨平台开发\"]\n        }\n      ]\n    }\n  };\n\n  const handleCategoryClick = (categoryId: string) => {\n    // 确保调用回调函数\n    if (onCategoryChange) {\n      onCategoryChange(categoryId);\n    }\n    setHoveredCategory(null);\n    setIsHovering(false);\n    setActiveSubCategory(null);\n    \n    // 添加调试信息\n    console.log(`切换到分类: ${categoryId}`);\n  };\n\n  const handleMouseEnter = (categoryId: string) => {\n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current);\n    }\n    setHoveredCategory(categoryId);\n    setIsHovering(true);\n  };\n\n  const handleMouseLeave = () => {\n    timeoutRef.current = setTimeout(() => {\n      setHoveredCategory(null);\n      setIsHovering(false);\n    }, 150);\n  };\n\n  const handleSubCategoryClick = (subCategory: string) => {\n    if (hoveredCategory && onSubCategoryClick) {\n      onSubCategoryClick(hoveredCategory, subCategory);\n    }\n    setActiveSubCategory(subCategory);\n    setHoveredCategory(null);\n    setIsHovering(false);\n  };\n\n  useEffect(() => {\n    return () => {\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <div className=\"bg-white/80 backdrop-blur-xl border-b border-slate-200/40 relative z-[60]\">\n      <div className=\"max-w-7xl mx-auto px-4\">\n        {/* Apple风格水平导航栏 */}\n        <div className=\"flex items-center justify-center space-x-8 py-3\">\n          {mainCategories.map((category) => (\n            <div\n              key={category.id}\n              className=\"relative\"\n              onMouseEnter={() => handleMouseEnter(category.id)}\n              onMouseLeave={handleMouseLeave}\n            >\n              <Button\n                variant=\"ghost\"\n                onClick={() => handleCategoryClick(category.id)}\n                className={`px-3 py-2 text-sm transition-colors duration-200 ${\n                  activeCategory === category.id\n                    ? 'text-slate-900'\n                    : 'text-slate-600 hover:text-slate-900'\n                }`}\n              >\n                {category.label}\n              </Button>\n            </div>\n          ))}\n        </div>\n\n        {/* 下拉菜单 */}\n        {isHovering && hoveredCategory && subCategories[hoveredCategory as keyof typeof subCategories] && (\n          <div\n            ref={dropdownRef}\n            className=\"absolute left-0 right-0 top-full bg-white/95 backdrop-blur-xl border-b border-slate-200/40 shadow-lg\"\n            onMouseEnter={() => {\n              if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n              }\n              setIsHovering(true);\n            }}\n            onMouseLeave={handleMouseLeave}\n          >\n            <div className=\"max-w-7xl mx-auto px-4 py-6\">\n              <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8\">\n                {subCategories[hoveredCategory as keyof typeof subCategories]?.sections.map((section, sectionIndex) => (\n                  <div key={sectionIndex} className=\"space-y-3\">\n                    <h3 className=\"font-semibold text-slate-900 text-sm\">{section.title}</h3>\n                    <ul className=\"space-y-2\">\n                      {section.items.map((item, itemIndex) => (\n                        <li key={itemIndex}>\n                          <button\n                            onClick={() => handleSubCategoryClick(item)}\n                            className={`text-sm transition-colors duration-200 hover:text-blue-600 ${\n                              activeSubCategory === item\n                                ? 'text-blue-600 font-medium'\n                                : 'text-slate-600'\n                            }`}\n                          >\n                            {item}\n                          </button>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAoBO,SAAS,mBAAmB,EAAE,iBAAiB,SAAS,EAAE,gBAAgB,EAAE,kBAAkB,EAA2B;IAC9H,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,aAAa,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAyB;IAEjD,MAAM,iBAAiB;QACrB;YAAE,IAAI;YAAW,OAAO;YAAM,MAAM,sSAAA,CAAA,aAAU;QAAC;QAC/C;YAAE,IAAI;YAAU,OAAO;YAAM,MAAM,4RAAA,CAAA,UAAO;QAAC;QAC3C;YAAE,IAAI;YAAQ,OAAO;YAAM,MAAM,sRAAA,CAAA,OAAI;QAAC;QACtC;YAAE,IAAI;YAAa,OAAO;YAAM,MAAM,gSAAA,CAAA,YAAS;QAAC;QAChD;YAAE,IAAI;YAAS,OAAO;YAAM,MAAM,0RAAA,CAAA,SAAM;QAAC;QACzC;YAAE,IAAI;YAAW,OAAO;YAAM,MAAM,gSAAA,CAAA,UAAO;QAAC;QAC5C;YAAE,IAAI;YAAS,OAAO;YAAM,MAAM,wRAAA,CAAA,QAAK;QAAC;QACxC;YAAE,IAAI;YAAY,OAAO;YAAM,MAAM,gSAAA,CAAA,YAAS;QAAC;QAC/C;YAAE,IAAI;YAAW,OAAO;YAAM,MAAM,sSAAA,CAAA,aAAU;QAAC;QAC/C;YAAE,IAAI;YAAS,OAAO;YAAM,MAAM,wRAAA,CAAA,QAAK;QAAC;QACxC;YAAE,IAAI;YAAY,OAAO;YAAM,MAAM,4SAAA,CAAA,gBAAa;QAAC;KACpD;IAED,QAAQ;IACR,MAAM,gBAAgB;QACpB,SAAS;YACP,OAAO;YACP,UAAU;gBACR;oBACE,OAAO;oBACP,OAAO;wBAAC;wBAAU;wBAAQ;wBAAQ;qBAAO;gBAC3C;gBACA;oBACE,OAAO;oBACP,OAAO;wBAAC;wBAAQ;wBAAS;wBAAS;qBAAO;gBAC3C;aACD;QACH;QACA,QAAQ;YACN,OAAO;YACP,UAAU;gBACR;oBACE,OAAO;oBACP,OAAO;wBAAC;wBAAU;wBAAQ;wBAAQ;qBAAQ;gBAC5C;gBACA;oBACE,OAAO;oBACP,OAAO;wBAAC;wBAAQ;wBAAS;wBAAS;qBAAO;gBAC3C;aACD;QACH;QACA,MAAM;YACJ,OAAO;YACP,UAAU;gBACR;oBACE,OAAO;oBACP,OAAO;wBAAC;wBAAQ;wBAAQ;wBAAS;qBAAO;gBAC1C;gBACA;oBACE,OAAO;oBACP,OAAO;wBAAC;wBAAS;wBAAS;wBAAQ;qBAAQ;gBAC5C;aACD;QACH;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,WAAW;QACX,IAAI,kBAAkB;YACpB,iBAAiB;QACnB;QACA,mBAAmB;QACnB,cAAc;QACd,qBAAqB;QAErB,SAAS;QACT,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,YAAY;IACpC;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,WAAW,OAAO,EAAE;YACtB,aAAa,WAAW,OAAO;QACjC;QACA,mBAAmB;QACnB,cAAc;IAChB;IAEA,MAAM,mBAAmB;QACvB,WAAW,OAAO,GAAG,WAAW;YAC9B,mBAAmB;YACnB,cAAc;QAChB,GAAG;IACL;IAEA,MAAM,yBAAyB,CAAC;QAC9B,IAAI,mBAAmB,oBAAoB;YACzC,mBAAmB,iBAAiB;QACtC;QACA,qBAAqB;QACrB,mBAAmB;QACnB,cAAc;IAChB;IAEA,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,WAAW,OAAO,EAAE;gBACtB,aAAa,WAAW,OAAO;YACjC;QACF;IACF,GAAG,EAAE;IAEL,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BAEb,6WAAC;oBAAI,WAAU;8BACZ,eAAe,GAAG,CAAC,CAAC,yBACnB,6WAAC;4BAEC,WAAU;4BACV,cAAc,IAAM,iBAAiB,SAAS,EAAE;4BAChD,cAAc;sCAEd,cAAA,6WAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,oBAAoB,SAAS,EAAE;gCAC9C,WAAW,CAAC,iDAAiD,EAC3D,mBAAmB,SAAS,EAAE,GAC1B,mBACA,uCACJ;0CAED,SAAS,KAAK;;;;;;2BAdZ,SAAS,EAAE;;;;;;;;;;gBAqBrB,cAAc,mBAAmB,aAAa,CAAC,gBAA8C,kBAC5F,6WAAC;oBACC,KAAK;oBACL,WAAU;oBACV,cAAc;wBACZ,IAAI,WAAW,OAAO,EAAE;4BACtB,aAAa,WAAW,OAAO;wBACjC;wBACA,cAAc;oBAChB;oBACA,cAAc;8BAEd,cAAA,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;sCACZ,aAAa,CAAC,gBAA8C,EAAE,SAAS,IAAI,CAAC,SAAS,6BACpF,6WAAC;oCAAuB,WAAU;;sDAChC,6WAAC;4CAAG,WAAU;sDAAwC,QAAQ,KAAK;;;;;;sDACnE,6WAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACxB,6WAAC;8DACC,cAAA,6WAAC;wDACC,SAAS,IAAM,uBAAuB;wDACtC,WAAW,CAAC,2DAA2D,EACrE,sBAAsB,OAClB,8BACA,kBACJ;kEAED;;;;;;mDATI;;;;;;;;;;;mCAJL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2B5B", "debugId": null}}, {"offset": {"line": 611, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 706, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6WAAC,+QAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6WAAC,+QAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6WAAC,+QAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 800, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/LandingPage.tsx"], "sourcesContent": ["import { useState, useEffect } from \"react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport {\n  Search,\n  Play,\n  Palette,\n  Code,\n  Video,\n  Megaphone,\n  FileText,\n  Music,\n  TrendingUp,\n  Star,\n  Users,\n  Award,\n  ArrowRight,\n  CheckCircle\n} from \"lucide-react\";\n\ninterface LandingPageProps {\n  onNavigateToServices?: () => void;\n}\n\nexport function LandingPage({ onNavigateToServices }: LandingPageProps) {\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  // 热门商品数据\n  const featuredProducts = [\n    {\n      id: \"1\",\n      name: \"品牌视觉设计\",\n      price: 999,\n      image: \"https://images.unsplash.com/photo-1558655146-d09347e92766?w=300&h=200&fit=crop\",\n      rating: 5.0,\n      reviews: 1234\n    },\n    {\n      id: \"2\",\n      name: \"网站开发定制\",\n      price: 4999,\n      image: \"https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=300&h=200&fit=crop\",\n      rating: 4.9,\n      reviews: 856\n    },\n    {\n      id: \"3\",\n      name: \"短视频制作\",\n      price: 1999,\n      image: \"https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=300&h=200&fit=crop\",\n      rating: 4.8,\n      reviews: 2341\n    },\n    {\n      id: \"4\",\n      name: \"文案策划服务\",\n      price: 699,\n      image: \"https://images.unsplash.com/photo-1455390582262-044cdead277a?w=300&h=200&fit=crop\",\n      rating: 4.7,\n      reviews: 567\n    }\n  ];\n\n  // 热门分类数据\n  const categories = [\n    {\n      id: \"design\",\n      name: \"设计\",\n      description: \"Logo设计、UI设计、平面设计\",\n      icon: Palette,\n      color: \"from-purple-500 to-pink-500\",\n      bgColor: \"bg-purple-50\",\n      count: \"12,000+\"\n    },\n    {\n      id: \"tech\",\n      name: \"技术开发\",\n      description: \"网站开发、APP开发、小程序\",\n      icon: Code,\n      color: \"from-blue-500 to-cyan-500\",\n      bgColor: \"bg-blue-50\",\n      count: \"8,500+\"\n    },\n    {\n      id: \"video\",\n      name: \"视频制作\",\n      description: \"短视频、宣传片、动画制作\",\n      icon: Video,\n      color: \"from-red-500 to-orange-500\",\n      bgColor: \"bg-red-50\",\n      count: \"6,200+\"\n    },\n    {\n      id: \"marketing\",\n      name: \"营销推广\",\n      description: \"文案策划、社媒运营、广告投放\",\n      icon: Megaphone,\n      color: \"from-green-500 to-emerald-500\",\n      bgColor: \"bg-green-50\",\n      count: \"4,800+\"\n    },\n    {\n      id: \"writing\",\n      name: \"文案写作\",\n      description: \"软文写作、新闻稿、产品文案\",\n      icon: FileText,\n      color: \"from-yellow-500 to-amber-500\",\n      bgColor: \"bg-yellow-50\",\n      count: \"3,600+\"\n    },\n    {\n      id: \"music\",\n      name: \"音乐音效\",\n      description: \"原创音乐、配音、音效制作\",\n      icon: Music,\n      color: \"from-indigo-500 to-purple-500\",\n      bgColor: \"bg-indigo-50\",\n      count: \"2,100+\"\n    }\n  ];\n\n  const handleSearch = () => {\n    if (searchQuery.trim()) {\n      onNavigateToServices?.();\n    }\n  };\n\n  const handleCategoryClick = (categoryId: string) => {\n    onNavigateToServices?.();\n  };\n\n  // 模拟热门服务数据\n  const featuredServices = [\n    {\n      id: \"1\",\n      title: \"✨绝美品牌视觉设计，让你的LOGO成为爆款记忆点！\",\n      description: \"姐妹们！这个设计师真的太会了😍 从LOGO到整套VI，各种风格都能拿捏住\",\n      price: 999,\n      originalPrice: 1999,\n      image: \"https://images.unsplash.com/photo-1558655146-d09347e92766?w=400&h=400&fit=crop\",\n      rating: 5.0,\n      reviews: 1234,\n      creator: {\n        name: \"设计师小王\",\n        avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces\",\n        verified: true\n      },\n      tags: [\"热门\", \"原创\", \"好评Top\"]\n    },\n    {\n      id: \"2\", \n      title: \"🚀全栈网站开发YYDS！React+Node.js技术栈\",\n      description: \"宝贝们看过来！这位程序员小哥哥技术真的绝了🔥 响应式设计+全栈开发\",\n      price: 4999,\n      image: \"https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=400&fit=crop\",\n      rating: 4.9,\n      reviews: 856,\n      creator: {\n        name: \"程序员老李\",\n        avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces\",\n        verified: true\n      },\n      tags: [\"专业\", \"全栈\", \"技能之星\"]\n    }\n  ];\n\n  const stats = [\n    { label: \"注册用户\", value: \"100万+\", icon: Users },\n    { label: \"服务订单\", value: \"50万+\", icon: TrendingUp },\n    { label: \"优质服务商\", value: \"10万+\", icon: Award },\n    { label: \"好评率\", value: \"99.8%\", icon: Star }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30\">\n      {/* Hero区域 - 视频介绍 */}\n      <section className=\"relative overflow-hidden py-28\">\n        {/* 视频背景 */}\n        {isClient && (\n          <video\n            key=\"hero-video\"\n            autoPlay\n            muted\n            loop\n            playsInline\n            className=\"absolute inset-0 w-full h-full object-cover z-0\"\n            suppressHydrationWarning\n          >\n            <source src=\"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4\" type=\"video/mp4\" />\n            <source src=\"https://www.w3schools.com/html/mov_bbb.mp4\" type=\"video/mp4\" />\n            Your browser does not support the video tag.\n          </video>\n        )}\n\n        {/* 服务端渲染时的占位符 */}\n        {!isClient && (\n          <div className=\"absolute inset-0 w-full h-full bg-gradient-to-br from-blue-600/20 to-purple-600/20 z-0\" />\n        )}\n\n        {/* 半透明覆盖层 */}\n        <div className=\"absolute inset-0 bg-black/20 z-10\"></div>\n\n        <div className=\"relative z-20 max-w-7xl mx-auto px-4\">\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n            {/* 左侧文字内容 */}\n            <div>\n              <h1 className=\"text-4xl md:text-5xl font-bold text-white mb-6 leading-tight\">\n                发现专业服务\n                <br />\n                <span className=\"text-blue-400\">成就无限可能</span>\n              </h1>\n              <p className=\"text-white/90 mb-8 text-lg leading-relaxed\">\n                U-bund连接全球优秀创作者，为您提供设计、技术、营销等\n                <br />\n                各领域的专业服务。从创意到实现，我们助您实现每一个想法。\n              </p>\n\n              {/* 搜索框 */}\n              <div className=\"relative mb-6\">\n                <Search className=\"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"搜索您需要的服务...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}\n                  className=\"w-full pl-12 pr-4 py-4 text-slate-700 placeholder-slate-400 focus:outline-none bg-white rounded-full shadow-lg border border-slate-200/60\"\n                />\n              </div>\n\n              {/* 热门商品快捷按钮 */}\n              <div className=\"flex flex-wrap gap-3\">\n                {featuredProducts.map((product) => (\n                  <Button\n                    key={product.id}\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={onNavigateToServices}\n                    className=\"bg-white/80 border-slate-200/60 text-slate-600 hover:text-slate-900 hover:bg-white\"\n                  >\n                    {product.name}\n                  </Button>\n                ))}\n              </div>\n            </div>\n\n            {/* 右侧视频/图片区域 */}\n            <div className=\"relative\">\n              {/* 这里可以放置其他内容 */}\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Stats Section */}\n      <section className=\"py-16 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-3xl mx-4\">\n        <div className=\"max-w-6xl mx-auto px-4\">\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n            {stats.map((stat, index) => (\n              <div key={index} className=\"text-center\">\n                <div className=\"inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl mb-4\">\n                  <stat.icon className=\"h-6 w-6 text-blue-600\" />\n                </div>\n                <div className=\"text-3xl font-bold text-slate-900 mb-2\">{stat.value}</div>\n                <div className=\"text-slate-600\">{stat.label}</div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Featured Services */}\n      <section className=\"py-16 px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-slate-900 mb-4\">\n              热门精选服务\n            </h2>\n            <p className=\"text-xl text-slate-600\">\n              发现最受欢迎的优质服务，让专业的人做专业的事\n            </p>\n          </div>\n          \n          <div className=\"grid md:grid-cols-2 gap-8\">\n            {featuredServices.map((service) => (\n              <Card key={service.id} className=\"group hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm\">\n                <CardContent className=\"p-0\">\n                  <div className=\"relative overflow-hidden rounded-t-lg\">\n                    <img \n                      src={service.image} \n                      alt={service.title}\n                      className=\"w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300\"\n                    />\n                    <div className=\"absolute top-4 left-4 flex gap-2\">\n                      {service.tags.map((tag, index) => (\n                        <Badge key={index} variant=\"secondary\" className=\"bg-white/90 text-slate-700\">\n                          {tag}\n                        </Badge>\n                      ))}\n                    </div>\n                  </div>\n                  \n                  <div className=\"p-6\">\n                    <h3 className=\"font-bold text-lg text-slate-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors\">\n                      {service.title}\n                    </h3>\n                    \n                    <p className=\"text-slate-600 text-sm mb-4 line-clamp-2\">\n                      {service.description}\n                    </p>\n                    \n                    <div className=\"flex items-center gap-3 mb-4\">\n                      <Avatar className=\"h-8 w-8\">\n                        <AvatarImage src={service.creator.avatar} />\n                        <AvatarFallback>{service.creator.name[0]}</AvatarFallback>\n                      </Avatar>\n                      <div className=\"flex-1\">\n                        <div className=\"flex items-center gap-1\">\n                          <span className=\"text-sm font-medium text-slate-900\">{service.creator.name}</span>\n                          {service.creator.verified && (\n                            <CheckCircle className=\"h-4 w-4 text-blue-500\" />\n                          )}\n                        </div>\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <Star className=\"h-4 w-4 text-yellow-500 fill-current\" />\n                        <span className=\"text-sm font-medium\">{service.rating}</span>\n                        <span className=\"text-sm text-slate-500\">({service.reviews})</span>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex items-center justify-between\">\n                      <div className=\"flex items-center gap-2\">\n                        <span className=\"text-2xl font-bold text-blue-600\">¥{service.price}</span>\n                        {service.originalPrice && (\n                          <span className=\"text-sm text-slate-500 line-through\">¥{service.originalPrice}</span>\n                        )}\n                      </div>\n                      <Button size=\"sm\" className=\"bg-blue-600 hover:bg-blue-700\">\n                        立即购买\n                      </Button>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n          \n          <div className=\"text-center mt-12\">\n            <Button \n              variant=\"outline\" \n              size=\"lg\"\n              onClick={onNavigateToServices}\n              className=\"px-8 py-3\"\n            >\n              查看更多服务\n              <ArrowRight className=\"ml-2 h-5 w-5\" />\n            </Button>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 px-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl mx-4 text-white\">\n        <div className=\"max-w-4xl mx-auto text-center\">\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-6\">\n            准备开始您的项目了吗？\n          </h2>\n          <p className=\"text-xl mb-8 opacity-90\">\n            加入U-bund，与专业服务提供者合作，让您的想法变为现实\n          </p>\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n            <Button \n              size=\"lg\" \n              variant=\"secondary\"\n              onClick={onNavigateToServices}\n              className=\"bg-white text-blue-600 hover:bg-slate-100 px-8 py-3\"\n            >\n              立即开始\n            </Button>\n            <Button \n              size=\"lg\" \n              variant=\"outline\"\n              className=\"border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3\"\n            >\n              了解更多\n            </Button>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;AAqBO,SAAS,YAAY,EAAE,oBAAoB,EAAoB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,SAAS;IACT,MAAM,mBAAmB;QACvB;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA;YACE,IAAI;YAC<PERSON>,MAAM;YACN,OAAO;YACP,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,QAAQ;YACR,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,QAAQ;YACR,SAAS;QACX;KACD;IAED,SAAS;IACT,MAAM,aAAa;QACjB;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM,4RAAA,CAAA,UAAO;YACb,OAAO;YACP,SAAS;YACT,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM,sRAAA,CAAA,OAAI;YACV,OAAO;YACP,SAAS;YACT,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM,wRAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;YACT,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM,gSAAA,CAAA,YAAS;YACf,OAAO;YACP,SAAS;YACT,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM,kSAAA,CAAA,WAAQ;YACd,OAAO;YACP,SAAS;YACT,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,MAAM,wRAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;YACT,OAAO;QACT;KACD;IAED,MAAM,eAAe;QACnB,IAAI,YAAY,IAAI,IAAI;YACtB;QACF;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B;IACF;IAEA,WAAW;IACX,MAAM,mBAAmB;QACvB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,eAAe;YACf,OAAO;YACP,QAAQ;YACR,SAAS;YACT,SAAS;gBACP,MAAM;gBACN,QAAQ;gBACR,UAAU;YACZ;YACA,MAAM;gBAAC;gBAAM;gBAAM;aAAQ;QAC7B;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,OAAO;YACP,OAAO;YACP,QAAQ;YACR,SAAS;YACT,SAAS;gBACP,MAAM;gBACN,QAAQ;gBACR,UAAU;YACZ;YACA,MAAM;gBAAC;gBAAM;gBAAM;aAAO;QAC5B;KACD;IAED,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAQ,OAAO;YAAS,MAAM,wRAAA,CAAA,QAAK;QAAC;QAC7C;YAAE,OAAO;YAAQ,OAAO;YAAQ,MAAM,sSAAA,CAAA,aAAU;QAAC;QACjD;YAAE,OAAO;YAAS,OAAO;YAAQ,MAAM,wRAAA,CAAA,QAAK;QAAC;QAC7C;YAAE,OAAO;YAAO,OAAO;YAAS,MAAM,sRAAA,CAAA,OAAI;QAAC;KAC5C;IAED,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAQ,WAAU;;oBAEhB,0BACC,6WAAC;wBAEC,QAAQ;wBACR,KAAK;wBACL,IAAI;wBACJ,WAAW;wBACX,WAAU;wBACV,wBAAwB;;0CAExB,6WAAC;gCAAO,KAAI;gCAAqF,MAAK;;;;;;0CACtG,6WAAC;gCAAO,KAAI;gCAA6C,MAAK;;;;;;4BAAc;;uBATxE;;;;;oBAeP,CAAC,0BACA,6WAAC;wBAAI,WAAU;;;;;;kCAIjB,6WAAC;wBAAI,WAAU;;;;;;kCAEf,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;;8CAEb,6WAAC;;sDACC,6WAAC;4CAAG,WAAU;;gDAA+D;8DAE3E,6WAAC;;;;;8DACD,6WAAC;oDAAK,WAAU;8DAAgB;;;;;;;;;;;;sDAElC,6WAAC;4CAAE,WAAU;;gDAA6C;8DAExD,6WAAC;;;;;gDAAK;;;;;;;sDAKR,6WAAC;4CAAI,WAAU;;8DACb,6WAAC,0RAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6WAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;oDACxC,WAAU;;;;;;;;;;;;sDAKd,6WAAC;4CAAI,WAAU;sDACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6WAAC,kIAAA,CAAA,SAAM;oDAEL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DAET,QAAQ,IAAI;mDANR,QAAQ,EAAE;;;;;;;;;;;;;;;;8CAavB,6WAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAQrB,6WAAC;gBAAQ,WAAU;0BACjB,cAAA,6WAAC;oBAAI,WAAU;8BACb,cAAA,6WAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6WAAC;gCAAgB,WAAU;;kDACzB,6WAAC;wCAAI,WAAU;kDACb,cAAA,6WAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;;;;;;kDAEvB,6WAAC;wCAAI,WAAU;kDAA0C,KAAK,KAAK;;;;;;kDACnE,6WAAC;wCAAI,WAAU;kDAAkB,KAAK,KAAK;;;;;;;+BALnC;;;;;;;;;;;;;;;;;;;;0BAalB,6WAAC;gBAAQ,WAAU;0BACjB,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,6WAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;sCAKxC,6WAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6WAAC,gIAAA,CAAA,OAAI;oCAAkB,WAAU;8CAC/B,cAAA,6WAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDACC,KAAK,QAAQ,KAAK;wDAClB,KAAK,QAAQ,KAAK;wDAClB,WAAU;;;;;;kEAEZ,6WAAC;wDAAI,WAAU;kEACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBACtB,6WAAC,iIAAA,CAAA,QAAK;gEAAa,SAAQ;gEAAY,WAAU;0EAC9C;+DADS;;;;;;;;;;;;;;;;0DAOlB,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;kEAGhB,6WAAC;wDAAE,WAAU;kEACV,QAAQ,WAAW;;;;;;kEAGtB,6WAAC;wDAAI,WAAU;;0EACb,6WAAC,kIAAA,CAAA,SAAM;gEAAC,WAAU;;kFAChB,6WAAC,kIAAA,CAAA,cAAW;wEAAC,KAAK,QAAQ,OAAO,CAAC,MAAM;;;;;;kFACxC,6WAAC,kIAAA,CAAA,iBAAc;kFAAE,QAAQ,OAAO,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;0EAE1C,6WAAC;gEAAI,WAAU;0EACb,cAAA,6WAAC;oEAAI,WAAU;;sFACb,6WAAC;4EAAK,WAAU;sFAAsC,QAAQ,OAAO,CAAC,IAAI;;;;;;wEACzE,QAAQ,OAAO,CAAC,QAAQ,kBACvB,6WAAC,+SAAA,CAAA,cAAW;4EAAC,WAAU;;;;;;;;;;;;;;;;;0EAI7B,6WAAC;gEAAI,WAAU;;kFACb,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,6WAAC;wEAAK,WAAU;kFAAuB,QAAQ,MAAM;;;;;;kFACrD,6WAAC;wEAAK,WAAU;;4EAAyB;4EAAE,QAAQ,OAAO;4EAAC;;;;;;;;;;;;;;;;;;;kEAI/D,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEAAI,WAAU;;kFACb,6WAAC;wEAAK,WAAU;;4EAAmC;4EAAE,QAAQ,KAAK;;;;;;;oEACjE,QAAQ,aAAa,kBACpB,6WAAC;wEAAK,WAAU;;4EAAsC;4EAAE,QAAQ,aAAa;;;;;;;;;;;;;0EAGjF,6WAAC,kIAAA,CAAA,SAAM;gEAAC,MAAK;gEAAK,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;;mCArDzD,QAAQ,EAAE;;;;;;;;;;sCA+DzB,6WAAC;4BAAI,WAAU;sCACb,cAAA,6WAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;oCACX;kDAEC,6WAAC,sSAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9B,6WAAC;gBAAQ,WAAU;0BACjB,cAAA,6WAAC;oBAAI,WAAU;;sCACb,6WAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,6WAAC;4BAAE,WAAU;sCAA0B;;;;;;sCAGvC,6WAAC;4BAAI,WAAU;;8CACb,6WAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6WAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 1603, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Breadcrumb = React.forwardRef<\n  HTMLElement,\n  React.ComponentPropsWithoutRef<\"nav\"> & {\n    separator?: React.ComponentType<{ className?: string }>\n  }\n>(({ ...props }, ref) => <nav ref={ref} aria-label=\"breadcrumb\" {...props} />)\nBreadcrumb.displayName = \"Breadcrumb\"\n\nconst BreadcrumbList = React.forwardRef<\n  HTMLOListElement,\n  React.ComponentPropsWithoutRef<\"ol\">\n>(({ className, ...props }, ref) => (\n  <ol\n    ref={ref}\n    className={cn(\n      \"flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5\",\n      className\n    )}\n    {...props}\n  />\n))\nBreadcrumbList.displayName = \"BreadcrumbList\"\n\nconst BreadcrumbItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentPropsWithoutRef<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    className={cn(\"inline-flex items-center gap-1.5\", className)}\n    {...props}\n  />\n))\nBreadcrumbItem.displayName = \"BreadcrumbItem\"\n\nconst BreadcrumbLink = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentPropsWithoutRef<\"a\"> & {\n    asChild?: boolean\n  }\n>(({ asChild, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      ref={ref}\n      className={cn(\"transition-colors hover:text-foreground\", className)}\n      {...props}\n    />\n  )\n})\nBreadcrumbLink.displayName = \"BreadcrumbLink\"\n\nconst BreadcrumbPage = React.forwardRef<\n  HTMLSpanElement,\n  React.ComponentPropsWithoutRef<\"span\">\n>(({ className, ...props }, ref) => (\n  <span\n    ref={ref}\n    role=\"link\"\n    aria-disabled=\"true\"\n    aria-current=\"page\"\n    className={cn(\"font-normal text-foreground\", className)}\n    {...props}\n  />\n))\nBreadcrumbPage.displayName = \"BreadcrumbPage\"\n\nconst BreadcrumbSeparator = ({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) => (\n  <li\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"[&>svg]:size-3.5\", className)}\n    {...props}\n  >\n    {children ?? <ChevronRight />}\n  </li>\n)\nBreadcrumbSeparator.displayName = \"BreadcrumbSeparator\"\n\nconst BreadcrumbEllipsis = ({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) => (\n  <span\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\n    {...props}\n  >\n    <MoreHorizontal className=\"h-4 w-4\" />\n    <span className=\"sr-only\">More</span>\n  </span>\n)\nBreadcrumbEllipsis.displayName = \"BreadcrumbEllipsis\"\n\nexport {\n  Breadcrumb,\n  BreadcrumbList,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n  BreadcrumbEllipsis,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AAAA;AAEA;;;;;;AAEA,MAAM,2BAAa,oUAAA,CAAA,aAAgB,CAKjC,CAAC,EAAE,GAAG,OAAO,EAAE,oBAAQ,6WAAC;QAAI,KAAK;QAAK,cAAW;QAAc,GAAG,KAAK;;;;;;AACzE,WAAW,WAAW,GAAG;AAEzB,MAAM,+BAAiB,oUAAA,CAAA,aAAgB,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4FACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,oUAAA,CAAA,aAAgB,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,oUAAA,CAAA,aAAgB,CAKrC,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACnC,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6WAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;AACA,eAAe,WAAW,GAAG;AAE7B,MAAM,+BAAiB,oUAAA,CAAA,aAAgB,CAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6WAAC;QACC,KAAK;QACL,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG;AAE7B,MAAM,sBAAsB,CAAC,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB,iBAC3B,6WAAC;QACC,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,6WAAC,0SAAA,CAAA,eAAY;;;;;;;;;;AAG9B,oBAAoB,WAAW,GAAG;AAElC,MAAM,qBAAqB,CAAC,EAC1B,SAAS,EACT,GAAG,OAC0B,iBAC7B,6WAAC;QACC,MAAK;QACL,eAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4CAA4C;QACzD,GAAG,KAAK;;0BAET,6WAAC,oSAAA,CAAA,iBAAc;gBAAC,WAAU;;;;;;0BAC1B,6WAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAG9B,mBAAmB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1728, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ServicesPage.tsx"], "sourcesContent": ["import { useState } from \"react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport {\n  Breadcrumb,\n  BreadcrumbList,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n} from \"@/components/ui/breadcrumb\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n  DropdownMenuCheckboxItem,\n} from \"@/components/ui/dropdown-menu\";\nimport {\n  Search,\n  Filter,\n  Grid,\n  List,\n  Star,\n  Heart,\n  ShoppingCart,\n  Sparkles,\n  TrendingUp,\n  Award,\n  Zap,\n  ChevronDown,\n  SlidersHorizontal,\n  User,\n  Bot,\n  X,\n  Home,\n  MapPin\n} from \"lucide-react\";\n\ninterface Product {\n  id: string;\n  name: string;\n  description: string;\n  price: number;\n  originalPrice?: number;\n  image: string;\n  rating: number;\n  reviews: number;\n  category: string;\n  tags: string[];\n  discount?: number;\n  isBestseller?: boolean;\n  isNew?: boolean;\n  creator: {\n    name: string;\n    avatar: string;\n    verified?: boolean;\n    location?: string;\n  };\n  favoriteCount: number;\n  specialBadge?: \"upro\" | \"top\" | \"star\";\n}\n\nconst mockProducts: Product[] = [\n  {\n    id: \"1\",\n    name: \"✨绝美品牌视觉设计，让你的LOGO成为爆款记忆点！\",\n    description: \"姐妹们！这个设计师真的太会了😍 从LOGO到整套VI，各种风格都能拿捏住，原创度100%，商用无忧虑～绝对是品牌升级的必备神器！\",\n    price: 999,\n    originalPrice: 1299,\n    image: \"https://images.unsplash.com/photo-1558655146-d09347e92766?w=400&h=400&fit=crop\",\n    rating: 5.0,\n    reviews: 1234,\n    category: \"设计\",\n    tags: [\"热门\", \"原创\"],\n    discount: 23,\n    isBestseller: true,\n    isNew: false,\n    creator: {\n      name: \"设计师小王\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces\",\n      verified: true,\n      location: \"北京\"\n    },\n    favoriteCount: 892,\n    specialBadge: \"upro\"\n  },\n  {\n    id: \"2\",\n    name: \"🚀全栈网站开发YYDS！React+Node.js技术栈，让你的网站飞起来\",\n    description: \"宝贝们看过来！这位程序员小哥哥技术真的绝了🔥 响应式设计+全栈开发+一键部署，从前端到后端全搞定，网站速度嗖嗖的，用户体验直接拉满！\",\n    price: 4999,\n    image: \"https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=400&fit=crop\",\n    rating: 4.9,\n    reviews: 856,\n    category: \"技术\",\n    tags: [\"专业\", \"全栈\"],\n    isBestseller: true,\n    creator: {\n      name: \"程序员老李\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces\",\n      verified: true,\n      location: \"上海\"\n    },\n    favoriteCount: 634,\n    specialBadge: \"top\"\n  },\n  {\n    id: \"3\",\n    name: \"📱小程序开发专家！微信生态全覆盖，让你的生意更上一层楼\",\n    description: \"小程序界的扛把子！🏆 从商城到工具类，各种类型都能搞定，用户体验丝滑到飞起，后台管理系统也是一应俱全！\",\n    price: 2999,\n    image: \"https://images.unsplash.com/photo-1551650975-87deedd944c3?w=400&h=400&fit=crop\",\n    rating: 4.8,\n    reviews: 445,\n    category: \"技术\",\n    tags: [\"小程序\", \"微信\"],\n    creator: {\n      name: \"小程序达人\",\n      avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=faces\",\n      verified: true,\n      location: \"深圳\"\n    },\n    favoriteCount: 378,\n    specialBadge: \"star\"\n  },\n  {\n    id: \"4\",\n    name: \"🎨UI设计大神在线！让你的界面颜值爆表，用户爱不释手\",\n    description: \"设计界的颜值担当！✨ 从APP到网页，每一个像素都精雕细琢，交互设计更是让人眼前一亮，保证让你的产品脱颖而出！\",\n    price: 1999,\n    image: \"https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=400&fit=crop\",\n    rating: 4.9,\n    reviews: 667,\n    category: \"设计\",\n    tags: [\"UI设计\", \"交互\"],\n    creator: {\n      name: \"UI设计师小美\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616c45e1f94?w=100&h=100&fit=crop&crop=faces\",\n      verified: true,\n      location: \"杭州\"\n    },\n    favoriteCount: 523,\n    specialBadge: \"upro\"\n  }\n];\n\nconst filterTags = [\n  { id: \"bestseller\", label: \"热销榜\", icon: TrendingUp },\n  { id: \"new\", label: \"新品\", icon: Sparkles },\n  { id: \"discount\", label: \"特惠\", icon: Zap },\n  { id: \"professional\", label: \"专业级\", icon: Award }\n];\n\ninterface ServicesPageProps {\n  activeCategory?: string;\n  activeSubCategory?: string | null;\n  onCategoryChange?: (category: string) => void;\n  onSubCategoryChange?: (subCategory: string | null) => void;\n  onViewProductDetail?: (productId: string) => void;\n}\n\nexport function ServicesPageContent({\n  activeCategory = \"popular\",\n  activeSubCategory,\n  onCategoryChange,\n  onSubCategoryChange,\n  onViewProductDetail\n}: ServicesPageProps) {\n  const [selectedTags, setSelectedTags] = useState<string[]>([]);\n  const [viewMode, setViewMode] = useState<\"grid\" | \"list\">(\"grid\");\n  const [sortBy, setSortBy] = useState(\"default\");\n  const [favorites, setFavorites] = useState<string[]>([]);\n\n  // 筛选状态\n  const [filters, setFilters] = useState({\n    timeRange: \"all\",\n    priceRange: \"all\",\n    rating: \"all\",\n    serviceType: \"all\"\n  });\n\n  // 快速筛选状态\n  const [quickFilters, setQuickFilters] = useState({\n    uproServices: false,\n    onlineOnly: false\n  });\n\n  // 筛选产品\n  const filteredProducts = mockProducts.filter(product => {\n    let matchesCategory = true;\n\n    // 根据分类筛选\n    switch (activeCategory) {\n      case \"popular\":\n        matchesCategory = !!product.isBestseller || !!product.isNew;\n        break;\n      case \"design\":\n        matchesCategory = product.category === \"设计\" || product.category === \"3D\";\n        break;\n      case \"programming\":\n        matchesCategory = product.category === \"技术\";\n        break;\n      case \"marketing\":\n        matchesCategory = product.category === \"营销\";\n        break;\n      case \"video\":\n        matchesCategory = product.category === \"视频\";\n        break;\n      case \"writing\":\n        matchesCategory = product.category === \"写作\";\n        break;\n      case \"music\":\n        matchesCategory = product.category === \"音频\";\n        break;\n      case \"business\":\n        matchesCategory = product.tags.includes(\"专业\") || product.tags.includes(\"数据\");\n        break;\n      default:\n        matchesCategory = true;\n    }\n\n    let matchesTags = true;\n    if (selectedTags.length > 0) {\n      matchesTags = selectedTags.every(tag => {\n        switch (tag) {\n          case \"bestseller\":\n            return !!product.isBestseller;\n          case \"new\":\n            return !!product.isNew;\n          case \"discount\":\n            return !!product.discount;\n          case \"professional\":\n            return product.tags.includes(\"专业\");\n          default:\n            return true;\n        }\n      });\n    }\n\n    // 快速筛选逻辑\n    let matchesQuickFilters = true;\n\n    if (quickFilters.uproServices) {\n      matchesQuickFilters = matchesQuickFilters && product.specialBadge === \"upro\";\n    }\n\n    if (quickFilters.onlineOnly) {\n      matchesQuickFilters = matchesQuickFilters && !!product.creator.verified;\n    }\n\n    return matchesCategory && matchesTags && matchesQuickFilters;\n  });\n\n  // 排序产品\n  const sortedProducts = [...filteredProducts].sort((a, b) => {\n    switch (sortBy) {\n      case \"price-low\":\n        return a.price - b.price;\n      case \"price-high\":\n        return b.price - a.price;\n      case \"rating\":\n        return b.rating - a.rating;\n      case \"reviews\":\n        return b.reviews - a.reviews;\n      default:\n        return 0;\n    }\n  });\n\n  const toggleTag = (tagId: string) => {\n    setSelectedTags(prev =>\n      prev.includes(tagId)\n        ? prev.filter(t => t !== tagId)\n        : [...prev, tagId]\n    );\n  };\n\n  const toggleFavorite = (productId: string) => {\n    setFavorites(prev =>\n      prev.includes(productId)\n        ? prev.filter(id => id !== productId)\n        : [...prev, productId]\n    );\n  };\n\n  // 获取分类名称\n  const getCategoryName = (category: string) => {\n    switch (category) {\n      case \"popular\": return \"热门项目\";\n      case \"design\": return \"图像与设计\";\n      case \"programming\": return \"编程与技术\";\n      case \"marketing\": return \"数字营销\";\n      case \"video\": return \"视频动画摄影\";\n      case \"writing\": return \"写作&翻译\";\n      case \"music\": return \"音乐与音频\";\n      case \"business\": return \"商业\";\n      case \"finance\": return \"财务\";\n      case \"legal\": return \"法律\";\n      case \"academic\": return \"学业\";\n      default: return \"服务\";\n    }\n  };\n\n  // 获取分类描述\n  const getCategoryDescription = (category: string) => {\n    switch (category) {\n      case \"popular\": return \"发现最受欢迎的服务项目\";\n      case \"design\": return \"创意设计和视觉表达服务\";\n      case \"programming\": return \"专业技术开发解决方案\";\n      case \"marketing\": return \"数字化营销推广服务\";\n      case \"video\": return \"专业影像制作服务\";\n      case \"writing\": return \"文字创作和多语言服务\";\n      case \"music\": return \"音频制作和音乐创作\";\n      case \"business\": return \"商业咨询和管理服务\";\n      case \"finance\": return \"财务分析和投资顾问\";\n      case \"legal\": return \"法律咨询和文书服务\";\n      case \"academic\": return \"学术辅导和教育服务\";\n      default: return \"发现专业服务\";\n    }\n  };\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 py-6\">\n      {/* 面包屑导航 */}\n      <div className=\"mb-4\">\n        <Breadcrumb>\n          <BreadcrumbList>\n            <BreadcrumbItem>\n              <BreadcrumbLink\n                href=\"#\"\n                onClick={(e) => {\n                  e.preventDefault();\n                  onCategoryChange?.(\"popular\");\n                  onSubCategoryChange?.(null);\n                }}\n                className=\"flex items-center gap-1 text-slate-500 hover:text-slate-700 transition-colors cursor-pointer\"\n              >\n                <Home className=\"h-4 w-4\" />\n                首页\n              </BreadcrumbLink>\n            </BreadcrumbItem>\n            <BreadcrumbSeparator />\n            {activeSubCategory ? (\n              <>\n                <BreadcrumbItem>\n                  <BreadcrumbLink\n                    href=\"#\"\n                    onClick={(e) => {\n                      e.preventDefault();\n                      onSubCategoryChange?.(null);\n                    }}\n                    className=\"text-slate-500 hover:text-slate-700 transition-colors cursor-pointer\"\n                  >\n                    {getCategoryName(activeCategory)}\n                  </BreadcrumbLink>\n                </BreadcrumbItem>\n                <BreadcrumbSeparator />\n                <BreadcrumbItem>\n                  <BreadcrumbPage className=\"text-slate-900\">\n                    {activeSubCategory}\n                  </BreadcrumbPage>\n                </BreadcrumbItem>\n              </>\n            ) : (\n              <BreadcrumbItem>\n                <BreadcrumbPage className=\"text-slate-900\">\n                  {getCategoryName(activeCategory)}\n                </BreadcrumbPage>\n              </BreadcrumbItem>\n            )}\n          </BreadcrumbList>\n        </Breadcrumb>\n      </div>\n\n      {/* 页面标题 */}\n      <div className=\"mb-6 flex items-start justify-between\">\n        <div>\n          <h1 className=\"text-slate-900 mb-1\">\n            {activeSubCategory || getCategoryName(activeCategory)}\n          </h1>\n          <p className=\"text-slate-600\">\n            {activeSubCategory\n              ? `${getCategoryName(activeCategory)} - ${activeSubCategory} 相关服务`\n              : getCategoryDescription(activeCategory)\n            }\n          </p>\n        </div>\n\n        {/* 右侧咨询按钮 - 只在热门项目显示 */}\n        {activeCategory === \"popular\" && (\n          <div className=\"flex flex-col gap-2\">\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"text-blue-600 hover:text-blue-700 hover:bg-blue-50/60 px-3 py-2 h-auto justify-start text-sm font-normal\"\n            >\n              <User className=\"h-4 w-4 mr-2\" />\n              是否需要定制化选购？\n            </Button>\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"text-purple-600 hover:text-purple-700 hover:bg-purple-50/60 px-3 py-2 h-auto justify-start text-sm font-normal\"\n            >\n              <Bot className=\"h-4 w-4 mr-2\" />\n              可以问问AI我先需要准备哪些\n            </Button>\n          </div>\n        )}\n      </div>\n\n      {/* 顶部筛选区域 */}\n      <div className=\"bg-white/70 backdrop-blur-xl border border-slate-200/60 rounded-2xl p-6 mb-6\">\n        {/* 主要筛选区域 */}\n        <div className=\"flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between mb-6\">\n          {/* 控制区域 */}\n          <div className=\"flex items-center gap-3 w-full justify-end\">\n            {/* 排序方式 */}\n            <div className=\"relative\">\n              <select\n                value={sortBy}\n                onChange={(e) => setSortBy(e.target.value)}\n                className=\"appearance-none bg-slate-50/80 border border-slate-200/60 rounded-lg px-3 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/20\"\n              >\n                <option value=\"default\">排序方式</option>\n                <option value=\"price-low\">价格从低到高</option>\n                <option value=\"price-high\">价格从高到低</option>\n                <option value=\"rating\">评分最高</option>\n                <option value=\"reviews\">评价最多</option>\n              </select>\n              <ChevronDown className=\"absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400 pointer-events-none\" />\n            </div>\n\n            {/* 视图切换 */}\n            <div className=\"flex items-center bg-slate-100/80 rounded-lg p-1\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setViewMode(\"grid\")}\n                className={`p-1 h-8 w-8 ${viewMode === \"grid\" ? 'bg-white shadow-sm' : ''}`}\n              >\n                <Grid className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setViewMode(\"list\")}\n                className={`p-1 h-8 w-8 ${viewMode === \"list\" ? 'bg-white shadow-sm' : ''}`}\n              >\n                <List className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* 特性筛选标签 */}\n        <div className=\"flex flex-wrap gap-2\">\n          {filterTags.map(tag => {\n            const Icon = tag.icon;\n            const isSelected = selectedTags.includes(tag.id);\n            return (\n              <Button\n                key={tag.id}\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => toggleTag(tag.id)}\n                className={`rounded-full px-3 py-2 flex items-center gap-2 transition-all duration-200 ${\n                  isSelected\n                    ? 'bg-gradient-to-r from-blue-50/80 to-indigo-50/80 text-blue-700 border-blue-200/60 shadow-sm'\n                    : 'bg-slate-50/80 text-slate-600 hover:bg-slate-100/80 border-slate-200/60'\n                } border`}\n              >\n                <Icon className=\"h-3 w-3\" />\n                {tag.label}\n              </Button>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* 结果统计 */}\n      <div className=\"flex items-center justify-between mb-6\">\n        <div className=\"flex items-center\">\n          <span className=\"text-slate-500 text-sm\">\n            共 {sortedProducts.length} 个结果\n          </span>\n        </div>\n        <div className=\"flex items-center gap-4\">\n          {/* 快速筛选切换按钮 */}\n          <div className=\"flex items-center gap-3\">\n            {/* Upro服务切换 */}\n            <div className=\"flex items-center gap-2\">\n              <button\n                onClick={() => setQuickFilters(prev => ({ ...prev, uproServices: !prev.uproServices }))}\n                className={`relative inline-flex items-center w-11 h-6 rounded-full transition-colors duration-200 ${\n                  quickFilters.uproServices\n                    ? 'bg-blue-600'\n                    : 'bg-slate-300'\n                }`}\n              >\n                <span\n                  className={`inline-block w-4 h-4 bg-white rounded-full transition-transform duration-200 ${\n                    quickFilters.uproServices ? 'translate-x-6' : 'translate-x-1'\n                  }`}\n                />\n              </button>\n              <span className=\"text-sm text-slate-600\">\n                专业服务\n              </span>\n            </div>\n\n            {/* 在线状态切换 */}\n            <div className=\"flex items-center gap-2\">\n              <button\n                onClick={() => setQuickFilters(prev => ({ ...prev, onlineOnly: !prev.onlineOnly }))}\n                className={`relative inline-flex items-center w-11 h-6 rounded-full transition-colors duration-200 ${\n                  quickFilters.onlineOnly\n                    ? 'bg-blue-600'\n                    : 'bg-slate-300'\n                }`}\n              >\n                <span\n                  className={`inline-block w-4 h-4 bg-white rounded-full transition-transform duration-200 ${\n                    quickFilters.onlineOnly ? 'translate-x-6' : 'translate-x-1'\n                  }`}\n                />\n              </button>\n              <span className=\"text-sm text-slate-600\">\n                即时响应\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* 产品网格 - 统一使用4列布局 */}\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n        {sortedProducts.map((product) => {\n          const isFavorited = favorites.includes(product.id);\n          return (\n            <Card\n              key={product.id}\n              className=\"group bg-white/90 backdrop-blur-sm border-slate-200/60 hover:shadow-xl hover:shadow-slate-200/40 transition-all duration-300 cursor-pointer overflow-hidden relative\"\n            >\n              {/* 星级评分 - 右上角 */}\n              <div className=\"absolute top-3 right-3 z-10 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full flex items-center gap-1\">\n                <Star className=\"h-3 w-3 text-yellow-500 fill-current\" />\n                <span className=\"text-xs font-medium text-slate-700\">{product.rating}</span>\n              </div>\n\n              {/* 商品图片 */}\n              <div className=\"relative aspect-[4/3] bg-gradient-to-br from-slate-50 to-slate-100/50 overflow-hidden\">\n                <img\n                  src={product.image}\n                  alt={product.name}\n                  className=\"w-full h-full object-cover group-hover:scale-105 transition-transform duration-300\"\n                />\n\n                {/* 商品标签 - 图片左下角 */}\n                {product.specialBadge && (\n                  <div className=\"absolute bottom-2 left-2 z-10\">\n                    {product.specialBadge === \"upro\" && (\n                      <Badge className=\"bg-purple-600 text-white border-0 text-xs px-2 py-0.5\">\n                        U PRO\n                      </Badge>\n                    )}\n                    {product.specialBadge === \"top\" && (\n                      <Badge className=\"bg-amber-600 text-white border-0 text-xs px-2 py-0.5\">\n                        好评Top\n                      </Badge>\n                    )}\n                    {product.specialBadge === \"star\" && (\n                      <Badge className=\"bg-blue-600 text-white border-0 text-xs px-2 py-0.5\">\n                        技能之星\n                      </Badge>\n                    )}\n                  </div>\n                )}\n              </div>\n\n              <CardContent className=\"px-4 pt-2 pb-4\">\n                {/* 创作者信息和评价 */}\n                <div className=\"flex items-center justify-between mb-3\">\n                  <div className=\"flex items-center gap-2 flex-1 min-w-0\">\n                    <div className=\"relative\">\n                      <img\n                        src={product.creator.avatar}\n                        alt={product.creator.name}\n                        className=\"w-6 h-6 rounded-full object-cover border border-slate-200\"\n                      />\n                      {product.creator.verified && (\n                        <div className=\"absolute -top-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"></div>\n                      )}\n                    </div>\n                    <span className=\"text-sm text-slate-600 truncate\">{product.creator.name}</span>\n                    {product.creator.verified && (\n                      <span className=\"text-xs text-blue-600 bg-blue-50 px-1.5 py-0.5 rounded\">认证</span>\n                    )}\n                  </div>\n                  <div className=\"flex items-center gap-1 flex-shrink-0\">\n                    <span className=\"text-xs text-slate-500\">\n                      ({product.reviews} 评价)\n                    </span>\n                  </div>\n                </div>\n\n                {/* 商品名称 */}\n                <h3 className=\"text-slate-900 mb-1 group-hover:text-blue-700 transition-colors duration-200 line-clamp-1\">\n                  {product.name}\n                </h3>\n\n                {/* 商品描述 */}\n                <p className=\"text-slate-600 text-sm mb-4 line-clamp-2 min-h-[2.5rem]\">\n                  {product.description}\n                </p>\n\n                {/* 底部区域 */}\n                <div className=\"flex items-end justify-between\">\n                  {/* 左下角 - 收藏按钮和数量 */}\n                  <div className=\"flex items-center gap-2\">\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={(e) => {\n                        e.stopPropagation();\n                        toggleFavorite(product.id);\n                      }}\n                      className={`h-8 w-8 p-0 rounded-full transition-all duration-200 ${\n                        isFavorited\n                          ? 'bg-red-50/90 text-red-500 hover:bg-red-100/90'\n                          : 'bg-slate-50/80 text-slate-400 hover:bg-slate-100/90 hover:text-red-500'\n                      }`}\n                    >\n                      <Heart className={`h-4 w-4 ${isFavorited ? 'fill-current' : ''}`} />\n                    </Button>\n                    <span className=\"text-xs text-slate-500\">\n                      {product.favoriteCount}\n                    </span>\n                  </div>\n\n                  {/* 右下角 - 价格 */}\n                  <div className=\"text-right\">\n                    <div className=\"flex items-center gap-1 justify-end\">\n                      <span className=\"text-slate-700 font-medium\">RMB</span>\n                      <span className=\"text-slate-900 font-medium\">\n                        {product.price}\n                      </span>\n                      <span className=\"text-slate-500 font-medium\">起</span>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          );\n        })}\n      </div>\n\n      {/* 空状态 */}\n      {sortedProducts.length === 0 && (\n        <div className=\"text-center py-12\">\n          <div className=\"relative inline-block mb-4\">\n            <div className=\"absolute inset-0 bg-slate-200/50 rounded-full blur-xl\"></div>\n            <Search className=\"relative h-12 w-12 text-slate-400 mx-auto\" />\n          </div>\n          <h3 className=\"text-slate-900 mb-2\">没有找到相关服务</h3>\n          <p className=\"text-slate-600\">请尝试调整搜索条件或筛选选项</p>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAEA;AAiBA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;AA6CA,MAAM,eAA0B;IAC9B;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,eAAe;QACf,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM;YAAC;YAAM;SAAK;QAClB,UAAU;QACV,cAAc;QACd,OAAO;QACP,SAAS;YACP,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,eAAe;QACf,cAAc;IAChB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM;YAAC;YAAM;SAAK;QAClB,cAAc;QACd,SAAS;YACP,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,eAAe;QACf,cAAc;IAChB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM;YAAC;YAAO;SAAK;QACnB,SAAS;YACP,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,eAAe;QACf,cAAc;IAChB;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;QACP,QAAQ;QACR,SAAS;QACT,UAAU;QACV,MAAM;YAAC;YAAQ;SAAK;QACpB,SAAS;YACP,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,eAAe;QACf,cAAc;IAChB;CACD;AAED,MAAM,aAAa;IACjB;QAAE,IAAI;QAAc,OAAO;QAAO,MAAM,sSAAA,CAAA,aAAU;IAAC;IACnD;QAAE,IAAI;QAAO,OAAO;QAAM,MAAM,8RAAA,CAAA,WAAQ;IAAC;IACzC;QAAE,IAAI;QAAY,OAAO;QAAM,MAAM,oRAAA,CAAA,MAAG;IAAC;IACzC;QAAE,IAAI;QAAgB,OAAO;QAAO,MAAM,wRAAA,CAAA,QAAK;IAAC;CACjD;AAUM,SAAS,oBAAoB,EAClC,iBAAiB,SAAS,EAC1B,iBAAiB,EACjB,gBAAgB,EAChB,mBAAmB,EACnB,mBAAmB,EACD;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEvD,OAAO;IACP,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;QACrC,WAAW;QACX,YAAY;QACZ,QAAQ;QACR,aAAa;IACf;IAEA,SAAS;IACT,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,cAAc;QACd,YAAY;IACd;IAEA,OAAO;IACP,MAAM,mBAAmB,aAAa,MAAM,CAAC,CAAA;QAC3C,IAAI,kBAAkB;QAEtB,SAAS;QACT,OAAQ;YACN,KAAK;gBACH,kBAAkB,CAAC,CAAC,QAAQ,YAAY,IAAI,CAAC,CAAC,QAAQ,KAAK;gBAC3D;YACF,KAAK;gBACH,kBAAkB,QAAQ,QAAQ,KAAK,QAAQ,QAAQ,QAAQ,KAAK;gBACpE;YACF,KAAK;gBACH,kBAAkB,QAAQ,QAAQ,KAAK;gBACvC;YACF,KAAK;gBACH,kBAAkB,QAAQ,QAAQ,KAAK;gBACvC;YACF,KAAK;gBACH,kBAAkB,QAAQ,QAAQ,KAAK;gBACvC;YACF,KAAK;gBACH,kBAAkB,QAAQ,QAAQ,KAAK;gBACvC;YACF,KAAK;gBACH,kBAAkB,QAAQ,QAAQ,KAAK;gBACvC;YACF,KAAK;gBACH,kBAAkB,QAAQ,IAAI,CAAC,QAAQ,CAAC,SAAS,QAAQ,IAAI,CAAC,QAAQ,CAAC;gBACvE;YACF;gBACE,kBAAkB;QACtB;QAEA,IAAI,cAAc;QAClB,IAAI,aAAa,MAAM,GAAG,GAAG;YAC3B,cAAc,aAAa,KAAK,CAAC,CAAA;gBAC/B,OAAQ;oBACN,KAAK;wBACH,OAAO,CAAC,CAAC,QAAQ,YAAY;oBAC/B,KAAK;wBACH,OAAO,CAAC,CAAC,QAAQ,KAAK;oBACxB,KAAK;wBACH,OAAO,CAAC,CAAC,QAAQ,QAAQ;oBAC3B,KAAK;wBACH,OAAO,QAAQ,IAAI,CAAC,QAAQ,CAAC;oBAC/B;wBACE,OAAO;gBACX;YACF;QACF;QAEA,SAAS;QACT,IAAI,sBAAsB;QAE1B,IAAI,aAAa,YAAY,EAAE;YAC7B,sBAAsB,uBAAuB,QAAQ,YAAY,KAAK;QACxE;QAEA,IAAI,aAAa,UAAU,EAAE;YAC3B,sBAAsB,uBAAuB,CAAC,CAAC,QAAQ,OAAO,CAAC,QAAQ;QACzE;QAEA,OAAO,mBAAmB,eAAe;IAC3C;IAEA,OAAO;IACP,MAAM,iBAAiB;WAAI;KAAiB,CAAC,IAAI,CAAC,CAAC,GAAG;QACpD,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;YAC1B,KAAK;gBACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;YAC1B,KAAK;gBACH,OAAO,EAAE,MAAM,GAAG,EAAE,MAAM;YAC5B,KAAK;gBACH,OAAO,EAAE,OAAO,GAAG,EAAE,OAAO;YAC9B;gBACE,OAAO;QACX;IACF;IAEA,MAAM,YAAY,CAAC;QACjB,gBAAgB,CAAA,OACd,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,SACvB;mBAAI;gBAAM;aAAM;IAExB;IAEA,MAAM,iBAAiB,CAAC;QACtB,aAAa,CAAA,OACX,KAAK,QAAQ,CAAC,aACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,aACzB;mBAAI;gBAAM;aAAU;IAE5B;IAEA,SAAS;IACT,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,SAAS;IACT,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;0BACb,cAAA,6WAAC,sIAAA,CAAA,aAAU;8BACT,cAAA,6WAAC,sIAAA,CAAA,iBAAc;;0CACb,6WAAC,sIAAA,CAAA,iBAAc;0CACb,cAAA,6WAAC,sIAAA,CAAA,iBAAc;oCACb,MAAK;oCACL,SAAS,CAAC;wCACR,EAAE,cAAc;wCAChB,mBAAmB;wCACnB,sBAAsB;oCACxB;oCACA,WAAU;;sDAEV,6WAAC,uRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIhC,6WAAC,sIAAA,CAAA,sBAAmB;;;;;4BACnB,kCACC;;kDACE,6WAAC,sIAAA,CAAA,iBAAc;kDACb,cAAA,6WAAC,sIAAA,CAAA,iBAAc;4CACb,MAAK;4CACL,SAAS,CAAC;gDACR,EAAE,cAAc;gDAChB,sBAAsB;4CACxB;4CACA,WAAU;sDAET,gBAAgB;;;;;;;;;;;kDAGrB,6WAAC,sIAAA,CAAA,sBAAmB;;;;;kDACpB,6WAAC,sIAAA,CAAA,iBAAc;kDACb,cAAA,6WAAC,sIAAA,CAAA,iBAAc;4CAAC,WAAU;sDACvB;;;;;;;;;;;;6DAKP,6WAAC,sIAAA,CAAA,iBAAc;0CACb,cAAA,6WAAC,sIAAA,CAAA,iBAAc;oCAAC,WAAU;8CACvB,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7B,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;;0CACC,6WAAC;gCAAG,WAAU;0CACX,qBAAqB,gBAAgB;;;;;;0CAExC,6WAAC;gCAAE,WAAU;0CACV,oBACG,GAAG,gBAAgB,gBAAgB,GAAG,EAAE,kBAAkB,KAAK,CAAC,GAChE,uBAAuB;;;;;;;;;;;;oBAM9B,mBAAmB,2BAClB,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;;kDAEV,6WAAC,sRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,6WAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;;kDAEV,6WAAC,oRAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;0BAQxC,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;kCAEb,cAAA,6WAAC;4BAAI,WAAU;;8CAEb,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;4CACzC,WAAU;;8DAEV,6WAAC;oDAAO,OAAM;8DAAU;;;;;;8DACxB,6WAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,6WAAC;oDAAO,OAAM;8DAAa;;;;;;8DAC3B,6WAAC;oDAAO,OAAM;8DAAS;;;;;;8DACvB,6WAAC;oDAAO,OAAM;8DAAU;;;;;;;;;;;;sDAE1B,6WAAC,wSAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAIzB,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,YAAY;4CAC3B,WAAW,CAAC,YAAY,EAAE,aAAa,SAAS,uBAAuB,IAAI;sDAE3E,cAAA,6WAAC,6RAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6WAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS,IAAM,YAAY;4CAC3B,WAAW,CAAC,YAAY,EAAE,aAAa,SAAS,uBAAuB,IAAI;sDAE3E,cAAA,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOxB,6WAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAA;4BACd,MAAM,OAAO,IAAI,IAAI;4BACrB,MAAM,aAAa,aAAa,QAAQ,CAAC,IAAI,EAAE;4BAC/C,qBACE,6WAAC,kIAAA,CAAA,SAAM;gCAEL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,UAAU,IAAI,EAAE;gCAC/B,WAAW,CAAC,2EAA2E,EACrF,aACI,gGACA,0EACL,OAAO,CAAC;;kDAET,6WAAC;wCAAK,WAAU;;;;;;oCACf,IAAI,KAAK;;+BAXL,IAAI,EAAE;;;;;wBAcjB;;;;;;;;;;;;0BAKJ,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAK,WAAU;;gCAAyB;gCACpC,eAAe,MAAM;gCAAC;;;;;;;;;;;;kCAG7B,6WAAC;wBAAI,WAAU;kCAEb,cAAA,6WAAC;4BAAI,WAAU;;8CAEb,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CACC,SAAS,IAAM,gBAAgB,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,cAAc,CAAC,KAAK,YAAY;oDAAC,CAAC;4CACrF,WAAW,CAAC,uFAAuF,EACjG,aAAa,YAAY,GACrB,gBACA,gBACJ;sDAEF,cAAA,6WAAC;gDACC,WAAW,CAAC,6EAA6E,EACvF,aAAa,YAAY,GAAG,kBAAkB,iBAC9C;;;;;;;;;;;sDAGN,6WAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;8CAM3C,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CACC,SAAS,IAAM,gBAAgB,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,YAAY,CAAC,KAAK,UAAU;oDAAC,CAAC;4CACjF,WAAW,CAAC,uFAAuF,EACjG,aAAa,UAAU,GACnB,gBACA,gBACJ;sDAEF,cAAA,6WAAC;gDACC,WAAW,CAAC,6EAA6E,EACvF,aAAa,UAAU,GAAG,kBAAkB,iBAC5C;;;;;;;;;;;sDAGN,6WAAC;4CAAK,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASjD,6WAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC;oBACnB,MAAM,cAAc,UAAU,QAAQ,CAAC,QAAQ,EAAE;oBACjD,qBACE,6WAAC,gIAAA,CAAA,OAAI;wBAEH,WAAU;;0CAGV,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,sRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6WAAC;wCAAK,WAAU;kDAAsC,QAAQ,MAAM;;;;;;;;;;;;0CAItE,6WAAC;gCAAI,WAAU;;kDACb,6WAAC;wCACC,KAAK,QAAQ,KAAK;wCAClB,KAAK,QAAQ,IAAI;wCACjB,WAAU;;;;;;oCAIX,QAAQ,YAAY,kBACnB,6WAAC;wCAAI,WAAU;;4CACZ,QAAQ,YAAY,KAAK,wBACxB,6WAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAwD;;;;;;4CAI1E,QAAQ,YAAY,KAAK,uBACxB,6WAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAuD;;;;;;4CAIzE,QAAQ,YAAY,KAAK,wBACxB,6WAAC,iIAAA,CAAA,QAAK;gDAAC,WAAU;0DAAsD;;;;;;;;;;;;;;;;;;0CAQ/E,6WAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDAErB,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC;wDAAI,WAAU;;0EACb,6WAAC;gEACC,KAAK,QAAQ,OAAO,CAAC,MAAM;gEAC3B,KAAK,QAAQ,OAAO,CAAC,IAAI;gEACzB,WAAU;;;;;;4DAEX,QAAQ,OAAO,CAAC,QAAQ,kBACvB,6WAAC;gEAAI,WAAU;;;;;;;;;;;;kEAGnB,6WAAC;wDAAK,WAAU;kEAAmC,QAAQ,OAAO,CAAC,IAAI;;;;;;oDACtE,QAAQ,OAAO,CAAC,QAAQ,kBACvB,6WAAC;wDAAK,WAAU;kEAAyD;;;;;;;;;;;;0DAG7E,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAK,WAAU;;wDAAyB;wDACrC,QAAQ,OAAO;wDAAC;;;;;;;;;;;;;;;;;;kDAMxB,6WAAC;wCAAG,WAAU;kDACX,QAAQ,IAAI;;;;;;kDAIf,6WAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;kDAItB,6WAAC;wCAAI,WAAU;;0DAEb,6WAAC;gDAAI,WAAU;;kEACb,6WAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,eAAe,QAAQ,EAAE;wDAC3B;wDACA,WAAW,CAAC,qDAAqD,EAC/D,cACI,kDACA,0EACJ;kEAEF,cAAA,6WAAC,wRAAA,CAAA,QAAK;4DAAC,WAAW,CAAC,QAAQ,EAAE,cAAc,iBAAiB,IAAI;;;;;;;;;;;kEAElE,6WAAC;wDAAK,WAAU;kEACb,QAAQ,aAAa;;;;;;;;;;;;0DAK1B,6WAAC;gDAAI,WAAU;0DACb,cAAA,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAK,WAAU;sEAA6B;;;;;;sEAC7C,6WAAC;4DAAK,WAAU;sEACb,QAAQ,KAAK;;;;;;sEAEhB,6WAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA1GhD,QAAQ,EAAE;;;;;gBAiHrB;;;;;;YAID,eAAe,MAAM,KAAK,mBACzB,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC;gCAAI,WAAU;;;;;;0CACf,6WAAC,0RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;;kCAEpB,6WAAC;wBAAG,WAAU;kCAAsB;;;;;;kCACpC,6WAAC;wBAAE,WAAU;kCAAiB;;;;;;;;;;;;;;;;;;AAKxC", "debugId": null}}, {"offset": {"line": 2852, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ChatWindow.tsx"], "sourcesContent": ["import { useState } from \"react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { \n  X, \n  Minus, \n  Send, \n  Bell,\n  MessageCircle,\n  User,\n  Bot\n} from \"lucide-react\";\n\ninterface ChatWindowProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onMinimize: () => void;\n}\n\ninterface Contact {\n  id: string;\n  name: string;\n  avatar: string;\n  isOnline: boolean;\n  lastMessage: string;\n  unreadCount: number;\n  type: \"user\" | \"official\";\n}\n\ninterface Message {\n  id: string;\n  content: string;\n  sender: \"me\" | \"other\";\n  time: string;\n  type: \"text\" | \"image\" | \"file\";\n}\n\nexport function ChatWindow({ isOpen, onClose, onMinimize }: ChatWindowProps) {\n  const [activeContact, setActiveContact] = useState(\"official\");\n  const [messageInput, setMessageInput] = useState(\"\");\n\n  // 模拟联系人数据\n  const contacts: Contact[] = [\n    {\n      id: \"official\",\n      name: \"官方客服\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: true,\n      lastMessage: \"有什么可以帮助您的吗？\",\n      unreadCount: 1,\n      type: \"official\"\n    },\n    {\n      id: \"merchant1\",\n      name: \"设计师小王\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: true,\n      lastMessage: \"好的，我马上开始设计\",\n      unreadCount: 2,\n      type: \"user\"\n    },\n    {\n      id: \"merchant2\",\n      name: \"程序员老李\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: false,\n      lastMessage: \"网站开发进度如何？\",\n      unreadCount: 0,\n      type: \"user\"\n    }\n  ];\n\n  // 模拟消息数据\n  const [messages] = useState<Record<string, Message[]>>({\n    official: [\n      {\n        id: \"1\",\n        content: \"您好！欢迎使用U-bund平台，有什么可以帮助您的吗？\",\n        sender: \"other\",\n        time: \"14:30\",\n        type: \"text\"\n      }\n    ],\n    merchant1: [\n      {\n        id: \"1\",\n        content: \"您好，关于Logo设计的需求我已经收到了\",\n        sender: \"other\",\n        time: \"10:15\",\n        type: \"text\"\n      },\n      {\n        id: \"2\",\n        content: \"好的，请问大概什么时候能完成？\",\n        sender: \"me\",\n        time: \"10:16\",\n        type: \"text\"\n      },\n      {\n        id: \"3\",\n        content: \"预计3-5个工作日，我会先做几个方案给您选择\",\n        sender: \"other\",\n        time: \"10:18\",\n        type: \"text\"\n      }\n    ],\n    merchant2: [\n      {\n        id: \"1\",\n        content: \"网站开发项目已经开始了，预计下周完成\",\n        sender: \"other\",\n        time: \"昨天\",\n        type: \"text\"\n      }\n    ]\n  });\n\n  const currentContact = contacts.find(c => c.id === activeContact);\n  const currentMessages = messages[activeContact] || [];\n\n  const handleSendMessage = () => {\n    if (messageInput.trim()) {\n      console.log(\"发送消息:\", messageInput);\n      setMessageInput(\"\");\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed bottom-4 right-4 w-[640px] h-[768px] bg-white border border-slate-200 rounded-lg shadow-2xl z-[100] flex flex-col overflow-hidden\">\n      {/* 顶部标题栏 */}\n      <div className=\"bg-slate-50/80 border-b border-slate-200 px-6 py-4 flex items-center justify-between\">\n        <div className=\"flex items-center gap-3\">\n          <Bell className=\"h-5 w-5 text-slate-600\" />\n          <span className=\"text-slate-900 font-medium\">消息通知</span>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={onMinimize}\n            className=\"h-8 w-8 p-0 hover:bg-slate-200/60\"\n          >\n            <Minus className=\"h-4 w-4\" />\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={onClose}\n            className=\"h-8 w-8 p-0 hover:bg-slate-200/60\"\n          >\n            <X className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"flex flex-1 overflow-hidden\">\n        {/* 左侧联系人列表 */}\n        <div className=\"w-64 border-r border-slate-200 bg-slate-50/30\">\n          <div className=\"p-4\">\n            <h3 className=\"text-sm font-medium text-slate-900 mb-3\">最近联系</h3>\n            <div className=\"space-y-2\">\n              {contacts.map((contact) => (\n                <div\n                  key={contact.id}\n                  onClick={() => setActiveContact(contact.id)}\n                  className={`flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors ${\n                    activeContact === contact.id\n                      ? 'bg-blue-50 border border-blue-200'\n                      : 'hover:bg-slate-100/60'\n                  }`}\n                >\n                  <div className=\"relative\">\n                    <Avatar className=\"h-10 w-10\">\n                      <AvatarImage src={contact.avatar} />\n                      <AvatarFallback>\n                        {contact.type === \"official\" ? <Bot className=\"h-5 w-5\" /> : <User className=\"h-5 w-5\" />}\n                      </AvatarFallback>\n                    </Avatar>\n                    <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ${\n                      contact.isOnline ? 'bg-green-500' : 'bg-slate-400'\n                    }`}></div>\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center justify-between mb-1\">\n                      <span className=\"text-sm font-medium text-slate-900 truncate\">\n                        {contact.name}\n                      </span>\n                      {contact.unreadCount > 0 && (\n                        <Badge className=\"h-5 w-5 p-0 flex items-center justify-center text-xs bg-red-500\">\n                          {contact.unreadCount}\n                        </Badge>\n                      )}\n                    </div>\n                    <p className=\"text-xs text-slate-600 truncate\">\n                      {contact.lastMessage}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* 右侧聊天区域 */}\n        <div className=\"flex-1 flex flex-col\">\n          {/* 聊天头部 */}\n          {currentContact && (\n            <div className=\"border-b border-slate-200 p-4\">\n              <div className=\"flex items-center gap-3\">\n                <Avatar className=\"h-10 w-10\">\n                  <AvatarImage src={currentContact.avatar} />\n                  <AvatarFallback>\n                    {currentContact.type === \"official\" ? <Bot className=\"h-5 w-5\" /> : <User className=\"h-5 w-5\" />}\n                  </AvatarFallback>\n                </Avatar>\n                <div>\n                  <h4 className=\"font-medium text-slate-900\">{currentContact.name}</h4>\n                  <p className=\"text-sm text-slate-600\">\n                    {currentContact.isOnline ? '在线' : '离线'}\n                  </p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* 消息列表 */}\n          <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n            {currentMessages.map((message) => (\n              <div\n                key={message.id}\n                className={`flex ${message.sender === 'me' ? 'justify-end' : 'justify-start'}`}\n              >\n                <div\n                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${\n                    message.sender === 'me'\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-slate-100 text-slate-900'\n                  }`}\n                >\n                  <p className=\"text-sm\">{message.content}</p>\n                  <p className={`text-xs mt-1 ${\n                    message.sender === 'me' ? 'text-blue-100' : 'text-slate-500'\n                  }`}>\n                    {message.time}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* 输入框 */}\n          {currentContact?.type !== \"official\" && (\n            <div className=\"border-t border-slate-200 p-4\">\n              <div className=\"flex items-end gap-3\">\n                <div className=\"flex-1\">\n                  <textarea\n                    value={messageInput}\n                    onChange={(e) => setMessageInput(e.target.value)}\n                    onKeyPress={handleKeyPress}\n                    placeholder=\"输入消息...\"\n                    className=\"w-full px-3 py-2 border border-slate-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-300\"\n                    rows={1}\n                  />\n                </div>\n                <Button\n                  onClick={handleSendMessage}\n                  disabled={!messageInput.trim()}\n                  className=\"bg-blue-600 hover:bg-blue-700\"\n                >\n                  <Send className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;AAkCO,SAAS,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAmB;IACzE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,UAAU;IACV,MAAM,WAAsB;QAC1B;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,UAAU;YACV,aAAa;YACb,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,UAAU;YACV,aAAa;YACb,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YAC<PERSON>,MAAM;YACN,QAAQ;YACR,UAAU;YACV,aAAa;YACb,aAAa;YACb,MAAM;QACR;KACD;IAED,SAAS;IACT,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAA6B;QACrD,UAAU;YACR;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,MAAM;YACR;SACD;QACD,WAAW;YACT;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,MAAM;YACR;SACD;QACD,WAAW;YACT;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,MAAM;YACR;SACD;IACH;IAEA,MAAM,iBAAiB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACnD,MAAM,kBAAkB,QAAQ,CAAC,cAAc,IAAI,EAAE;IAErD,MAAM,oBAAoB;QACxB,IAAI,aAAa,IAAI,IAAI;YACvB,QAAQ,GAAG,CAAC,SAAS;YACrB,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,sRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6WAAC;gCAAK,WAAU;0CAA6B;;;;;;;;;;;;kCAE/C,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6WAAC,wRAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,6WAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6WAAC,gRAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAKnB,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAG,WAAU;8CAA0C;;;;;;8CACxD,6WAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6WAAC;4CAEC,SAAS,IAAM,iBAAiB,QAAQ,EAAE;4CAC1C,WAAW,CAAC,wEAAwE,EAClF,kBAAkB,QAAQ,EAAE,GACxB,sCACA,yBACJ;;8DAEF,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;;8EAChB,6WAAC,kIAAA,CAAA,cAAW;oEAAC,KAAK,QAAQ,MAAM;;;;;;8EAChC,6WAAC,kIAAA,CAAA,iBAAc;8EACZ,QAAQ,IAAI,KAAK,2BAAa,6WAAC,oRAAA,CAAA,MAAG;wEAAC,WAAU;;;;;6FAAe,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAGjF,6WAAC;4DAAI,WAAW,CAAC,2EAA2E,EAC1F,QAAQ,QAAQ,GAAG,iBAAiB,gBACpC;;;;;;;;;;;;8DAEJ,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEAAK,WAAU;8EACb,QAAQ,IAAI;;;;;;gEAEd,QAAQ,WAAW,GAAG,mBACrB,6WAAC,iIAAA,CAAA,QAAK;oEAAC,WAAU;8EACd,QAAQ,WAAW;;;;;;;;;;;;sEAI1B,6WAAC;4DAAE,WAAU;sEACV,QAAQ,WAAW;;;;;;;;;;;;;2CA/BnB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;kCAyCzB,6WAAC;wBAAI,WAAU;;4BAEZ,gCACC,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6WAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,eAAe,MAAM;;;;;;8DACvC,6WAAC,kIAAA,CAAA,iBAAc;8DACZ,eAAe,IAAI,KAAK,2BAAa,6WAAC,oRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;6EAAe,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGxF,6WAAC;;8DACC,6WAAC;oDAAG,WAAU;8DAA8B,eAAe,IAAI;;;;;;8DAC/D,6WAAC;oDAAE,WAAU;8DACV,eAAe,QAAQ,GAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;;0CAQ5C,6WAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,wBACpB,6WAAC;wCAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,MAAM,KAAK,OAAO,gBAAgB,iBAAiB;kDAE9E,cAAA,6WAAC;4CACC,WAAW,CAAC,0CAA0C,EACpD,QAAQ,MAAM,KAAK,OACf,2BACA,+BACJ;;8DAEF,6WAAC;oDAAE,WAAU;8DAAW,QAAQ,OAAO;;;;;;8DACvC,6WAAC;oDAAE,WAAW,CAAC,aAAa,EAC1B,QAAQ,MAAM,KAAK,OAAO,kBAAkB,kBAC5C;8DACC,QAAQ,IAAI;;;;;;;;;;;;uCAdZ,QAAQ,EAAE;;;;;;;;;;4BAsBpB,gBAAgB,SAAS,4BACxB,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,YAAY;gDACZ,aAAY;gDACZ,WAAU;gDACV,MAAM;;;;;;;;;;;sDAGV,6WAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,CAAC,aAAa,IAAI;4CAC5B,WAAU;sDAEV,cAAA,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlC", "debugId": null}}, {"offset": {"line": 3371, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ChatFloatingButton.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { MessageCircle } from \"lucide-react\";\n\ninterface ChatFloatingButtonProps {\n  onClick: () => void;\n  notificationCount?: number;\n  isVisible?: boolean;\n}\n\nexport function ChatFloatingButton({ \n  onClick, \n  notificationCount = 0, \n  isVisible = true \n}: ChatFloatingButtonProps) {\n  if (!isVisible) return null;\n\n  return (\n    <div className=\"fixed bottom-6 right-6 z-50\">\n      <Button\n        onClick={onClick}\n        size=\"lg\"\n        className=\"relative h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 group\"\n      >\n        <MessageCircle className=\"h-6 w-6 text-white group-hover:scale-110 transition-transform\" />\n        \n        {/* 通知数量徽章 */}\n        {notificationCount > 0 && (\n          <Badge className=\"absolute -top-2 -right-2 h-6 w-6 p-0 flex items-center justify-center text-xs bg-red-500 text-white border-2 border-white\">\n            {notificationCount > 99 ? '99+' : notificationCount}\n          </Badge>\n        )}\n        \n        {/* 脉冲动画 */}\n        <div className=\"absolute inset-0 rounded-full bg-blue-600 animate-ping opacity-20\"></div>\n      </Button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAQO,SAAS,mBAAmB,EACjC,OAAO,EACP,oBAAoB,CAAC,EACrB,YAAY,IAAI,EACQ;IACxB,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC,kIAAA,CAAA,SAAM;YACL,SAAS;YACT,MAAK;YACL,WAAU;;8BAEV,6WAAC,4SAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;gBAGxB,oBAAoB,mBACnB,6WAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BACd,oBAAoB,KAAK,QAAQ;;;;;;8BAKtC,6WAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 3453, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { NavigationV80 } from \"@/components/NavigationV80\";\nimport { CategoryNavigation } from \"@/components/CategoryNavigation\";\nimport { LandingPage } from \"@/components/LandingPage\";\nimport { ServicesPageContent } from \"@/components/ServicesPage\";\nimport { ChatWindow } from \"@/components/ChatWindow\";\nimport { ChatFloatingButton } from \"@/components/ChatFloatingButton\";\nimport { useRouter } from \"next/navigation\";\n\nexport default function Home() {\n  const router = useRouter();\n  const [activeCategory, setActiveCategory] = useState(\"popular\");\n  const [isChatOpen, setIsChatOpen] = useState(false);\n  const [notificationCount] = useState(3);\n\n  const handleNavigate = (page: string) => {\n    switch (page) {\n      case \"home\":\n      case \"landing\":\n        router.push(\"/\");\n        break;\n      case \"services\":\n        router.push(\"/services\");\n        break;\n      case \"orders\":\n        router.push(\"/orders\");\n        break;\n      case \"favorites\":\n        router.push(\"/favorites\");\n        break;\n      case \"support\":\n        router.push(\"/support\");\n        break;\n      case \"gallery\":\n        router.push(\"/gallery\");\n        break;\n      case \"login\":\n        router.push(\"/login\");\n        break;\n      case \"register\":\n        router.push(\"/register\");\n        break;\n      default:\n        break;\n    }\n  };\n\n  const handleNavigateToServices = () => {\n    router.push(\"/services\");\n  };\n\n  const handleSubCategoryClick = (category: string, subCategory: string) => {\n    setActiveCategory(category);\n    // 点击子分类后跳转到服务页面\n    router.push(`/services?category=${category}&sub=${subCategory}`);\n  };\n\n  const handleCategoryChange = (category: string) => {\n    setActiveCategory(category);\n    console.log(`已切换到分类: ${category}`);\n  };\n\n  const handleLoginClick = () => {\n    router.push(\"/login\");\n  };\n\n  const handleOpenChat = () => {\n    setIsChatOpen(true);\n  };\n\n  const handleCloseChat = () => {\n    setIsChatOpen(false);\n  };\n\n  const handleMinimizeChat = () => {\n    setIsChatOpen(false);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30\">\n      <NavigationV80\n        onNavigate={handleNavigate}\n        onCategoryChange={handleCategoryChange}\n        onLoginClick={handleLoginClick}\n      />\n\n      <CategoryNavigation\n        activeCategory={activeCategory}\n        onCategoryChange={handleCategoryChange}\n        onSubCategoryClick={handleSubCategoryClick}\n      />\n\n      <LandingPage onNavigateToServices={handleNavigateToServices} />\n\n      {/* 服务内容区域 */}\n      <ServicesPageContent\n        activeCategory={activeCategory}\n        activeSubCategory={null}\n        onCategoryChange={handleCategoryChange}\n        onSubCategoryChange={() => {}}\n      />\n\n      {/* 聊天窗口 */}\n      <ChatWindow isOpen={isChatOpen} onClose={handleCloseChat} onMinimize={handleMinimizeChat} />\n\n      {/* 聊天悬浮球 */}\n      <ChatFloatingButton\n        onClick={handleOpenChat}\n        notificationCount={notificationCount}\n        isVisible={!isChatOpen}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;g<PERSON><PERSON>,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF;gBACE;QACJ;IACF;IAEA,MAAM,2BAA2B;QAC/B,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,yBAAyB,CAAC,UAAkB;QAChD,kBAAkB;QAClB,gBAAgB;QAChB,OAAO,IAAI,CAAC,CAAC,mBAAmB,EAAE,SAAS,KAAK,EAAE,aAAa;IACjE;IAEA,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB;QAClB,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU;IACnC;IAEA,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,iBAAiB;QACrB,cAAc;IAChB;IAEA,MAAM,kBAAkB;QACtB,cAAc;IAChB;IAEA,MAAM,qBAAqB;QACzB,cAAc;IAChB;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC,mIAAA,CAAA,gBAAa;gBACZ,YAAY;gBACZ,kBAAkB;gBAClB,cAAc;;;;;;0BAGhB,6WAAC,wIAAA,CAAA,qBAAkB;gBACjB,gBAAgB;gBAChB,kBAAkB;gBAClB,oBAAoB;;;;;;0BAGtB,6WAAC,iIAAA,CAAA,cAAW;gBAAC,sBAAsB;;;;;;0BAGnC,6WAAC,kIAAA,CAAA,sBAAmB;gBAClB,gBAAgB;gBAChB,mBAAmB;gBACnB,kBAAkB;gBAClB,qBAAqB,KAAO;;;;;;0BAI9B,6WAAC,gIAAA,CAAA,aAAU;gBAAC,QAAQ;gBAAY,SAAS;gBAAiB,YAAY;;;;;;0BAGtE,6WAAC,wIAAA,CAAA,qBAAkB;gBACjB,SAAS;gBACT,mBAAmB;gBACnB,WAAW,CAAC;;;;;;;;;;;;AAIpB", "debugId": null}}]}