import { useState, useMemo } from "react";
import { NavigationV80 } from "./components/NavigationV80";
import { NavigationV78 } from "./components/NavigationV78";
import { CategoryNavigation } from "./components/CategoryNavigation";
import { LandingPage } from "./components/LandingPage";
import { ServicesPage } from "./components/ServicesPage";
import { OrdersPage } from "./components/OrdersPage";
import { FavoritesPage } from "./components/FavoritesPage";
import { PlaceholderPage } from "./components/PlaceholderPage";
import { OrderDetail } from "./components/OrderDetail";
import { ProductDetail } from "./components/ProductDetail";
import { LoginPage } from "./components/LoginPage";
import { RegisterPage } from "./components/RegisterPage";
import { ChatWindow } from "./components/ChatWindow";
import { ChatFloatingButton } from "./components/ChatFloatingButton";
import { mockOrders } from "./data/mockOrders";
import { getStatusCounts, filterOrders } from "./utils/orderUtils";

// 模拟商品数据（从ServicesPage中提取）
const mockProducts = [
  {
    id: "1",
    name: "✨绝美品牌视觉设计，让你的LOGO成为爆款记忆点！",
    description: "姐妹们！这个设计师真的太会了😍 从LOGO到整套VI，各种风格都能拿捏住，原创度100%，商用无忧虑～绝对是品牌升级的必备神器！",
    price: 999,
    image: "https://images.unsplash.com/photo-1558655146-d09347e92766?w=400&h=400&fit=crop",
    rating: 5.0,
    reviews: 1234,
    category: "设计",
    tags: ["热门", "原创"],
    isBestseller: true,
    isNew: false,
    creator: {
      name: "设计师小王",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "北京",
      isOnline: true,
      averageResponseTime: "3分钟",
      activeOrdersCount: 5
    },
    favoriteCount: 892,
    specialBadge: "upro" as const,
    images: [
      "https://images.unsplash.com/photo-1558655146-d09347e92766?w=600&h=600&fit=crop",
      "https://images.unsplash.com/photo-1542744094-3a31f272c490?w=600&h=600&fit=crop",
      "https://images.unsplash.com/photo-1558655146-364adaf1fcc9?w=600&h=600&fit=crop"
    ]
  },
  {
    id: "2",
    name: "🚀全栈网站开发YYDS！React+Node.js技术栈，让你的网站飞起来",
    description: "宝贝们看过来！这位程序员小哥哥技术真的绝了🔥 响应式设计+全栈开发+一键部署，从前端到后端全搞定，网站速度嗖嗖的，用户体验直接拉满！",
    price: 4999,
    image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=400&fit=crop",
    rating: 4.9,
    reviews: 856,
    category: "技术",
    tags: ["专业", "全栈"],
    isBestseller: true,
    creator: {
      name: "程序员老李",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "上海",
      isOnline: false,
      lastSeen: "2小时前"
    },
    favoriteCount: 634,
    specialBadge: "top" as const
  }
];

export default function App() {
  const [currentPage, setCurrentPage] = useState<"landing" | "services" | "orders" | "favorites" | "support" | "gallery" | "login" | "register" | "product">("landing");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [dateFilter, setDateFilter] = useState("all");
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [activeCategory, setActiveCategory] = useState("popular");
  const [activeSubCategory, setActiveSubCategory] = useState<string | null>(null);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [notificationCount] = useState(3); // 模拟通知数量

  const handleNavigate = (page: string) => {
    setCurrentPage(page as any);
    setSelectedOrderId(null);
    setSelectedProductId(null);
    // 如果导航到首页，重置到Landing页面
    if (page === "home") {
      setCurrentPage("landing");
      setActiveCategory("popular");
      setActiveSubCategory(null);
    }
  };

  const handleNavigateToServices = () => {
    setCurrentPage("services");
    setActiveCategory("popular");
    setActiveSubCategory(null);
  };

  const handleSubCategoryClick = (category: string, subCategory: string) => {
    setActiveCategory(category);
    setActiveSubCategory(subCategory);
    
    // 如果当前在首页，点击子分类后跳转到服务页面
    if (currentPage === "landing") {
      setCurrentPage("services");
    }
    
    console.log(`已选择子分类: ${subCategory} 在分类: ${category}`);
  };

  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    setActiveSubCategory(null); // 切换主分类时清除子分类
    
    // 如果当前在首页，点击分类后跳转到服务页面
    if (currentPage === "landing") {
      setCurrentPage("services");
    }
  };

  const handleLoginClick = () => {
    setCurrentPage("login");
  };

  const handleRegisterClick = () => {
    setCurrentPage("register");
  };

  const handleBackFromLogin = () => {
    setCurrentPage("landing");
  };

  const handleBackFromRegister = () => {
    setCurrentPage("landing");
  };

  const handleOpenChat = () => {
    setIsChatOpen(true);
  };

  const handleCloseChat = () => {
    setIsChatOpen(false);
  };

  const handleMinimizeChat = () => {
    setIsChatOpen(false);
  };

  // 处理商品详情页导航
  const handleViewProductDetail = (productId: string) => {
    setSelectedProductId(productId);
    setCurrentPage("product");
  };

  const handleBackFromProduct = () => {
    setSelectedProductId(null);
    setCurrentPage("services");
  };

  // 筛选订单
  const filteredOrders = useMemo(() => {
    return filterOrders(mockOrders, searchTerm, statusFilter, dateFilter);
  }, [searchTerm, statusFilter, dateFilter]);

  const statusCounts = getStatusCounts(mockOrders);

  const handleStatusChange = (status: string) => {
    setStatusFilter(status);
  };

  const handleViewOrderDetail = (orderId: string) => {
    setSelectedOrderId(orderId);
  };

  const handleBackToList = () => {
    setSelectedOrderId(null);
  };

  // 如果在登录页面，显示登录页面
  if (currentPage === "login") {
    return (
      <LoginPage 
        onBack={handleBackFromLogin} 
        onRegisterClick={handleRegisterClick}
      />
    );
  }

  // 如果在注册页面，显示注册页面
  if (currentPage === "register") {
    return (
      <RegisterPage 
        onBack={handleBackFromRegister}
        onLoginClick={handleLoginClick}
      />
    );
  }

  // 如果在商品详情页面，显示商品详情
  if (currentPage === "product" && selectedProductId) {
    const selectedProduct = mockProducts.find(product => product.id === selectedProductId);
    if (selectedProduct) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30">
          <NavigationV78 currentPage="services" onNavigate={handleNavigate} onOpenChat={handleOpenChat} />
          <ProductDetail product={selectedProduct} onBack={handleBackFromProduct} />
          
          {/* 聊天窗口 */}
          <ChatWindow isOpen={isChatOpen} onClose={handleCloseChat} onMinimize={handleMinimizeChat} />
          
          {/* 聊天悬浮球 - 当聊天窗口关闭时显示 */}
          <ChatFloatingButton 
            onClick={handleOpenChat}
            notificationCount={notificationCount}
            isVisible={!isChatOpen}
          />
        </div>
      );
    }
  }

  // 如果在订单页面且选中了订单，显示订单详情
  if (currentPage === "orders" && selectedOrderId) {
    const selectedOrder = mockOrders.find(order => order.id === selectedOrderId);
    if (selectedOrder) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30">
          <NavigationV78 currentPage="orders" onNavigate={handleNavigate} onOpenChat={handleOpenChat} />
          <OrderDetail order={selectedOrder} onBack={handleBackToList} />
          
          {/* 聊天窗口 */}
          <ChatWindow isOpen={isChatOpen} onClose={handleCloseChat} onMinimize={handleMinimizeChat} />
          
          {/* 聊天悬浮球 - 当聊天窗口关闭时显示 */}
          <ChatFloatingButton 
            onClick={handleOpenChat}
            notificationCount={notificationCount}
            isVisible={!isChatOpen}
          />
        </div>
      );
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30">
      {/* 根据页面选择导航栏版本 */}
      {currentPage === "landing" ? (
        <NavigationV80 
          onNavigate={handleNavigate} 
          onCategoryChange={handleCategoryChange}
          onLoginClick={handleLoginClick}
        />
      ) : (
        <NavigationV78 
          currentPage={currentPage === "services" ? "services" : currentPage as any} 
          onNavigate={handleNavigate}
          onOpenChat={handleOpenChat}
        />
      )}
      
      {/* 分类导航栏 - 在首页、服务页面和作品广场显示 */}
      {(currentPage === "landing" || currentPage === "services" || currentPage === "gallery") && (
        <CategoryNavigation 
          activeCategory={activeCategory}
          onCategoryChange={handleCategoryChange}
          onSubCategoryClick={handleSubCategoryClick}
        />
      )}
      
      {/* 根据当前页面渲染不同内容 */}
      {currentPage === "landing" && (
        <LandingPage onNavigateToServices={handleNavigateToServices} />
      )}
      
      {currentPage === "services" && (
        <ServicesPage 
          activeCategory={activeCategory} 
          activeSubCategory={activeSubCategory}
          onCategoryChange={handleCategoryChange}
          onSubCategoryChange={setActiveSubCategory}
          onViewProductDetail={handleViewProductDetail}
        />
      )}
      
      {currentPage === "orders" && (
        <OrdersPage
          filteredOrders={filteredOrders}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          statusFilter={statusFilter}
          onStatusFilterChange={setStatusFilter}
          dateFilter={dateFilter}
          onDateFilterChange={setDateFilter}
          statusCounts={statusCounts}
          onStatusChange={handleStatusChange}
          onViewOrderDetail={handleViewOrderDetail}
        />
      )}

      {currentPage === "favorites" && (
        <FavoritesPage />
      )}

      {currentPage === "support" && (
        <PlaceholderPage
          title="定制服务"
          description="专业定制服务平台正在建设中，敬请期待..."
          iconColor="text-green-600"
          gradientColors="from-green-500/10 to-emerald-500/10"
          borderColor="border-green-200/30"
        />
      )}

      {currentPage === "gallery" && (
        <div className="max-w-7xl mx-auto px-4 py-12 text-center">
          <div className="relative inline-block mb-4">
            <div className="absolute inset-0 bg-purple-500/10 rounded-xl blur-sm"></div>
            <div className="relative p-3 bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-xl border border-purple-200/30">
              <div className="h-8 w-8 bg-purple-600 rounded"></div>
            </div>
          </div>
          <h2 className="text-slate-900 mb-2">探索</h2>
          <p className="text-slate-600 mb-4">探索功能开发中，精彩内容即将呈现...</p>
          <p className="text-slate-500 text-sm">当前分类: {activeCategory}</p>
        </div>
      )}

      {/* 聊天窗口 */}
      <ChatWindow isOpen={isChatOpen} onClose={handleCloseChat} onMinimize={handleMinimizeChat} />
      
      {/* 聊天悬浮球 - 当聊天窗口关闭时显示 */}
      <ChatFloatingButton 
        onClick={handleOpenChat}
        notificationCount={notificationCount}
        isVisible={!isChatOpen}
      />
    </div>
  );
}