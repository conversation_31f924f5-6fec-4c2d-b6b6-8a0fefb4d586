import { Badge } from "./ui/badge";

interface OrderStatusBadgeProps {
  status: "pending" | "processing" | "shipped" | "delivered" | "cancelled" | "aftersale";
}

export function OrderStatusBadge({ status }: OrderStatusBadgeProps) {
  const statusConfig = {
    pending: {
      label: "待付款",
      className: "bg-yellow-100 text-yellow-800 hover:bg-yellow-100/80"
    },
    processing: {
      label: "处理中",
      className: "bg-blue-100 text-blue-800 hover:bg-blue-100/80"
    },
    shipped: {
      label: "已发货",
      className: "bg-purple-100 text-purple-800 hover:bg-purple-100/80"
    },
    delivered: {
      label: "已完成",
      className: "bg-green-100 text-green-800 hover:bg-green-100/80"
    },
    cancelled: {
      label: "已取消",
      className: "bg-red-100 text-red-800 hover:bg-red-100/80"
    },
    aftersale: {
      label: "售后中",
      className: "bg-orange-100 text-orange-800 hover:bg-orange-100/80"
    }
  };

  const config = statusConfig[status];

  return (
    <Badge variant="secondary" className={config.className}>
      {config.label}
    </Badge>
  );
}