import { Clock } from "lucide-react";

interface ProgressStep {
  id: string;
  label: string;
  description: string;
  status: "completed" | "current" | "pending";
  completedAt?: string;
}

interface WorkProgressBarProps {
  currentStep: number;
  steps: ProgressStep[];
}

export function WorkProgressBar({ currentStep, steps }: WorkProgressBarProps) {
  const currentStepData = steps.find(step => step.status === "current");
  const completedSteps = steps.filter(step => step.status === "completed");
  const allCompleted = steps.every(step => step.status === "completed");

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* 统一的标题设计 */}
      <h3 className="text-slate-900 flex items-center gap-2">
        <Clock className="h-4 w-4" />
        工作进度
      </h3>

      {/* Apple风格进度条保持原样 */}
      <div className="relative">
        {/* 步骤容器 */}
        <div className="flex justify-between items-start relative">
          {/* 背景连接线 - 带断开效果 */}
          <div className="absolute top-0 left-0 right-0 h-1 flex">
            {steps.map((_, index) => {
              if (index === steps.length - 1) return null;
              
              const isCompleted = currentStep > index;
              const segmentWidth = `${100 / (steps.length - 1)}%`;
              
              return (
                <div key={`segment-${index}`} className="flex-1 flex items-center" style={{ width: segmentWidth }}>
                  {/* 连接线段 */}
                  <div className={`h-1 rounded-full transition-all duration-1000 ${
                    isCompleted ? 'bg-green-500' : 'bg-slate-200'
                  }`} style={{ width: 'calc(100% - 16px)', marginLeft: '32px', marginRight: '16px' }} />
                  
                  {/* 断开间隙 */}
                  {index < steps.length - 2 && (
                    <div className="w-2 h-1 bg-transparent" />
                  )}
                </div>
              );
            })}
          </div>

          {/* 步骤标签 - 增加与进度条的距离 */}
          {steps.map((step, index) => {
            const isCompleted = step.status === "completed";
            const isCurrent = step.status === "current";
            const isActive = isCompleted || isCurrent;

            return (
              <div key={step.id} className="flex flex-col items-center flex-1 relative z-10">
                {/* 步骤标签 - 增加上边距 */}
                <div className="text-center max-w-[140px] mt-8">
                  <div className={`text-base leading-tight ${
                    isActive ? 'text-slate-900' : 'text-slate-400'
                  }`}>
                    {step.label}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Apple风格状态描述 */}
        <div className="mt-12 space-y-6">
          {/* 主状态 */}
          <div>
            <div className="text-slate-900 text-xl mb-6">
              {allCompleted ? (
                "您的工作已完成。"
              ) : currentStepData ? (
                `您的${currentStepData.label.toLowerCase()}正在进行中。`
              ) : (
                "等待开始处理您的订单。"
              )}
            </div>
          </div>

          {/* 详细状态信息 */}
          <div className="space-y-4">
            {/* 显示最新的完成状态 */}
            {completedSteps.length > 0 && (
              <div>
                <div className="text-slate-900 mb-2">
                  {formatDate(completedSteps[completedSteps.length - 1].completedAt!)}：
                </div>
                <div className="text-slate-600 leading-relaxed">
                  {completedSteps[completedSteps.length - 1].description}
                </div>
              </div>
            )}

            {/* 显示当前进行中的状态 */}
            {currentStepData && (
              <div>
                <div className="text-slate-900 mb-2">
                  {formatDate(new Date().toISOString())}：
                </div>
                <div className="text-slate-600 leading-relaxed">
                  {currentStepData.description}
                </div>
              </div>
            )}

            {/* 如果所有步骤都完成，显示完成信息 */}
            {allCompleted && (
              <div>
                <div className="text-slate-600 leading-relaxed">
                  所有步骤已成功完成，感谢您的耐心等待。
                </div>
              </div>
            )}
          </div>

          {/* 历史记录 */}
          {completedSteps.length > 1 && (
            <div className="pt-4 border-t border-slate-200/60">
              <div className="space-y-3">
                {completedSteps.slice(0, -1).reverse().map(step => (
                  <div key={`history-${step.id}`} className="text-sm">
                    <div className="text-slate-600 mb-1">
                      {formatDate(step.completedAt!)}：
                    </div>
                    <div className="text-slate-500">
                      {step.description}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}