"use client";

import { useState } from "react";
import { NavigationV78 } from "@/components/NavigationV78";
import { ChatWindow } from "@/components/ChatWindow";
import { ChatFloatingButton } from "@/components/ChatFloatingButton";
import { useRouter } from "next/navigation";
import { Heart } from "lucide-react";

export default function Favorites() {
  const router = useRouter();
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [notificationCount] = useState(3);

  const handleNavigate = (page: string) => {
    switch (page) {
      case "home":
      case "landing":
        router.push("/");
        break;
      case "services":
        router.push("/services");
        break;
      case "orders":
        router.push("/orders");
        break;
      case "favorites":
        router.push("/favorites");
        break;
      case "support":
        router.push("/support");
        break;
      case "gallery":
        router.push("/gallery");
        break;
      case "login":
        router.push("/login");
        break;
      case "register":
        router.push("/register");
        break;
      default:
        break;
    }
  };

  const handleOpenChat = () => {
    setIsChatOpen(true);
  };

  const handleCloseChat = () => {
    setIsChatOpen(false);
  };

  const handleMinimizeChat = () => {
    setIsChatOpen(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30">
      <NavigationV78 
        currentPage="favorites" 
        onNavigate={handleNavigate}
        onOpenChat={handleOpenChat}
      />
      
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* 页面头部 */}
        <div className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <div className="relative">
              <div className="absolute inset-0 bg-red-500/20 rounded-xl blur-sm"></div>
              <div className="relative p-3 bg-gradient-to-br from-red-500/10 to-pink-500/10 rounded-xl border border-red-200/30">
                <Heart className="h-6 w-6 text-red-600 fill-current" />
              </div>
            </div>
            <div>
              <h1 className="text-2xl font-semibold text-slate-900">我的收藏</h1>
              <p className="text-slate-600">已收藏 0 个专业服务</p>
            </div>
          </div>
        </div>

        {/* 空状态 */}
        <div className="text-center py-16">
          <div className="relative inline-block mb-4">
            <div className="absolute inset-0 bg-slate-500/10 rounded-xl blur-sm"></div>
            <div className="relative p-6 bg-gradient-to-br from-slate-500/10 to-slate-500/5 rounded-xl border border-slate-200/30">
              <Heart className="h-12 w-12 text-slate-400 mx-auto" />
            </div>
          </div>
          <h3 className="text-lg font-medium text-slate-900 mb-2">暂无收藏内容</h3>
          <p className="text-slate-600 mb-6">
            还没有收藏任何服务，快去发现优质服务吧！
          </p>
          <button
            onClick={() => router.push("/services")}
            className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            去逛逛
          </button>
        </div>
      </div>

      {/* 聊天窗口 */}
      <ChatWindow isOpen={isChatOpen} onClose={handleCloseChat} onMinimize={handleMinimizeChat} />
      
      {/* 聊天悬浮球 */}
      <ChatFloatingButton 
        onClick={handleOpenChat}
        notificationCount={notificationCount}
        isVisible={!isChatOpen}
      />
    </div>
  );
}
