{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,KAGoC;QAHpC,EACd,SAAS,EACT,GAAG,OAC+C,GAHpC;IAId,qBACE,4TAAC,kRAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS;AAgBT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,4TAAC,kRAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,eAAe,KAGgC;QAHhC,EACtB,SAAS,EACT,GAAG,OACmD,GAHhC;IAItB,qBACE,4TAAC,kRAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/NavigationV78.tsx"], "sourcesContent": ["import { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { ImageWithFallback } from \"@/components/figma/ImageWithFallback\";\nimport { \n  Bell, \n  Settings, \n  ChevronDown, \n  Package,\n  User,\n  Star,\n  FileText,\n  Search,\n  Palette,\n  Compass,\n  Shield,\n  MessageCircle\n} from \"lucide-react\";\n\ninterface NavigationV78Props {\n  currentPage?: \"services\" | \"orders\" | \"favorites\" | \"support\" | \"gallery\";\n  onNavigate?: (page: string) => void;\n  onOpenChat?: () => void;\n}\n\nexport function NavigationV78({ currentPage = \"services\", onNavigate, onOpenChat }: NavigationV78Props) {\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [notificationCount] = useState(3); // 模拟通知数量\n\n  const handleNavigate = (page: string) => {\n    onNavigate?.(page);\n    setShowUserMenu(false);\n  };\n\n  const handleNotificationClick = () => {\n    onOpenChat?.();\n  };\n\n  return (\n    <nav className=\"bg-white/90 backdrop-blur-sm border-b border-slate-200/60 sticky top-0 z-[70]\">\n      <div className=\"max-w-7xl mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* 左侧 - Logo */}\n          <div className=\"flex items-center gap-8\">\n            <div className=\"flex items-center gap-3 cursor-pointer\" onClick={() => handleNavigate(\"home\")}>\n              <div className=\"relative\">\n                <div className=\"absolute inset-0 bg-blue-500/10 rounded-xl blur-sm\"></div>\n                <div className=\"relative p-2 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-xl border border-blue-200/30\">\n                  <Package className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              </div>\n              <span className=\"text-slate-900 text-lg font-bold tracking-tight\">U-bund</span>\n            </div>\n          </div>\n\n          {/* 中间 - 导航按钮和搜索框 */}\n          <div className=\"flex-1 max-w-4xl mx-8 hidden md:flex items-center gap-4\">\n            {/* 导航按钮组 */}\n            <div className=\"flex items-center gap-2\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => handleNavigate(\"support\")}\n                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${\n                  currentPage === \"support\"\n                    ? 'bg-blue-50/80 text-blue-700 shadow-sm' \n                    : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'\n                }`}\n              >\n                <Palette className=\"h-4 w-4\" />\n                定制服务*\n              </Button>\n              \n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => handleNavigate(\"services\")}\n                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${\n                  currentPage === \"services\"\n                    ? 'bg-blue-50/80 text-blue-700 shadow-sm' \n                    : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'\n                }`}\n              >\n                <Search className=\"h-4 w-4\" />\n                找服务\n              </Button>\n              \n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => handleNavigate(\"gallery\")}\n                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${\n                  currentPage === \"gallery\"\n                    ? 'bg-blue-50/80 text-blue-700 shadow-sm' \n                    : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'\n                }`}\n              >\n                <Compass className=\"h-4 w-4\" />\n                作品广场*\n              </Button>\n            </div>\n\n            {/* 搜索框 */}\n            <div className=\"flex-1 max-w-md relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"搜索服务、商家...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 bg-slate-50/80 border border-slate-200/60 rounded-lg text-sm placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-300 transition-all duration-200\"\n              />\n            </div>\n          </div>\n\n          {/* 右侧 - 用户操作区 */}\n          <div className=\"flex items-center gap-3\">\n            {/* 我的订单 */}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => handleNavigate(\"orders\")}\n              className={`hidden sm:flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${\n                currentPage === \"orders\"\n                  ? 'bg-blue-50/80 text-blue-700 shadow-sm' \n                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'\n              }`}\n            >\n              <FileText className=\"h-4 w-4\" />\n              我的订单\n            </Button>\n\n            {/* 我的收藏 */}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => handleNavigate(\"favorites\")}\n              className={`hidden sm:flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${\n                currentPage === \"favorites\"\n                  ? 'bg-blue-50/80 text-blue-700 shadow-sm' \n                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'\n              }`}\n            >\n              <Star className=\"h-4 w-4\" />\n              我的收藏\n            </Button>\n\n            {/* 通知铃铛 */}\n            <div className=\"relative\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={handleNotificationClick}\n                className=\"relative p-2 rounded-lg text-slate-600 hover:text-slate-900 hover:bg-slate-100/80 transition-all duration-200\"\n              >\n                <Bell className=\"h-5 w-5\" />\n                {notificationCount > 0 && (\n                  <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium\">\n                    {notificationCount}\n                  </span>\n                )}\n              </Button>\n            </div>\n\n            {/* 用户头像和菜单 */}\n            <div className=\"relative\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setShowUserMenu(!showUserMenu)}\n                className=\"flex items-center gap-2 p-1 rounded-lg hover:bg-slate-100/80 transition-all duration-200\"\n              >\n                <Avatar className=\"h-8 w-8\">\n                  <AvatarImage src=\"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces\" />\n                  <AvatarFallback>用户</AvatarFallback>\n                </Avatar>\n                <ChevronDown className=\"h-4 w-4 text-slate-600\" />\n              </Button>\n\n              {/* 用户菜单下拉 */}\n              {showUserMenu && (\n                <div className=\"absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-slate-200/60 py-2 z-50\">\n                  <button\n                    onClick={() => handleNavigate(\"profile\")}\n                    className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 transition-colors\"\n                  >\n                    <User className=\"h-4 w-4\" />\n                    个人中心\n                  </button>\n                  <button\n                    onClick={() => handleNavigate(\"settings\")}\n                    className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 transition-colors\"\n                  >\n                    <Settings className=\"h-4 w-4\" />\n                    设置\n                  </button>\n                  <div className=\"border-t border-slate-200/60 my-2\"></div>\n                  <button\n                    onClick={() => handleNavigate(\"logout\")}\n                    className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors\"\n                  >\n                    退出登录\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;AAqBO,SAAS,cAAc,KAAwE;QAAxE,EAAE,cAAc,UAAU,EAAE,UAAU,EAAE,UAAU,EAAsB,GAAxE;;IAC5B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,SAAS;IAElD,MAAM,iBAAiB,CAAC;QACtB,uBAAA,iCAAA,WAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,0BAA0B;QAC9B,uBAAA,iCAAA;IACF;IAEA,qBACE,4TAAC;QAAI,WAAU;kBACb,cAAA,4TAAC;YAAI,WAAU;sBACb,cAAA,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC;4BAAI,WAAU;4BAAyC,SAAS,IAAM,eAAe;;8CACpF,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;;;;;;sDACf,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC,+RAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGvB,4TAAC;oCAAK,WAAU;8CAAkD;;;;;;;;;;;;;;;;;kCAKtE,4TAAC;wBAAI,WAAU;;0CAEb,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,WAAW,AAAC,4EAIX,OAHC,gBAAgB,YACZ,0CACA;;0DAGN,4TAAC,+RAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAIjC,4TAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,WAAW,AAAC,4EAIX,OAHC,gBAAgB,aACZ,0CACA;;0DAGN,4TAAC,6RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAIhC,4TAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,WAAW,AAAC,4EAIX,OAHC,gBAAgB,YACZ,0CACA;;0DAGN,4TAAC,+RAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;0CAMnC,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,6RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,4TAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;;;;;;;kCAMhB,4TAAC;wBAAI,WAAU;;0CAEb,4TAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe;gCAC9B,WAAW,AAAC,sFAIX,OAHC,gBAAgB,WACZ,0CACA;;kDAGN,4TAAC,qSAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAKlC,4TAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe;gCAC9B,WAAW,AAAC,sFAIX,OAHC,gBAAgB,cACZ,0CACA;;kDAGN,4TAAC,yRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAK9B,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;;sDAEV,4TAAC,yRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,oBAAoB,mBACnB,4TAAC;4CAAK,WAAU;sDACb;;;;;;;;;;;;;;;;;0CAOT,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;;0DAEV,4TAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,4TAAC,qIAAA,CAAA,cAAW;wDAAC,KAAI;;;;;;kEACjB,4TAAC,qIAAA,CAAA,iBAAc;kEAAC;;;;;;;;;;;;0DAElB,4TAAC,2SAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;oCAIxB,8BACC,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDACC,SAAS,IAAM,eAAe;gDAC9B,WAAU;;kEAEV,4TAAC,yRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAG9B,4TAAC;gDACC,SAAS,IAAM,eAAe;gDAC9B,WAAU;;kEAEV,4TAAC,iSAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,4TAAC;gDAAI,WAAU;;;;;;0DACf,4TAAC;gDACC,SAAS,IAAM,eAAe;gDAC9B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB;GA3LgB;KAAA", "debugId": null}}, {"offset": {"line": 586, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 639, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ChatWindow.tsx"], "sourcesContent": ["import { useState } from \"react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { \n  X, \n  Minus, \n  Send, \n  Bell,\n  MessageCircle,\n  User,\n  Bot\n} from \"lucide-react\";\n\ninterface ChatWindowProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onMinimize: () => void;\n}\n\ninterface Contact {\n  id: string;\n  name: string;\n  avatar: string;\n  isOnline: boolean;\n  lastMessage: string;\n  unreadCount: number;\n  type: \"user\" | \"official\";\n}\n\ninterface Message {\n  id: string;\n  content: string;\n  sender: \"me\" | \"other\";\n  time: string;\n  type: \"text\" | \"image\" | \"file\";\n}\n\nexport function ChatWindow({ isOpen, onClose, onMinimize }: ChatWindowProps) {\n  const [activeContact, setActiveContact] = useState(\"official\");\n  const [messageInput, setMessageInput] = useState(\"\");\n\n  // 模拟联系人数据\n  const contacts: Contact[] = [\n    {\n      id: \"official\",\n      name: \"官方客服\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: true,\n      lastMessage: \"有什么可以帮助您的吗？\",\n      unreadCount: 1,\n      type: \"official\"\n    },\n    {\n      id: \"merchant1\",\n      name: \"设计师小王\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: true,\n      lastMessage: \"好的，我马上开始设计\",\n      unreadCount: 2,\n      type: \"user\"\n    },\n    {\n      id: \"merchant2\",\n      name: \"程序员老李\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: false,\n      lastMessage: \"网站开发进度如何？\",\n      unreadCount: 0,\n      type: \"user\"\n    }\n  ];\n\n  // 模拟消息数据\n  const [messages] = useState<Record<string, Message[]>>({\n    official: [\n      {\n        id: \"1\",\n        content: \"您好！欢迎使用U-bund平台，有什么可以帮助您的吗？\",\n        sender: \"other\",\n        time: \"14:30\",\n        type: \"text\"\n      }\n    ],\n    merchant1: [\n      {\n        id: \"1\",\n        content: \"您好，关于Logo设计的需求我已经收到了\",\n        sender: \"other\",\n        time: \"10:15\",\n        type: \"text\"\n      },\n      {\n        id: \"2\",\n        content: \"好的，请问大概什么时候能完成？\",\n        sender: \"me\",\n        time: \"10:16\",\n        type: \"text\"\n      },\n      {\n        id: \"3\",\n        content: \"预计3-5个工作日，我会先做几个方案给您选择\",\n        sender: \"other\",\n        time: \"10:18\",\n        type: \"text\"\n      }\n    ],\n    merchant2: [\n      {\n        id: \"1\",\n        content: \"网站开发项目已经开始了，预计下周完成\",\n        sender: \"other\",\n        time: \"昨天\",\n        type: \"text\"\n      }\n    ]\n  });\n\n  const currentContact = contacts.find(c => c.id === activeContact);\n  const currentMessages = messages[activeContact] || [];\n\n  const handleSendMessage = () => {\n    if (messageInput.trim()) {\n      console.log(\"发送消息:\", messageInput);\n      setMessageInput(\"\");\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed bottom-4 right-4 w-[640px] h-[768px] bg-white border border-slate-200 rounded-lg shadow-2xl z-[100] flex flex-col overflow-hidden\">\n      {/* 顶部标题栏 */}\n      <div className=\"bg-slate-50/80 border-b border-slate-200 px-6 py-4 flex items-center justify-between\">\n        <div className=\"flex items-center gap-3\">\n          <Bell className=\"h-5 w-5 text-slate-600\" />\n          <span className=\"text-slate-900 font-medium\">消息通知</span>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={onMinimize}\n            className=\"h-8 w-8 p-0 hover:bg-slate-200/60\"\n          >\n            <Minus className=\"h-4 w-4\" />\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={onClose}\n            className=\"h-8 w-8 p-0 hover:bg-slate-200/60\"\n          >\n            <X className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"flex flex-1 overflow-hidden\">\n        {/* 左侧联系人列表 */}\n        <div className=\"w-64 border-r border-slate-200 bg-slate-50/30\">\n          <div className=\"p-4\">\n            <h3 className=\"text-sm font-medium text-slate-900 mb-3\">最近联系</h3>\n            <div className=\"space-y-2\">\n              {contacts.map((contact) => (\n                <div\n                  key={contact.id}\n                  onClick={() => setActiveContact(contact.id)}\n                  className={`flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors ${\n                    activeContact === contact.id\n                      ? 'bg-blue-50 border border-blue-200'\n                      : 'hover:bg-slate-100/60'\n                  }`}\n                >\n                  <div className=\"relative\">\n                    <Avatar className=\"h-10 w-10\">\n                      <AvatarImage src={contact.avatar} />\n                      <AvatarFallback>\n                        {contact.type === \"official\" ? <Bot className=\"h-5 w-5\" /> : <User className=\"h-5 w-5\" />}\n                      </AvatarFallback>\n                    </Avatar>\n                    <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ${\n                      contact.isOnline ? 'bg-green-500' : 'bg-slate-400'\n                    }`}></div>\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center justify-between mb-1\">\n                      <span className=\"text-sm font-medium text-slate-900 truncate\">\n                        {contact.name}\n                      </span>\n                      {contact.unreadCount > 0 && (\n                        <Badge className=\"h-5 w-5 p-0 flex items-center justify-center text-xs bg-red-500\">\n                          {contact.unreadCount}\n                        </Badge>\n                      )}\n                    </div>\n                    <p className=\"text-xs text-slate-600 truncate\">\n                      {contact.lastMessage}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* 右侧聊天区域 */}\n        <div className=\"flex-1 flex flex-col\">\n          {/* 聊天头部 */}\n          {currentContact && (\n            <div className=\"border-b border-slate-200 p-4\">\n              <div className=\"flex items-center gap-3\">\n                <Avatar className=\"h-10 w-10\">\n                  <AvatarImage src={currentContact.avatar} />\n                  <AvatarFallback>\n                    {currentContact.type === \"official\" ? <Bot className=\"h-5 w-5\" /> : <User className=\"h-5 w-5\" />}\n                  </AvatarFallback>\n                </Avatar>\n                <div>\n                  <h4 className=\"font-medium text-slate-900\">{currentContact.name}</h4>\n                  <p className=\"text-sm text-slate-600\">\n                    {currentContact.isOnline ? '在线' : '离线'}\n                  </p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* 消息列表 */}\n          <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n            {currentMessages.map((message) => (\n              <div\n                key={message.id}\n                className={`flex ${message.sender === 'me' ? 'justify-end' : 'justify-start'}`}\n              >\n                <div\n                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${\n                    message.sender === 'me'\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-slate-100 text-slate-900'\n                  }`}\n                >\n                  <p className=\"text-sm\">{message.content}</p>\n                  <p className={`text-xs mt-1 ${\n                    message.sender === 'me' ? 'text-blue-100' : 'text-slate-500'\n                  }`}>\n                    {message.time}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* 输入框 */}\n          {currentContact?.type !== \"official\" && (\n            <div className=\"border-t border-slate-200 p-4\">\n              <div className=\"flex items-end gap-3\">\n                <div className=\"flex-1\">\n                  <textarea\n                    value={messageInput}\n                    onChange={(e) => setMessageInput(e.target.value)}\n                    onKeyPress={handleKeyPress}\n                    placeholder=\"输入消息...\"\n                    className=\"w-full px-3 py-2 border border-slate-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-300\"\n                    rows={1}\n                  />\n                </div>\n                <Button\n                  onClick={handleSendMessage}\n                  disabled={!messageInput.trim()}\n                  className=\"bg-blue-600 hover:bg-blue-700\"\n                >\n                  <Send className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;AAkCO,SAAS,WAAW,KAAgD;QAAhD,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAmB,GAAhD;;IACzB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,UAAU;IACV,MAAM,WAAsB;QAC1B;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,UAAU;YACV,aAAa;YACb,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,UAAU;YACV,aAAa;YACb,aAAa;Y<PERSON><PERSON>,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,UAAU;YACV,aAAa;YACb,aAAa;YACb,MAAM;QACR;KACD;IAED,SAAS;IACT,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAA6B;QACrD,UAAU;YACR;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,MAAM;YACR;SACD;QACD,WAAW;YACT;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,MAAM;YACR;SACD;QACD,WAAW;YACT;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,MAAM;YACR;SACD;IACH;IAEA,MAAM,iBAAiB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACnD,MAAM,kBAAkB,QAAQ,CAAC,cAAc,IAAI,EAAE;IAErD,MAAM,oBAAoB;QACxB,IAAI,aAAa,IAAI,IAAI;YACvB,QAAQ,GAAG,CAAC,SAAS;YACrB,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC,yRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,4TAAC;gCAAK,WAAU;0CAA6B;;;;;;;;;;;;kCAE/C,4TAAC;wBAAI,WAAU;;0CACb,4TAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,4TAAC,2RAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,4TAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,4TAAC,mRAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAKnB,4TAAC;gBAAI,WAAU;;kCAEb,4TAAC;wBAAI,WAAU;kCACb,cAAA,4TAAC;4BAAI,WAAU;;8CACb,4TAAC;oCAAG,WAAU;8CAA0C;;;;;;8CACxD,4TAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,4TAAC;4CAEC,SAAS,IAAM,iBAAiB,QAAQ,EAAE;4CAC1C,WAAW,AAAC,2EAIX,OAHC,kBAAkB,QAAQ,EAAE,GACxB,sCACA;;8DAGN,4TAAC;oDAAI,WAAU;;sEACb,4TAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;;8EAChB,4TAAC,qIAAA,CAAA,cAAW;oEAAC,KAAK,QAAQ,MAAM;;;;;;8EAChC,4TAAC,qIAAA,CAAA,iBAAc;8EACZ,QAAQ,IAAI,KAAK,2BAAa,4TAAC,uRAAA,CAAA,MAAG;wEAAC,WAAU;;;;;6FAAe,4TAAC,yRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAGjF,4TAAC;4DAAI,WAAW,AAAC,8EAEhB,OADC,QAAQ,QAAQ,GAAG,iBAAiB;;;;;;;;;;;;8DAGxC,4TAAC;oDAAI,WAAU;;sEACb,4TAAC;4DAAI,WAAU;;8EACb,4TAAC;oEAAK,WAAU;8EACb,QAAQ,IAAI;;;;;;gEAEd,QAAQ,WAAW,GAAG,mBACrB,4TAAC,oIAAA,CAAA,QAAK;oEAAC,WAAU;8EACd,QAAQ,WAAW;;;;;;;;;;;;sEAI1B,4TAAC;4DAAE,WAAU;sEACV,QAAQ,WAAW;;;;;;;;;;;;;2CA/BnB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;kCAyCzB,4TAAC;wBAAI,WAAU;;4BAEZ,gCACC,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,4TAAC,qIAAA,CAAA,cAAW;oDAAC,KAAK,eAAe,MAAM;;;;;;8DACvC,4TAAC,qIAAA,CAAA,iBAAc;8DACZ,eAAe,IAAI,KAAK,2BAAa,4TAAC,uRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;6EAAe,4TAAC,yRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGxF,4TAAC;;8DACC,4TAAC;oDAAG,WAAU;8DAA8B,eAAe,IAAI;;;;;;8DAC/D,4TAAC;oDAAE,WAAU;8DACV,eAAe,QAAQ,GAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;;0CAQ5C,4TAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,wBACpB,4TAAC;wCAEC,WAAW,AAAC,QAAiE,OAA1D,QAAQ,MAAM,KAAK,OAAO,gBAAgB;kDAE7D,cAAA,4TAAC;4CACC,WAAW,AAAC,6CAIX,OAHC,QAAQ,MAAM,KAAK,OACf,2BACA;;8DAGN,4TAAC;oDAAE,WAAU;8DAAW,QAAQ,OAAO;;;;;;8DACvC,4TAAC;oDAAE,WAAW,AAAC,gBAEd,OADC,QAAQ,MAAM,KAAK,OAAO,kBAAkB;8DAE3C,QAAQ,IAAI;;;;;;;;;;;;uCAdZ,QAAQ,EAAE;;;;;;;;;;4BAsBpB,CAAA,2BAAA,qCAAA,eAAgB,IAAI,MAAK,4BACxB,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC;4CAAI,WAAU;sDACb,cAAA,4TAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,YAAY;gDACZ,aAAY;gDACZ,WAAU;gDACV,MAAM;;;;;;;;;;;sDAGV,4TAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,CAAC,aAAa,IAAI;4CAC5B,WAAU;sDAEV,cAAA,4TAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlC;GA1PgB;KAAA", "debugId": null}}, {"offset": {"line": 1170, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ChatFloatingButton.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { MessageCircle } from \"lucide-react\";\n\ninterface ChatFloatingButtonProps {\n  onClick: () => void;\n  notificationCount?: number;\n  isVisible?: boolean;\n}\n\nexport function ChatFloatingButton({ \n  onClick, \n  notificationCount = 0, \n  isVisible = true \n}: ChatFloatingButtonProps) {\n  if (!isVisible) return null;\n\n  return (\n    <div className=\"fixed bottom-6 right-6 z-50\">\n      <Button\n        onClick={onClick}\n        size=\"lg\"\n        className=\"relative h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 group\"\n      >\n        <MessageCircle className=\"h-6 w-6 text-white group-hover:scale-110 transition-transform\" />\n        \n        {/* 通知数量徽章 */}\n        {notificationCount > 0 && (\n          <Badge className=\"absolute -top-2 -right-2 h-6 w-6 p-0 flex items-center justify-center text-xs bg-red-500 text-white border-2 border-white\">\n            {notificationCount > 99 ? '99+' : notificationCount}\n          </Badge>\n        )}\n        \n        {/* 脉冲动画 */}\n        <div className=\"absolute inset-0 rounded-full bg-blue-600 animate-ping opacity-20\"></div>\n      </Button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAQO,SAAS,mBAAmB,KAIT;QAJS,EACjC,OAAO,EACP,oBAAoB,CAAC,EACrB,YAAY,IAAI,EACQ,GAJS;IAKjC,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,4TAAC;QAAI,WAAU;kBACb,cAAA,4TAAC,qIAAA,CAAA,SAAM;YACL,SAAS;YACT,MAAK;YACL,WAAU;;8BAEV,4TAAC,+SAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;gBAGxB,oBAAoB,mBACnB,4TAAC,oIAAA,CAAA,QAAK;oBAAC,WAAU;8BACd,oBAAoB,KAAK,QAAQ;;;;;;8BAKtC,4TAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;KA5BgB", "debugId": null}}, {"offset": {"line": 1237, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,4TAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1270, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,4TAAC,kRAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,4TAAC,kRAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,4TAAC,kRAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,4TAAC,kRAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,4TAAC,kRAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,4TAAC,+SAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,4TAAC,kRAAA,CAAA,SAAsB;kBACrB,cAAA,4TAAC,kRAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,4TAAC;;;;;8BACD,4TAAC,kRAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,4TAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,4TAAC,kRAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,4TAAC,kRAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,4TAAC;gBAAK,WAAU;0BACd,cAAA,4TAAC,kRAAA,CAAA,gBAA6B;8BAC5B,cAAA,4TAAC,+RAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,4TAAC,kRAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,4TAAC,kRAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,4TAAC,kRAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,2SAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,4TAAC,kRAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,4TAAC,+SAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 1529, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,4TAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 1651, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/data/mockFavorites.ts"], "sourcesContent": ["import { FavoriteItem } from \"../types/favorites\";\n\nexport const mockFavorites: FavoriteItem[] = [\n  {\n    id: \"fav_1\",\n    title: \"企业品牌Logo设计 + VI视觉识别系统\",\n    description: \"专业Logo设计，包含完整VI手册，商用授权，多方案对比选择\",\n    price: 8999,\n    originalPrice: 12999,\n    image: \"https://images.unsplash.com/photo-1634942537034-2531766767d1?w=400&h=400&fit=crop\",\n    creator: {\n      name: \"墨刻品牌设计\",\n      avatar: \"https://images.unsplash.com/photo-1472214103451-9374bd1c798e?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: true,\n      location: \"上海\"\n    },\n    rating: 4.9,\n    reviewCount: 234,\n    tags: [\"U pro\", \"好评Top\"],\n    category: \"图像设计\",\n    addedAt: \"2025-01-25\"\n  },\n  {\n    id: \"fav_2\",\n    title: \"响应式企业官网定制开发\",\n    description: \"React技术栈，响应式设计，SEO优化，包含后台管理系统\",\n    price: 15999,\n    image: \"https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=400&fit=crop\",\n    creator: {\n      name: \"码猿科技团队\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: false,\n      location: \"北京\"\n    },\n    rating: 4.8,\n    reviewCount: 156,\n    tags: [\"技能之星\"],\n    category: \"编程技术\",\n    addedAt: \"2025-01-24\"\n  },\n  {\n    id: \"fav_3\",\n    title: \"移动端APP UI/UX设计\",\n    description: \"原创设计，交互原型，设计规范，切图标注，多尺寸适配\",\n    price: 3299,\n    image: \"https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=400&fit=crop\",\n    creator: {\n      name: \"像素完美设计\",\n      avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: true,\n      location: \"深圳\"\n    },\n    rating: 4.9,\n    reviewCount: 89,\n    tags: [\"U pro\", \"好评Top\"],\n    category: \"图像设计\",\n    addedAt: \"2025-01-23\"\n  },\n  {\n    id: \"fav_4\",\n    title: \"企业宣传片拍摄制作（3分钟）\",\n    description: \"4K拍摄，专业团队，后期调色，配音配乐，多格式输出\",\n    price: 2899,\n    image: \"https://images.unsplash.com/photo-1485846234645-a62644f84728?w=400&h=400&fit=crop\",\n    creator: {\n      name: \"光影视觉制作\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: true,\n      location: \"广州\"\n    },\n    rating: 4.7,\n    reviewCount: 67,\n    tags: [\"技能之星\"],\n    category: \"视频动画\",\n    addedAt: \"2025-01-22\"\n  },\n  {\n    id: \"fav_5\",\n    title: \"品牌文案策划服务\",\n    description: \"品牌slogan，产品文案，官网内容，SEO优化，多语言版本\",\n    price: 899,\n    image: \"https://images.unsplash.com/photo-1455390582262-044cdead277a?w=400&h=400&fit=crop\",\n    creator: {\n      name: \"文字匠人创意\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: false,\n      location: \"杭州\"\n    },\n    rating: 4.6,\n    reviewCount: 123,\n    tags: [\"好评Top\"],\n    category: \"文案策划\",\n    addedAt: \"2025-01-21\"\n  },\n  {\n    id: \"fav_6\",\n    title: \"商品摄影服务（20张精修）\",\n    description: \"专业棚拍，多角度拍摄，精修20张，商用授权，多格式输出\",\n    price: 1999,\n    image: \"https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=400&h=400&fit=crop\",\n    creator: {\n      name: \"光影商业摄影\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: true,\n      location: \"成都\"\n    },\n    rating: 4.8,\n    reviewCount: 45,\n    tags: [\"U pro\"],\n    category: \"摄影服务\",\n    addedAt: \"2025-01-20\"\n  },\n  {\n    id: \"fav_7\",\n    title: \"社交媒体营销推广方案（30天）\",\n    description: \"小红书推广，抖音内容营销，微博话题运营，数据报告\",\n    price: 1599,\n    originalPrice: 2199,\n    image: \"https://images.unsplash.com/photo-1432888622747-4eb9a8efeb07?w=400&h=400&fit=crop\",\n    creator: {\n      name: \"数字营销专家\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: true,\n      location: \"武汉\"\n    },\n    rating: 4.5,\n    reviewCount: 78,\n    tags: [\"技能之星\"],\n    category: \"数字营销\",\n    addedAt: \"2025-01-19\"\n  },\n  {\n    id: \"fav_8\",\n    title: \"iOS + Android原生应用开发\",\n    description: \"Swift + Kotlin开发，适配多设备，应用商店上架，一年维护\",\n    price: 25999,\n    image: \"https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=400&fit=crop\",\n    creator: {\n      name: \"星云移动应用\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616c989c32c?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: false,\n      location: \"南京\"\n    },\n    rating: 4.9,\n    reviewCount: 34,\n    tags: [\"U pro\", \"技能之星\"],\n    category: \"编程技术\",\n    addedAt: \"2025-01-18\"\n  },\n  {\n    id: \"fav_9\",\n    title: \"电商产品详情页设计\",\n    description: \"专业详情页设计，提升转化率，包含主图设计和长图制作\",\n    price: 1299,\n    image: \"https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=400&fit=crop\",\n    creator: {\n      name: \"电商设计工作室\",\n      avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: true,\n      location: \"苏州\"\n    },\n    rating: 4.7,\n    reviewCount: 112,\n    tags: [\"好评Top\"],\n    category: \"图像设计\",\n    addedAt: \"2025-01-17\"\n  },\n  {\n    id: \"fav_10\",\n    title: \"微信小程序开发定制\",\n    description: \"微信小程序全栈开发，包含前端界面和后端接口开发\",\n    price: 8888,\n    originalPrice: 12888,\n    image: \"https://images.unsplash.com/photo-1551650975-87deedd944c3?w=400&h=400&fit=crop\",\n    creator: {\n      name: \"小程序开发专家\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: true,\n      location: \"西安\"\n    },\n    rating: 4.8,\n    reviewCount: 91,\n    tags: [\"U pro\", \"好评Top\"],\n    category: \"编程技术\",\n    addedAt: \"2025-01-16\"\n  },\n  {\n    id: \"fav_11\",\n    title: \"短视频剪辑制作服务\",\n    description: \"抖音快手短视频剪辑，专业调色，字幕配音，爆款包装\",\n    price: 599,\n    image: \"https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=400&h=400&fit=crop\",\n    creator: {\n      name: \"视频剪辑达人\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: true,\n      location: \"长沙\"\n    },\n    rating: 4.6,\n    reviewCount: 156,\n    tags: [\"技能之星\"],\n    category: \"视频动画\",\n    addedAt: \"2025-01-15\"\n  },\n  {\n    id: \"fav_12\",\n    title: \"企业画册设计制作\",\n    description: \"高端画册设计，印刷工艺指导，包含封面和内页全套设计\",\n    price: 2599,\n    image: \"https://images.unsplash.com/photo-1586880244386-8b3e34c8382c?w=400&h=400&fit=crop\",\n    creator: {\n      name: \"印象画册设计\",\n      avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: false,\n      location: \"天津\"\n    },\n    rating: 4.9,\n    reviewCount: 67,\n    tags: [\"U pro\", \"好评Top\"],\n    category: \"图像设计\",\n    addedAt: \"2025-01-14\"\n  }\n];\n"], "names": [], "mappings": ";;;AAEO,MAAM,gBAAgC;IAC3C;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,eAAe;QACf,OAAO;QACP,SAAS;YACP,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,QAAQ;QACR,aAAa;QACb,MAAM;YAAC;YAAS;SAAQ;QACxB,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,SAAS;YACP,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,QAAQ;QACR,aAAa;QACb,MAAM;YAAC;SAAO;QACd,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,SAAS;YACP,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,QAAQ;QACR,aAAa;QACb,MAAM;YAAC;YAAS;SAAQ;QACxB,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,SAAS;YACP,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,QAAQ;QACR,aAAa;QACb,MAAM;YAAC;SAAO;QACd,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,SAAS;YACP,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,QAAQ;QACR,aAAa;QACb,MAAM;YAAC;SAAQ;QACf,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,SAAS;YACP,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,QAAQ;QACR,aAAa;QACb,MAAM;YAAC;SAAQ;QACf,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,eAAe;QACf,OAAO;QACP,SAAS;YACP,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,QAAQ;QACR,aAAa;QACb,MAAM;YAAC;SAAO;QACd,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,SAAS;YACP,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,QAAQ;QACR,aAAa;QACb,MAAM;YAAC;YAAS;SAAO;QACvB,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,SAAS;YACP,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,QAAQ;QACR,aAAa;QACb,MAAM;YAAC;SAAQ;QACf,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,eAAe;QACf,OAAO;QACP,SAAS;YACP,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,QAAQ;QACR,aAAa;QACb,MAAM;YAAC;YAAS;SAAQ;QACxB,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,SAAS;YACP,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,QAAQ;QACR,aAAa;QACb,MAAM;YAAC;SAAO;QACd,UAAU;QACV,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;QACP,SAAS;YACP,MAAM;YACN,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,QAAQ;QACR,aAAa;QACb,MAAM;YAAC;YAAS;SAAQ;QACxB,UAAU;QACV,SAAS;IACX;CACD", "debugId": null}}, {"offset": {"line": 1913, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/FavoritesPage.tsx"], "sourcesContent": ["import { useState, useMemo } from \"react\";\nimport { Heart, Search, Filter, Star, MapPin, Calendar } from \"lucide-react\";\nimport { <PERSON><PERSON> } from \"./ui/button\";\nimport { Input } from \"./ui/input\";\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from \"./ui/select\";\nimport { Badge } from \"./ui/badge\";\nimport { Card, CardContent } from \"./ui/card\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"./ui/avatar\";\nimport { Separator } from \"./ui/separator\";\nimport { mockFavorites } from \"../data/mockFavorites\";\nimport { FavoriteItem } from \"../types/favorites\";\n\nexport function FavoritesPageContent() {\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [sortBy, setSortBy] = useState(\"latest\");\n  const [filterCategory, setFilterCategory] = useState(\"all\");\n\n  // 筛选和排序收藏商品\n  const filteredAndSortedFavorites = useMemo(() => {\n    let filtered = mockFavorites;\n\n    // 搜索筛选\n    if (searchTerm) {\n      const lowerSearchTerm = searchTerm.toLowerCase();\n      filtered = filtered.filter((item) =>\n        item.title.toLowerCase().includes(lowerSearchTerm) ||\n        item.description.toLowerCase().includes(lowerSearchTerm) ||\n        item.creator.name.toLowerCase().includes(lowerSearchTerm)\n      );\n    }\n\n    // 分类筛选\n    if (filterCategory !== \"all\") {\n      filtered = filtered.filter(item => item.category === filterCategory);\n    }\n\n    // 排序\n    const sorted = [...filtered].sort((a, b) => {\n      switch (sortBy) {\n        case \"latest\":\n          return new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime();\n        case \"price-low\":\n          return a.price - b.price;\n        case \"price-high\":\n          return b.price - a.price;\n        case \"rating\":\n          return b.rating - a.rating;\n        default:\n          return 0;\n      }\n    });\n\n    return sorted;\n  }, [searchTerm, sortBy, filterCategory]);\n\n  // 获取所有分类\n  const categories = [...new Set(mockFavorites.map(item => item.category))];\n\n  const handleRemoveFavorite = (id: string) => {\n    console.log(`移除收藏: ${id}`);\n    // 这里可以添加实际的移除收藏逻辑\n  };\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString);\n    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });\n  };\n\n  const renderServiceCard = (service: FavoriteItem) => (\n    <Card key={service.id} className=\"group overflow-hidden border-0 bg-white/70 backdrop-blur-sm hover:bg-white hover:shadow-lg transition-all duration-300 hover:-translate-y-1\">\n      <CardContent className=\"p-0\">\n        {/* 商品图片 */}\n        <div className=\"relative aspect-[4/3] overflow-hidden\">\n          <img\n            src={service.image}\n            alt={service.title}\n            className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-700\"\n          />\n          \n          {/* 收藏移除按钮 */}\n          <button\n            onClick={() => handleRemoveFavorite(service.id)}\n            className=\"absolute top-3 right-3 w-8 h-8 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-red-500 hover:bg-white hover:scale-110 transition-all duration-200 shadow-lg\"\n          >\n            <Heart className=\"h-4 w-4 fill-current\" />\n          </button>\n\n          {/* 商品标签 */}\n          {service.tags && service.tags.length > 0 && (\n            <div className=\"absolute bottom-3 left-3 flex gap-1.5\">\n              {service.tags.map((tag, index) => {\n                const tagColors = {\n                  \"U pro\": \"bg-gradient-to-r from-blue-500 to-indigo-500 text-white\",\n                  \"好评Top\": \"bg-gradient-to-r from-green-500 to-emerald-500 text-white\", \n                  \"技能之星\": \"bg-gradient-to-r from-purple-500 to-pink-500 text-white\"\n                };\n                return (\n                  <Badge \n                    key={index}\n                    className={`text-xs px-2 py-0.5 border-0 ${tagColors[tag as keyof typeof tagColors] || 'bg-gray-500 text-white'}`}\n                  >\n                    {tag}\n                  </Badge>\n                );\n              })}\n            </div>\n          )}\n        </div>\n\n        {/* 商品信息 */}\n        <div className=\"p-4\">\n          {/* 创作者信息 */}\n          <div className=\"flex items-center gap-2 mb-3\">\n            <Avatar className=\"h-6 w-6\">\n              <AvatarImage src={service.creator.avatar} />\n              <AvatarFallback className=\"text-xs\">{service.creator.name[0]}</AvatarFallback>\n            </Avatar>\n            <span className=\"text-sm text-slate-600 truncate\">{service.creator.name}</span>\n            {service.creator.isOnline && (\n              <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n            )}\n            <div className=\"flex items-center gap-1 text-xs text-slate-500 ml-auto\">\n              <MapPin className=\"h-3 w-3\" />\n              {service.creator.location}\n            </div>\n          </div>\n\n          {/* 商品标题 */}\n          <h3 className=\"font-medium text-slate-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors\">\n            {service.title}\n          </h3>\n\n          {/* 评分和评价数 */}\n          <div className=\"flex items-center gap-2 mb-3\">\n            <div className=\"flex items-center gap-1\">\n              <Star className=\"h-4 w-4 text-yellow-500 fill-current\" />\n              <span className=\"text-sm font-medium text-slate-700\">{service.rating}</span>\n            </div>\n            <span className=\"text-sm text-slate-500\">({service.reviewCount} 评价)</span>\n          </div>\n\n          {/* 价格和收藏时间 */}\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-2\">\n              <span className=\"text-lg font-semibold text-slate-900\">¥{service.price}</span>\n              {service.originalPrice && (\n                <span className=\"text-sm text-slate-500 line-through\">¥{service.originalPrice}</span>\n              )}\n            </div>\n            <div className=\"flex items-center gap-1 text-xs text-slate-500\">\n              <Calendar className=\"h-3 w-3\" />\n              {formatDate(service.addedAt)}\n            </div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 py-8\">\n      {/* 页面头部 */}\n      <div className=\"mb-8\">\n        <div className=\"flex items-center gap-3 mb-4\">\n          <div className=\"relative\">\n            <div className=\"absolute inset-0 bg-red-500/20 rounded-xl blur-sm\"></div>\n            <div className=\"relative p-3 bg-gradient-to-br from-red-500/10 to-pink-500/10 rounded-xl border border-red-200/30\">\n              <Heart className=\"h-6 w-6 text-red-600 fill-current\" />\n            </div>\n          </div>\n          <div>\n            <h1 className=\"text-2xl font-semibold text-slate-900\">我的收藏</h1>\n            <p className=\"text-slate-600\">已收藏 {mockFavorites.length} 个专业服务</p>\n          </div>\n        </div>\n\n        {/* 搜索和筛选栏 */}\n        <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between bg-white/60 backdrop-blur-sm rounded-2xl p-4 border border-slate-200/60\">\n          <div className=\"flex-1 max-w-md\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\" />\n              <Input\n                placeholder=\"搜索收藏的服务...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10 bg-white/80 border-slate-200/60 focus:bg-white\"\n              />\n            </div>\n          </div>\n          \n          <div className=\"flex gap-3\">\n            <Select value={filterCategory} onValueChange={setFilterCategory}>\n              <SelectTrigger className=\"w-32 bg-white/80 border-slate-200/60\">\n                <SelectValue placeholder=\"分类\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">全部分类</SelectItem>\n                {categories.map(category => (\n                  <SelectItem key={category} value={category}>{category}</SelectItem>\n                ))}\n              </SelectContent>\n            </Select>\n\n            <Select value={sortBy} onValueChange={setSortBy}>\n              <SelectTrigger className=\"w-32 bg-white/80 border-slate-200/60\">\n                <SelectValue placeholder=\"排序\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"latest\">最新收藏</SelectItem>\n                <SelectItem value=\"price-low\">价格从低到高</SelectItem>\n                <SelectItem value=\"price-high\">价格从高到低</SelectItem>\n                <SelectItem value=\"rating\">评分最高</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n        </div>\n      </div>\n\n      {/* 收藏列表 */}\n      {filteredAndSortedFavorites.length > 0 ? (\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n          {filteredAndSortedFavorites.map(renderServiceCard)}\n        </div>\n      ) : (\n        <div className=\"text-center py-16\">\n          <div className=\"relative inline-block mb-4\">\n            <div className=\"absolute inset-0 bg-slate-500/10 rounded-xl blur-sm\"></div>\n            <div className=\"relative p-6 bg-gradient-to-br from-slate-500/10 to-slate-500/5 rounded-xl border border-slate-200/30\">\n              <Heart className=\"h-12 w-12 text-slate-400 mx-auto\" />\n            </div>\n          </div>\n          <h3 className=\"text-lg font-medium text-slate-900 mb-2\">\n            {searchTerm || filterCategory !== \"all\" ? \"没有找到相关收藏\" : \"暂无收藏内容\"}\n          </h3>\n          <p className=\"text-slate-600 mb-6\">\n            {searchTerm || filterCategory !== \"all\" \n              ? \"尝试调整搜索条件或筛选选项\" \n              : \"还没有收藏任何服务，快去发现优质服务吧！\"\n            }\n          </p>\n          {!searchTerm && filterCategory === \"all\" && (\n            <Button \n              onClick={() => window.location.href = \"/services\"}\n              className=\"bg-blue-600 hover:bg-blue-700\"\n            >\n              去逛逛\n            </Button>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;AAGO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,YAAY;IACZ,MAAM,6BAA6B,CAAA,GAAA,4RAAA,CAAA,UAAO,AAAD;oEAAE;YACzC,IAAI,WAAW,+HAAA,CAAA,gBAAa;YAE5B,OAAO;YACP,IAAI,YAAY;gBACd,MAAM,kBAAkB,WAAW,WAAW;gBAC9C,WAAW,SAAS,MAAM;gFAAC,CAAC,OAC1B,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,oBAClC,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,oBACxC,KAAK,OAAO,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;;YAE7C;YAEA,OAAO;YACP,IAAI,mBAAmB,OAAO;gBAC5B,WAAW,SAAS,MAAM;gFAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;;YACvD;YAEA,KAAK;YACL,MAAM,SAAS;mBAAI;aAAS,CAAC,IAAI;mFAAC,CAAC,GAAG;oBACpC,OAAQ;wBACN,KAAK;4BACH,OAAO,IAAI,KAAK,EAAE,OAAO,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,OAAO,EAAE,OAAO;wBACpE,KAAK;4BACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;wBAC1B,KAAK;4BACH,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;wBAC1B,KAAK;4BACH,OAAO,EAAE,MAAM,GAAG,EAAE,MAAM;wBAC5B;4BACE,OAAO;oBACX;gBACF;;YAEA,OAAO;QACT;mEAAG;QAAC;QAAY;QAAQ;KAAe;IAEvC,SAAS;IACT,MAAM,aAAa;WAAI,IAAI,IAAI,+HAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,QAAQ;KAAG;IAEzE,MAAM,uBAAuB,CAAC;QAC5B,QAAQ,GAAG,CAAC,AAAC,SAAW,OAAH;IACrB,kBAAkB;IACpB;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YAAE,OAAO;YAAS,KAAK;QAAU;IAC3E;IAEA,MAAM,oBAAoB,CAAC,wBACzB,4TAAC,mIAAA,CAAA,OAAI;YAAkB,WAAU;sBAC/B,cAAA,4TAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCACC,KAAK,QAAQ,KAAK;gCAClB,KAAK,QAAQ,KAAK;gCAClB,WAAU;;;;;;0CAIZ,4TAAC;gCACC,SAAS,IAAM,qBAAqB,QAAQ,EAAE;gCAC9C,WAAU;0CAEV,cAAA,4TAAC,2RAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;4BAIlB,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,MAAM,GAAG,mBACrC,4TAAC;gCAAI,WAAU;0CACZ,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK;oCACtB,MAAM,YAAY;wCAChB,SAAS;wCACT,SAAS;wCACT,QAAQ;oCACV;oCACA,qBACE,4TAAC,oIAAA,CAAA,QAAK;wCAEJ,WAAW,AAAC,gCAAoG,OAArE,SAAS,CAAC,IAA8B,IAAI;kDAEtF;uCAHI;;;;;gCAMX;;;;;;;;;;;;kCAMN,4TAAC;wBAAI,WAAU;;0CAEb,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,4TAAC,qIAAA,CAAA,cAAW;gDAAC,KAAK,QAAQ,OAAO,CAAC,MAAM;;;;;;0DACxC,4TAAC,qIAAA,CAAA,iBAAc;gDAAC,WAAU;0DAAW,QAAQ,OAAO,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;kDAE9D,4TAAC;wCAAK,WAAU;kDAAmC,QAAQ,OAAO,CAAC,IAAI;;;;;;oCACtE,QAAQ,OAAO,CAAC,QAAQ,kBACvB,4TAAC;wCAAI,WAAU;;;;;;kDAEjB,4TAAC;wCAAI,WAAU;;0DACb,4TAAC,iSAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CACjB,QAAQ,OAAO,CAAC,QAAQ;;;;;;;;;;;;;0CAK7B,4TAAC;gCAAG,WAAU;0CACX,QAAQ,KAAK;;;;;;0CAIhB,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAI,WAAU;;0DACb,4TAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,4TAAC;gDAAK,WAAU;0DAAsC,QAAQ,MAAM;;;;;;;;;;;;kDAEtE,4TAAC;wCAAK,WAAU;;4CAAyB;4CAAE,QAAQ,WAAW;4CAAC;;;;;;;;;;;;;0CAIjE,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAI,WAAU;;0DACb,4TAAC;gDAAK,WAAU;;oDAAuC;oDAAE,QAAQ,KAAK;;;;;;;4CACrE,QAAQ,aAAa,kBACpB,4TAAC;gDAAK,WAAU;;oDAAsC;oDAAE,QAAQ,aAAa;;;;;;;;;;;;;kDAGjF,4TAAC;wCAAI,WAAU;;0DACb,4TAAC,iSAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CACnB,WAAW,QAAQ,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;WAlF1B,QAAQ,EAAE;;;;;IA0FvB,qBACE,4TAAC;QAAI,WAAU;;0BAEb,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;kDACb,4TAAC;wCAAI,WAAU;;;;;;kDACf,4TAAC;wCAAI,WAAU;kDACb,cAAA,4TAAC,2RAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGrB,4TAAC;;kDACC,4TAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,4TAAC;wCAAE,WAAU;;4CAAiB;4CAAK,+HAAA,CAAA,gBAAa,CAAC,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;kCAK5D,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC;oCAAI,WAAU;;sDACb,4TAAC,6RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,4TAAC,oIAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;;;;;;;;;;;;;;;;;0CAKhB,4TAAC;gCAAI,WAAU;;kDACb,4TAAC,qIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAgB,eAAe;;0DAC5C,4TAAC,qIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,4TAAC,qIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,4TAAC,qIAAA,CAAA,gBAAa;;kEACZ,4TAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;oDACvB,WAAW,GAAG,CAAC,CAAA,yBACd,4TAAC,qIAAA,CAAA,aAAU;4DAAgB,OAAO;sEAAW;2DAA5B;;;;;;;;;;;;;;;;;kDAKvB,4TAAC,qIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAQ,eAAe;;0DACpC,4TAAC,qIAAA,CAAA,gBAAa;gDAAC,WAAU;0DACvB,cAAA,4TAAC,qIAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,4TAAC,qIAAA,CAAA,gBAAa;;kEACZ,4TAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,4TAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAY;;;;;;kEAC9B,4TAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAa;;;;;;kEAC/B,4TAAC,qIAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQpC,2BAA2B,MAAM,GAAG,kBACnC,4TAAC;gBAAI,WAAU;0BACZ,2BAA2B,GAAG,CAAC;;;;;qCAGlC,4TAAC;gBAAI,WAAU;;kCACb,4TAAC;wBAAI,WAAU;;0CACb,4TAAC;gCAAI,WAAU;;;;;;0CACf,4TAAC;gCAAI,WAAU;0CACb,cAAA,4TAAC,2RAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAGrB,4TAAC;wBAAG,WAAU;kCACX,cAAc,mBAAmB,QAAQ,aAAa;;;;;;kCAEzD,4TAAC;wBAAE,WAAU;kCACV,cAAc,mBAAmB,QAC9B,kBACA;;;;;;oBAGL,CAAC,cAAc,mBAAmB,uBACjC,4TAAC,qIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;wBACtC,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAQb;GAhPgB;KAAA", "debugId": null}}, {"offset": {"line": 2592, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/app/favorites/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { NavigationV78 } from \"@/components/NavigationV78\";\nimport { ChatWindow } from \"@/components/ChatWindow\";\nimport { ChatFloatingButton } from \"@/components/ChatFloatingButton\";\nimport { FavoritesPageContent } from \"@/components/FavoritesPage\";\nimport { useRouter } from \"next/navigation\";\n\nexport default function Favorites() {\n  const router = useRouter();\n  const [isChatOpen, setIsChatOpen] = useState(false);\n  const [notificationCount] = useState(3);\n\n  const handleNavigate = (page: string) => {\n    switch (page) {\n      case \"home\":\n      case \"landing\":\n        router.push(\"/\");\n        break;\n      case \"services\":\n        router.push(\"/services\");\n        break;\n      case \"orders\":\n        router.push(\"/orders\");\n        break;\n      case \"favorites\":\n        router.push(\"/favorites\");\n        break;\n      case \"support\":\n        router.push(\"/support\");\n        break;\n      case \"gallery\":\n        router.push(\"/gallery\");\n        break;\n      case \"login\":\n        router.push(\"/login\");\n        break;\n      case \"register\":\n        router.push(\"/register\");\n        break;\n      default:\n        break;\n    }\n  };\n\n  const handleOpenChat = () => {\n    setIsChatOpen(true);\n  };\n\n  const handleCloseChat = () => {\n    setIsChatOpen(false);\n  };\n\n  const handleMinimizeChat = () => {\n    setIsChatOpen(false);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30\">\n      <NavigationV78 \n        currentPage=\"favorites\" \n        onNavigate={handleNavigate}\n        onOpenChat={handleOpenChat}\n      />\n      \n      <FavoritesPageContent />\n\n      {/* 聊天窗口 */}\n      <ChatWindow isOpen={isChatOpen} onClose={handleCloseChat} onMinimize={handleMinimizeChat} />\n      \n      {/* 聊天悬浮球 */}\n      <ChatFloatingButton \n        onClick={handleOpenChat}\n        notificationCount={notificationCount}\n        isVisible={!isChatOpen}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,oQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,GAAG,CAAA,GAAA,4RAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;g<PERSON><PERSON>,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF;gBACE;QACJ;IACF;IAEA,MAAM,iBAAiB;QACrB,cAAc;IAChB;IAEA,MAAM,kBAAkB;QACtB,cAAc;IAChB;IAEA,MAAM,qBAAqB;QACzB,cAAc;IAChB;IAEA,qBACE,4TAAC;QAAI,WAAU;;0BACb,4TAAC,sIAAA,CAAA,gBAAa;gBACZ,aAAY;gBACZ,YAAY;gBACZ,YAAY;;;;;;0BAGd,4TAAC,sIAAA,CAAA,uBAAoB;;;;;0BAGrB,4TAAC,mIAAA,CAAA,aAAU;gBAAC,QAAQ;gBAAY,SAAS;gBAAiB,YAAY;;;;;;0BAGtE,4TAAC,2IAAA,CAAA,qBAAkB;gBACjB,SAAS;gBACT,mBAAmB;gBACnB,WAAW,CAAC;;;;;;;;;;;;AAIpB;GAtEwB;;QACP,oQAAA,CAAA,YAAS;;;KADF", "debugId": null}}]}