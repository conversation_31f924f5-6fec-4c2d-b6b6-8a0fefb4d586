import { Order, StatusCounts } from "../types/order";

export const getStatusCounts = (orders: Order[]): StatusCounts => {
  return {
    total: orders.length,
    pending: orders.filter(o => o.status === "pending").length,
    processing: orders.filter(o => o.status === "processing").length,
    shipped: orders.filter(o => o.status === "shipped").length,
    delivered: orders.filter(o => o.status === "delivered").length,
    cancelled: orders.filter(o => o.status === "cancelled").length,
    aftersale: orders.filter(o => o.status === "aftersale").length,
  };
};

export const filterOrdersBySearch = (orders: Order[], searchTerm: string): Order[] => {
  if (!searchTerm) return orders;
  
  const lowerSearchTerm = searchTerm.toLowerCase();
  return orders.filter((order) => {
    return (
      order.orderNumber.toLowerCase().includes(lowerSearchTerm) ||
      order.items.some(item => 
        item.name.toLowerCase().includes(lowerSearchTerm)
      ) ||
      order.merchant.name.toLowerCase().includes(lowerSearchTerm)
    );
  });
};

export const filterOrdersByStatus = (orders: Order[], statusFilter: string): Order[] => {
  if (statusFilter === "all") return orders;
  return orders.filter(order => order.status === statusFilter);
};

export const filterOrdersByDate = (orders: Order[], dateFilter: string): Order[] => {
  if (dateFilter === "all") return orders;
  
  const now = new Date();
  return orders.filter(order => {
    const orderDate = new Date(order.date);
    const diffTime = now.getTime() - orderDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    switch (dateFilter) {
      case "recent":  
        return diffDays <= 30;
      case "quarter":
        return diffDays <= 90;
      case "year":
        return diffDays <= 365;
      default:
        return true;
    }
  });
};

export const filterOrders = (
  orders: Order[], 
  searchTerm: string, 
  statusFilter: string, 
  dateFilter: string
): Order[] => {
  let filtered = orders;
  filtered = filterOrdersBySearch(filtered, searchTerm);
  filtered = filterOrdersByStatus(filtered, statusFilter);
  filtered = filterOrdersByDate(filtered, dateFilter);
  return filtered;
};