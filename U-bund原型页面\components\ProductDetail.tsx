import { useState, useRef } from "react";
import { But<PERSON> } from "./ui/button";
import { ImageWithFallback } from "./figma/ImageWithFallback";
import { Card, CardContent } from "./ui/card";
import { Badge } from "./ui/badge";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "./ui/avatar";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "./ui/breadcrumb";
import {
  Star,
  Heart,
  ChevronLeft,
  ChevronRight,
  Play,
  Check,
  ArrowLeft,
  MapPin,
  Shield,
  Clock,
  RefreshCw,
  Award,
  MessageCircle,
  Share2,
  Flag,
  IdCard,
} from "lucide-react";

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  rating: number;
  reviews: number;
  category: string;
  tags: string[];
  isBestseller?: boolean;
  isNew?: boolean;
  creator: {
    name: string;
    avatar: string;
    verified?: boolean;
    location?: string;
    isOnline?: boolean;
    averageResponseTime?: string; // 平均回复时长，如 "3分钟"
    activeOrdersCount?: number; // 正在处理的订单数量
    lastSeen?: string; // 最后在线时间，如 "2小时前"
  };
  favoriteCount: number;
  specialBadge?: "upro" | "top" | "star";
  images?: string[];
  packages?: {
    basic: {
      name: string;
      price: number;
      features: string[];
      timeInfo?: string;
    };
    standard: {
      name: string;
      price: number;
      features: string[];
      timeInfo?: string;
    };
    premium: {
      name: string;
      price: number;
      features: string[];
      timeInfo?: string;
    };
  };
}

interface ProductDetailProps {
  product: Product;
  onBack: () => void;
}

export function ProductDetail({
  product,
  onBack,
}: ProductDetailProps) {
  const [selectedPackage, setSelectedPackage] = useState<
    "basic" | "standard" | "premium"
  >("standard");
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isFavorited, setIsFavorited] = useState(false);
  const [companyScrollIndex, setCompanyScrollIndex] =
    useState(0);
  const companyScrollRef = useRef<HTMLDivElement>(null);

  // 模拟多张图片数据
  const productImages = product.images || [
    product.image,
    "https://images.unsplash.com/photo-1542744094-3a31f272c490?w=600&h=600&fit=crop",
    "https://images.unsplash.com/photo-1558655146-364adaf1fcc9?w=600&h=600&fit=crop",
    "https://images.unsplash.com/photo-1586717799252-bd134ad00e26?w=600&h=600&fit=crop",
    "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600&h=600&fit=crop",
    "https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=600&h=600&fit=crop",
  ];

  // 模拟套餐数据
  const packages = product.packages || {
    basic: {
      name: "基础服务",
      price: product.price,
      features: [
        "2个基础设计方案",
        "设计矢量文件",
        "可打印文件",
        "源文件提供",
        "2次修改机会",
        "可商用素材",
      ],
      timeInfo: "3天草图方案",
    },
    standard: {
      name: "标准服务",
      price: Math.round(product.price * 1.5),
      features: [
        "4个基础设计方案",
        "设计矢量文件",
        "可打印文件",
        "源文件提供",
        "2次修改机会",
        "可商用素材",
      ],
      timeInfo: "3天设计时间，7天交付时间",
    },
    premium: {
      name: "高级服务",
      price: Math.round(product.price * 2.5),
      features: [
        "6个基础设计方案",
        "设计矢量文件",
        "可打印文件",
        "源文件提供",
        "2次修改机会",
        "可商用素材",
      ],
      timeInfo: "1天快速启动，3天交付时间",
    },
  };

  // 生成社会证明标签数据
  const socialProofTags = [
    `多人评价"性价比超高"`,
    `超${Math.floor(product.reviews / 100)}千回头客`,
    `超${Math.floor(product.favoriteCount / 100)}万人加购`,
    `${product.creator.location}地区热销`,
  ];

  const nextImage = () => {
    setCurrentImageIndex(
      (prev) => (prev + 1) % productImages.length,
    );
  };

  const prevImage = () => {
    setCurrentImageIndex(
      (prev) =>
        (prev - 1 + productImages.length) %
        productImages.length,
    );
  };

  const selectedPackageInfo = packages[selectedPackage];

  // 处理操作按钮点击
  const handleFavoriteClick = () => {
    setIsFavorited(!isFavorited);
    console.log(isFavorited ? "取消收藏" : "添加收藏");
  };

  const handleShareClick = () => {
    console.log("分享商品");
  };

  const handleReportClick = () => {
    console.log("举报商品");
  };

  // 公司项目数据
  const companyProjects = [
    { name: "阿里巴巴", shortName: "阿", color: "bg-blue-600" },
    { name: "腾讯", shortName: "腾", color: "bg-green-600" },
    { name: "京东", shortName: "京", color: "bg-red-600" },
    {
      name: "滴滴出行",
      shortName: "滴",
      color: "bg-orange-600",
    },
    { name: "美团", shortName: "美", color: "bg-purple-600" },
    {
      name: "字节跳动",
      shortName: "字",
      color: "bg-indigo-600",
    },
    { name: "百度", shortName: "百", color: "bg-yellow-600" },
    { name: "网易", shortName: "网", color: "bg-pink-600" },
  ];

  // 处理公司项目滚动
  const scrollCompanyProjects = (
    direction: "left" | "right",
  ) => {
    const maxIndex = Math.max(0, companyProjects.length - 3);

    if (direction === "left") {
      setCompanyScrollIndex((prev) => Math.max(0, prev - 1));
    } else {
      setCompanyScrollIndex((prev) =>
        Math.min(maxIndex, prev + 1),
      );
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-6">
      {/* 顶部导航区域 */}
      <div className="mb-4 flex items-center justify-between">
        {/* 面包屑导航 */}
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink
                href="#"
                onClick={(e) => {
                  e.preventDefault();
                  onBack();
                }}
                className="flex items-center gap-1 text-slate-500 hover:text-slate-700 transition-colors cursor-pointer"
              >
                <ArrowLeft className="h-4 w-4" />
                返回
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-slate-900">
                {product.name}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* 操作按钮组 */}
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleFavoriteClick}
            className={`flex items-center gap-1 px-3 py-1.5 rounded-lg border transition-all duration-200 ${
              isFavorited
                ? "bg-red-50 border-red-200 text-red-600 hover:bg-red-100"
                : "bg-white border-slate-200 text-slate-600 hover:bg-slate-50"
            }`}
          >
            <Heart
              className={`h-4 w-4 ${isFavorited ? "fill-current" : ""}`}
            />
            <span className="text-sm">收藏</span>
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleShareClick}
            className="flex items-center gap-1 px-3 py-1.5 rounded-lg border bg-white border-slate-200 text-slate-600 hover:bg-slate-50 transition-all duration-200"
          >
            <Share2 className="h-4 w-4" />
            <span className="text-sm">分享</span>
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={handleReportClick}
            className="flex items-center gap-1 px-3 py-1.5 rounded-lg border bg-white border-slate-200 text-slate-600 hover:bg-slate-50 transition-all duration-200"
          >
            <Flag className="h-4 w-4" />
            <span className="text-sm">举报</span>
          </Button>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-[61.8%_38.2%] gap-8 mb-12">
        {/* 左侧 - 标题和图片展示区 */}
        <div className="space-y-6">
          {/* 商品标题和社会证明标签 */}
          <div>
            <h1 className="text-slate-900 font-bold text-3xl mb-4 bg-gradient-to-r from-slate-900 via-slate-800 to-slate-700 bg-clip-text text-transparent leading-tight tracking-tight">
              {product.name}
            </h1>

            {/* 社会证明标签 */}
            <div className="flex flex-wrap gap-2 mb-6">
              {socialProofTags.map((tag, index) => (
                <span
                  key={index}
                  className="inline-block px-3 py-1 bg-slate-100 text-slate-600 text-sm rounded-full"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>

          {/* 主图展示 */}
          <div className="relative aspect-square bg-slate-100 rounded-2xl overflow-hidden group">
            <ImageWithFallback
              src={productImages[currentImageIndex]}
              alt={product.name}
              className="w-full h-full object-cover"
            />

            {/* 播放按钮覆盖层 */}
            <div className="absolute inset-0 flex items-center justify-center bg-black/20 opacity-0 hover:opacity-100 transition-opacity duration-300">
              <Button
                size="lg"
                className="w-16 h-16 rounded-full bg-white/90 hover:bg-white text-slate-700 hover:text-slate-900 shadow-lg backdrop-blur-sm"
              >
                <Play className="h-6 w-6 ml-1" />
              </Button>
            </div>

            {/* 导航按钮 */}
            {productImages.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white backdrop-blur-sm rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white backdrop-blur-sm rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </>
            )}
          </div>

          {/* 圆点指示器 */}
          <div className="flex justify-center gap-2">
            {productImages.map((_, index) => (
              <button
                key={index}
                className={`w-2.5 h-2.5 rounded-full transition-all duration-300 ${
                  index === currentImageIndex
                    ? "bg-slate-900 scale-125"
                    : "bg-slate-300 hover:bg-slate-400"
                }`}
                onClick={() => setCurrentImageIndex(index)}
              />
            ))}
          </div>
        </div>

        {/* 右侧 - 商品信息 */}
        <div className="space-y-6">
          {/* 创作者信息 */}
          <div className="flex items-center gap-3 mb-6">
            <div className="relative">
              <Avatar className="w-12 h-12">
                <AvatarImage src={product.creator.avatar} />
                <AvatarFallback>
                  {product.creator.name[0]}
                </AvatarFallback>
              </Avatar>
              {/* 在线状态指示器 */}
              <div
                className={`absolute -top-0.5 -right-0.5 w-3.5 h-3.5 rounded-full border-2 border-white ${
                  product.creator.isOnline
                    ? "bg-green-500"
                    : "bg-slate-400"
                }`}
              ></div>
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                <span className="font-medium text-slate-900">
                  {product.creator.name}
                </span>
                {product.creator.verified && (
                  <Badge
                    variant="secondary"
                    className="text-xs p-1.5"
                  >
                    <IdCard className="h-3 w-3" />
                  </Badge>
                )}
                {product.specialBadge === "upro" && (
                  <Badge
                    variant="secondary"
                    className="text-xs px-2 py-1 bg-gradient-to-r from-purple-500 to-purple-600 text-white"
                  >
                    U pro
                  </Badge>
                )}
                {(product.specialBadge === "star" ||
                  product.specialBadge === "top") && (
                  <Badge
                    variant="secondary"
                    className="text-xs px-2 py-1 bg-gradient-to-r from-orange-500 to-orange-600 text-white"
                  >
                    好评之星
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-4 text-sm text-slate-600">
                <div className="flex items-center gap-1">
                  <Star className="h-3 w-3 text-yellow-500 fill-current" />
                  <span>{product.rating}</span>
                </div>
                <div className="flex items-center gap-1">
                  <MessageCircle className="h-3 w-3" />
                  <span>{product.reviews} 评价</span>
                </div>
                {product.creator.location && (
                  <div className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    <span>{product.creator.location}</span>
                  </div>
                )}
              </div>
            </div>

            {/* 右侧状态信息 */}
            <div className="text-right text-xs whitespace-nowrap">
              {product.creator.isOnline ? (
                <div className="space-y-0.5">
                  <div className="text-slate-500">
                    目前正在处理{" "}
                    {product.creator.activeOrdersCount || 3}{" "}
                    个订单
                  </div>
                  <div className="text-slate-600">
                    平均回复时长:{" "}
                    {product.creator.averageResponseTime ||
                      "3分钟"}
                  </div>
                </div>
              ) : (
                <div className="text-slate-500">
                  {product.creator.lastSeen || "几小时前"} 在线
                </div>
              )}
            </div>
          </div>

          {/* 服务过的公司项目 */}
          <div className="bg-slate-50/30 border border-slate-200/40 rounded-xl p-4">
            <h4 className="font-medium text-slate-900 mb-3">
              服务过的公司项目
            </h4>

            <div className="relative overflow-hidden">
              {/* 左侧滚动箭头 */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => scrollCompanyProjects("left")}
                disabled={companyScrollIndex === 0}
                className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 h-8 w-8 p-0 rounded-full bg-white/80 hover:bg-white shadow-md disabled:opacity-30 disabled:shadow-none"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              {/* 右侧滚动箭头 */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => scrollCompanyProjects("right")}
                disabled={
                  companyScrollIndex >=
                  companyProjects.length - 3
                }
                className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 h-8 w-8 p-0 rounded-full bg-white/80 hover:bg-white shadow-md disabled:opacity-30 disabled:shadow-none"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>

              <div
                ref={companyScrollRef}
                className="flex gap-3 transition-transform duration-300 ease-out px-10"
                style={{
                  transform: `translateX(-${companyScrollIndex * 33.333}%)`,
                }}
              >
                {companyProjects.map((company, index) => (
                  <div
                    key={index}
                    className="flex items-center gap-3 flex-shrink-0"
                    style={{ width: "calc(33.333% - 8px)" }}
                  >
                    <div
                      className={`w-8 h-8 ${company.color} rounded-md flex items-center justify-center`}
                    >
                      <span className="text-white text-xs font-bold">
                        {company.shortName}
                      </span>
                    </div>
                    <span className="text-xs text-slate-600 flex-1">
                      {company.name}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 套餐选择 */}
          <div>
            <Card className="bg-slate-50/30 border-slate-200/60 p-4 rounded-[28px]">
              <div className="flex gap-2">
                {Object.entries(packages).map(([key, pkg]) => (
                  <Button
                    key={key}
                    variant="ghost"
                    className={`flex-1 py-3 rounded-full font-medium transition-all duration-200 ${
                      selectedPackage === key
                        ? "bg-slate-900 text-white hover:bg-slate-800"
                        : "bg-slate-100 text-slate-700 hover:bg-slate-200"
                    }`}
                    onClick={() =>
                      setSelectedPackage(key as any)
                    }
                  >
                    {pkg.name}
                  </Button>
                ))}
              </div>
            </Card>
          </div>

          {/* 价格显示 */}
          <div className="py-2">
            <div className="text-2xl font-bold text-slate-900">
              RMB {selectedPackageInfo.price} 起
            </div>
            <div className="text-sm text-slate-600 mt-1">
              {selectedPackageInfo.name} •{" "}
              {selectedPackageInfo.timeInfo ||
                "预计交付 3-7 天"}{" "}
              • 5天交付
            </div>
          </div>

          {/* 包含内容 */}
          <div className="space-y-3">
            <h4 className="font-medium text-slate-900">
              包含内容
            </h4>
            <div className="space-y-2">
              {selectedPackageInfo.features.map(
                (feature, index) => {
                  // 判断是否是可商用素材，基础服务用灰色，标准和高级用绿色
                  const isCommercialMaterial =
                    feature === "可商用素材";
                  const isBasicPackage =
                    selectedPackage === "basic";
                  const checkColor =
                    isCommercialMaterial && isBasicPackage
                      ? "text-slate-400"
                      : "text-green-600";

                  return (
                    <div
                      key={index}
                      className="flex items-center gap-2 text-sm text-slate-600"
                    >
                      <Check
                        className={`h-4 w-4 ${checkColor}`}
                      />
                      <span>{feature}</span>
                    </div>
                  );
                },
              )}
            </div>
          </div>

          {/* 服务保障 */}
          <div className="bg-slate-50/50 border border-slate-200/60 rounded-xl p-4">
            <h4 className="font-medium text-slate-900 mb-3">
              服务保障
            </h4>
            <div className="grid grid-cols-2 gap-3">
              <div className="text-sm text-slate-600">
                <span>作品认证</span>
              </div>
              <div className="text-sm text-slate-600">
                <span>可提供售后服务</span>
              </div>
              <div className="text-sm text-slate-600">
                <span>技能认证</span>
              </div>
              <div className="text-sm text-slate-600">
                <span>双方金钱保护</span>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="space-y-3">
            <Button
              className="w-full h-12 bg-slate-700 hover:bg-slate-800 text-white"
              onClick={handleFavoriteClick}
            >
              <Heart
                className={`h-4 w-4 mr-2 ${isFavorited ? "fill-current" : ""}`}
              />
              {isFavorited ? "已添加收藏" : "添加收藏"}
            </Button>
            <Button className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white">
              立即下单
            </Button>
          </div>
        </div>
      </div>

      {/* 商品展示介绍 */}
      <div className="grid grid-cols-1 lg:grid-cols-[61.8%_38.2%] gap-8 mb-8">
        <Card>
          <CardContent className="p-8">
            <h2 className="text-slate-900 mb-6 text-center">
              商品展示介绍
            </h2>
            <div className="bg-slate-100 rounded-xl h-96 flex items-center justify-center text-slate-500">
              <div className="text-center">
                <div className="text-lg mb-2">服务详细介绍</div>
                <div className="text-sm">
                  这里将展示服务的详细说明、案例展示、制作流程等内容
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        <div></div>
      </div>

      {/* 评论展示 */}
      <div className="grid grid-cols-1 lg:grid-cols-[61.8%_38.2%] gap-8 mb-8">
        <Card>
          <CardContent className="p-8">
            <h2 className="text-slate-900 mb-6 text-center">
              用户评价
            </h2>
            <div className="bg-slate-100 rounded-xl h-64 flex items-center justify-center text-slate-500">
              <div className="text-center">
                <div className="text-lg mb-2">用户评价展示</div>
                <div className="text-sm">
                  这里将展示真实用户的评价和反馈
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        <div></div>
      </div>

      {/* 其他商品展示 */}
      <div>
        <h2 className="text-slate-900 mb-6 text-center">
          其他商品推荐
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[1, 2, 3].map((item) => (
            <Card
              key={item}
              className="bg-slate-100 cursor-pointer hover:shadow-lg transition-shadow"
            >
              <CardContent className="p-6">
                <div className="aspect-square bg-slate-200 rounded-lg mb-4 flex items-center justify-center text-slate-500">
                  <div className="text-center">
                    <div className="text-sm">其他商品展示</div>
                  </div>
                </div>
                <h3 className="font-medium text-slate-900 mb-2">
                  推荐服务 {item}
                </h3>
                <div className="text-sm text-slate-600">
                  RMB 999 起
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}