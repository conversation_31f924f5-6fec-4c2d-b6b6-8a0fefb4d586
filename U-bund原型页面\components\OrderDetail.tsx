import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header } from "./ui/card";
import { OrderStatusBadge } from "./OrderStatusBadge";
import { Button } from "./ui/button";
import { ImageWithFallback } from "./figma/ImageWithFallback";
import { WorkProgressBar } from "./WorkProgressBar";
import { 
  ArrowLeft, 
  Package, 
  Truck, 
  MapPin, 
  CreditCard, 
  Phone, 
  MessageCircle, 
  Calendar,
  Clock,
  User,
  Receipt,
  Shield,
  Star,
  RotateCcw,
  Copy,
  Check,
  Users,
  Play,
  Search,
  Package2
} from "lucide-react";
import { Badge } from "./ui/badge";
import { Separator } from "./ui/separator";

interface OrderItem {
  id: string;
  name: string;
  image: string;
  quantity: number;
  price: number;
  specifications?: string[];
}

interface Merchant {
  id: string;
  name: string;
  avatar: string;
  isOnline: boolean;
  phone?: string;
  rating?: number;
}

interface WorkProgressStep {
  id: string;
  label: string;
  description: string;
  status: "completed" | "current" | "pending";
  completedAt?: string;
}

interface WorkProgress {
  currentStep: number;
  steps: WorkProgressStep[];
}

interface Order {
  id: string;
  orderNumber: string;
  date: string;
  status: "pending" | "processing" | "shipped" | "delivered" | "cancelled";
  items: OrderItem[];
  total: number;
  shippingAddress: string;
  completedAt?: string;
  estimatedCompletionTime?: string;
  merchant: Merchant;
  paymentMethod: string;
  shippingFee: number;
  discountAmount?: number;
  trackingNumber?: string;
  workProgress: WorkProgress;
}

interface OrderDetailProps {
  order: Order;
  onBack: () => void;
}

export function OrderDetail({ order, onBack }: OrderDetailProps) {
  const [copiedOrderNumber, setCopiedOrderNumber] = useState(false);
  const [copiedTrackingNumber, setCopiedTrackingNumber] = useState(false);

  const formatPrice = (price: number) => {
    return `¥${price.toFixed(2)}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const copyToClipboard = async (text: string, type: 'order' | 'tracking') => {
    try {
      await navigator.clipboard.writeText(text);
      if (type === 'order') {
        setCopiedOrderNumber(true);
        setTimeout(() => setCopiedOrderNumber(false), 2000);
      } else {
        setCopiedTrackingNumber(true);
        setTimeout(() => setCopiedTrackingNumber(false), 2000);
      }
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  const handleContactMerchant = () => {
    console.log(`联系商家：${order.merchant.name}`);
  };

  const handleCallMerchant = () => {
    console.log(`拨打商家电话：${order.merchant.phone}`);
  };

  const handleInvoiceRequest = () => {
    console.log(`开发票请求：订单 ${order.orderNumber}`);
  };

  const handleAfterSalesRequest = () => {
    console.log(`申请售后：订单 ${order.orderNumber}`);
  };

  const handleRateOrder = () => {
    console.log(`评价订单：订单 ${order.orderNumber}`);
  };

  const handleReorder = () => {
    console.log(`重新下单：订单 ${order.orderNumber}`);
  };

  const handlePayOrder = () => {
    console.log(`立即付款：订单 ${order.orderNumber}`);
  };

  const handleConfirmReceipt = () => {
    console.log(`确认收货：订单 ${order.orderNumber}`);
  };

  const isCustomService = order.shippingAddress === "";
  const isIncompleteOrder = ["pending", "processing", "shipped"].includes(order.status);

  // 为WorkProgressBar准备数据，添加图标
  const progressStepsWithIcons = order.workProgress.steps.map(step => ({
    ...step,
    icon: step.id === "1" ? Users : 
          step.id === "2" ? Play : 
          step.id === "3" ? Search : 
          Package2
  }));

  return (
    <div>
      {/* 页面标题区域 */}
      <div className="bg-white/70 backdrop-blur-xl border-b border-slate-200/60">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center gap-4">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={onBack}
              className="h-10 w-10 p-0 hover:bg-slate-100/80 transition-colors duration-200"
            >
              <ArrowLeft className="h-5 w-5 text-slate-600" />
            </Button>
            <div>
              <h1 className="text-slate-900">订单详情</h1>
              <p className="text-slate-600 text-sm">{order.orderNumber}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-6 space-y-6 bg-gradient-to-br from-slate-50 via-white to-slate-50/30 min-h-screen">
        {/* 订单状态卡片 */}
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200/60 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-slate-50/30 via-transparent to-blue-50/20" />
          <CardHeader className="relative">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <div className="absolute inset-0 bg-blue-500/10 rounded-xl blur-sm"></div>
                  <div className="relative p-3 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-xl border border-blue-200/30">
                    <Package className="h-6 w-6 text-blue-600" />
                  </div>
                </div>
                <div>
                  <h2 className="text-slate-900">订单状态</h2>
                  <div className="flex items-center gap-2 mt-1">
                    <button
                      onClick={() => copyToClipboard(order.orderNumber, 'order')}
                      className="flex items-center gap-1.5 text-sm text-slate-600 hover:text-slate-900 transition-colors"
                    >
                      <span>订单号：{order.orderNumber}</span>
                      {copiedOrderNumber ? (
                        <Check className="h-3 w-3 text-green-600" />
                      ) : (
                        <Copy className="h-3 w-3" />
                      )}
                    </button>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <OrderStatusBadge status={order.status} />
                <p className="text-sm text-slate-500 mt-1">
                  下单时间：{formatDate(order.date)}
                </p>
                {/* 时间信息 */}
                {isIncompleteOrder && order.estimatedCompletionTime && (
                  <div className="flex items-center justify-end gap-1.5 text-xs text-slate-500 bg-slate-50/80 px-2 py-1 rounded-full mt-2">
                    <Clock className="h-3 w-3" />
                    <span>预计 {formatDate(order.estimatedCompletionTime)} 完成</span>
                  </div>
                )}
                {order.status === "delivered" && order.completedAt && (
                  <div className="text-xs text-slate-500 bg-green-50/80 px-2 py-1 rounded-full mt-2">
                    {formatDate(order.completedAt)} 完成
                  </div>
                )}
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* 商家信息卡片 */}
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200/60">
          <CardHeader>
            <h3 className="text-slate-900 flex items-center gap-2">
              <User className="h-4 w-4" />
              商家信息
            </h3>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <ImageWithFallback
                    src={order.merchant.avatar}
                    alt={order.merchant.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div className={`absolute -top-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ${
                    order.merchant.isOnline 
                      ? 'bg-green-500' 
                      : 'bg-slate-400'
                  }`} />
                </div>
                <div>
                  <h4 className="text-slate-900">{order.merchant.name}</h4>
                  <div className="flex items-center gap-3 mt-1">
                    <Badge 
                      variant="secondary" 
                      className={`${order.merchant.isOnline ? 'bg-green-50 text-green-700' : 'bg-slate-50 text-slate-600'}`}
                    >
                      {order.merchant.isOnline ? '在线' : '离线'}
                    </Badge>
                    {order.merchant.rating && (
                      <div className="flex items-center gap-1 text-sm text-slate-600">
                        <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                        <span>{order.merchant.rating.toFixed(1)}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleContactMerchant}
                  className="bg-white/80 hover:bg-white border-slate-200/80"
                >
                  <MessageCircle className="h-4 w-4 mr-2" />
                  联系商家
                </Button>
                {order.merchant.phone && (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    onClick={handleCallMerchant}
                    className="bg-white/80 hover:bg-white border-slate-200/80"
                  >
                    <Phone className="h-4 w-4 mr-2" />
                    拨打电话
                  </Button>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 商品信息卡片 */}
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200/60">
          <CardHeader>
            <h3 className="text-slate-900 flex items-center gap-2">
              <Package className="h-4 w-4" />
              商品详情
            </h3>
          </CardHeader>
          <CardContent className="space-y-4">
            {order.items.map((item, index) => (
              <div key={item.id}>
                {index > 0 && <Separator className="my-4" />}
                <div className="flex gap-4">
                  <div className="relative">
                    <ImageWithFallback
                      src={item.image}
                      alt={item.name}
                      className="w-20 h-20 rounded-lg object-cover bg-slate-100 border border-slate-200/60"
                    />
                  </div>
                  <div className="flex-1 space-y-2">
                    <h4 className="text-slate-900">{item.name}</h4>
                    {item.specifications && (
                      <div className="flex flex-wrap gap-2">
                        {item.specifications.map((spec, idx) => (
                          <Badge key={idx} variant="secondary" className="text-xs">
                            {spec}
                          </Badge>
                        ))}
                      </div>
                    )}
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-slate-600">数量：{item.quantity}</span>
                      <span className="text-slate-900">{formatPrice(item.price)}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* 要求信息卡片 */}
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200/60">
          <CardHeader>
            <h3 className="text-slate-900 flex items-center gap-2">
              <Receipt className="h-4 w-4" />
              要求信息
            </h3>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <p className="text-slate-600 leading-relaxed">
                针对此订单，我们已为您准备了详细的定制要求和规格说明。包含产品的技术参数、定制选项、质量标准等重要信息，帮助您更好地了解订单详情。
              </p>
              {isCustomService && (
                <div className="bg-blue-50/50 border border-blue-200/60 rounded-lg p-3">
                  <p className="text-sm text-blue-700">
                    定制服务订单包含专属的工艺要求和个性化设计方案。
                  </p>
                </div>
              )}
            </div>
            <div className="flex justify-end">
              <Button 
                variant="outline" 
                size="sm"
                className="bg-white/80 hover:bg-white border-slate-200/80"
                onClick={() => console.log('查看详细要求信息')}
              >
                查看详情
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 工作进度条 - 只在非取消状态显示 */}
        {order.status !== "cancelled" && (
          <Card className="bg-white/70 backdrop-blur-sm border-slate-200/60">
            <CardContent className="pt-6">
              <WorkProgressBar
                currentStep={order.workProgress.currentStep}
                steps={progressStepsWithIcons}
              />
            </CardContent>
          </Card>
        )}

        {/* 物流信息卡片 */}
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200/60">
          <CardHeader>
            <h3 className="text-slate-900 flex items-center gap-2">
              {isCustomService ? <Calendar className="h-4 w-4" /> : <Truck className="h-4 w-4" />}
              {isCustomService ? "服务信息" : "物流信息"}
            </h3>
          </CardHeader>
          <CardContent className="space-y-4">
            {isCustomService ? (
              <div className="flex items-center gap-2 text-sm text-slate-600 bg-blue-50/50 px-3 py-2 rounded-lg border border-blue-100/60">
                <Calendar className="h-4 w-4 text-blue-600" />
                <span>定制服务，无需物流配送</span>
              </div>
            ) : (
              <>
                <div className="flex items-start gap-3">
                  <MapPin className="h-4 w-4 text-slate-500 mt-0.5" />
                  <div>
                    <p className="text-sm text-slate-600">收货地址</p>
                    <p className="text-slate-900 mt-1">{order.shippingAddress}</p>
                  </div>
                </div>
                {order.trackingNumber && (
                  <div className="flex items-start gap-3">
                    <Truck className="h-4 w-4 text-slate-500 mt-0.5" />
                    <div className="flex-1">
                      <p className="text-sm text-slate-600">物流单号</p>
                      <button
                        onClick={() => copyToClipboard(order.trackingNumber!, 'tracking')}
                        className="flex items-center gap-1.5 text-slate-900 hover:text-blue-600 transition-colors mt-1"
                      >
                        <span>{order.trackingNumber}</span>
                        {copiedTrackingNumber ? (
                          <Check className="h-3 w-3 text-green-600" />
                        ) : (
                          <Copy className="h-3 w-3" />
                        )}
                      </button>
                    </div>
                  </div>
                )}
              </>
            )}
          </CardContent>
        </Card>

        {/* 支付信息卡片 */}
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200/60">
          <CardHeader>
            <h3 className="text-slate-900 flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              支付详情
            </h3>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">商品金额</span>
                <span className="text-slate-900">{formatPrice(order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0))}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">价格佣金 (5%)</span>
                <span className="text-slate-900">{formatPrice(order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0) * 0.05)}</span>
              </div>
              {order.shippingFee > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-slate-600">运费</span>
                  <span className="text-slate-900">{formatPrice(order.shippingFee)}</span>
                </div>
              )}
              {order.discountAmount && order.discountAmount > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-slate-600">优惠金额</span>
                  <span className="text-red-600">-{formatPrice(order.discountAmount)}</span>
                </div>
              )}
              <Separator />
              <div className="flex justify-between">
                <span className="text-slate-900">实付金额</span>
                <span className="text-slate-900 text-xl">{formatPrice(order.total)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-slate-600">支付方式</span>
                <span className="text-slate-900">{order.paymentMethod}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 操作按钮 */}
        <Card className="bg-white/70 backdrop-blur-sm border-slate-200/60">
          <CardContent className="pt-6">
            <div className="flex flex-wrap gap-3 justify-center">
              {/* 根据订单状态显示不同的主要操作按钮 */}
              {order.status === "pending" && (
                <Button 
                  onClick={handlePayOrder}
                  className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  立即付款
                </Button>
              )}
              
              {order.status === "shipped" && (
                <Button 
                  onClick={handleConfirmReceipt}
                  className="bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  确认收货
                </Button>
              )}

              {/* 次要操作按钮 */}
              {order.status === "delivered" && (
                <>
                  <Button 
                    variant="outline" 
                    onClick={handleRateOrder}
                    className="bg-white/80 hover:bg-white border-slate-200/80"
                  >
                    <Star className="h-4 w-4 mr-2" />
                    评价订单
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={handleInvoiceRequest}
                    className="bg-white/80 hover:bg-white border-slate-200/80"
                  >
                    <Receipt className="h-4 w-4 mr-2" />
                    开发票
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={handleAfterSalesRequest}
                    className="bg-white/80 hover:bg-white border-slate-200/80"
                  >
                    <Shield className="h-4 w-4 mr-2" />
                    申请售后
                  </Button>
                </>
              )}

              {order.status === "cancelled" && (
                <Button 
                  variant="outline" 
                  onClick={handleReorder}
                  className="bg-blue-50/80 hover:bg-blue-100/80 border-blue-200/80 text-blue-700"
                >
                  <RotateCcw className="h-4 w-4 mr-2" />
                  重新下单
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}