import { useState } from "react";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Checkbox } from "./ui/checkbox";
import { ArrowLeft, ArrowRight } from "lucide-react";

interface RegisterPageProps {
  onBack: () => void;
  onLoginClick: () => void;
}

export function RegisterPage({ onBack, onLoginClick }: RegisterPageProps) {
  const [formData, setFormData] = useState({
    email: "",
    password: "",
    confirmPassword: "",
    username: "",
    phone: "",
    verificationCode: "",
  });
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isCodeSending, setIsCodeSending] = useState(false);
  const [codeCountdown, setCodeCountdown] = useState(0);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleRegister();
    }
  };

  const handleSendCode = async () => {
    if (!formData.phone.trim()) {
      alert("请先输入手机号码");
      return;
    }

    // 简单的手机号格式验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.phone)) {
      alert("请输入正确的手机号码格式");
      return;
    }

    setIsCodeSending(true);
    
    // 模拟发送验证码
    setTimeout(() => {
      setIsCodeSending(false);
      setCodeCountdown(60);
      console.log("验证码已发送到:", formData.phone);
      
      // 开始倒计时
      const timer = setInterval(() => {
        setCodeCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }, 1000);
  };

  const handleRegister = async () => {
    if (!formData.email.trim() || !formData.password.trim() || !formData.confirmPassword.trim() || 
        !formData.username.trim() || !formData.phone.trim() || !formData.verificationCode.trim() || !agreeToTerms) {
      alert("请填写所有必填项");
      return;
    }
    
    if (formData.password !== formData.confirmPassword) {
      alert("密码确认不匹配");
      return;
    }

    // 简单的手机号格式验证
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.phone)) {
      alert("请输入正确的手机号码格式");
      return;
    }

    if (formData.verificationCode.length !== 6) {
      alert("请输入6位验证码");
      return;
    }
    
    setIsLoading(true);
    // 模拟注册过程
    setTimeout(() => {
      setIsLoading(false);
      console.log("注册信息:", formData);
      // 这里可以添加实际的注册逻辑
    }, 1500);
  };

  const isFormValid = formData.email.trim() && formData.password.trim() && formData.confirmPassword.trim() && 
                     formData.username.trim() && formData.phone.trim() && formData.verificationCode.trim() && agreeToTerms;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* 返回按钮 */}
        <Button
          onClick={onBack}
          variant="ghost"
          className="mb-8 p-2 hover:bg-slate-100/60 rounded-full transition-colors"
        >
          <ArrowLeft className="h-5 w-5 text-slate-600" />
        </Button>

        {/* 主要内容区域 */}
        <div className="p-8">
          {/* 标题区域 */}
          <div className="text-center mb-8">
            <h1 className="text-slate-900 mb-2">创建 U-bund 账户</h1>
            <p className="text-slate-600 text-sm">加入 U-bund，开启专业服务之旅</p>
          </div>

          {/* 注册表单 */}
          <div className="space-y-4">
            {/* 用户名输入框 */}
            <div>
              <Input
                type="text"
                placeholder="用户名"
                value={formData.username}
                onChange={(e) => handleInputChange("username", e.target.value)}
                onKeyPress={handleKeyPress}
                className="w-full h-12 pl-4 bg-slate-50/80 border-slate-200/60 rounded-xl focus:bg-white focus:border-blue-300/60 transition-all duration-200"
              />
            </div>

            {/* 邮箱输入框 */}
            <div>
              <Input
                type="email"
                placeholder="电子邮件"
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                onKeyPress={handleKeyPress}
                className="w-full h-12 pl-4 bg-slate-50/80 border-slate-200/60 rounded-xl focus:bg-white focus:border-blue-300/60 transition-all duration-200"
              />
            </div>

            {/* 手机号输入框 */}
            <div>
              <Input
                type="tel"
                placeholder="手机号码"
                value={formData.phone}
                onChange={(e) => handleInputChange("phone", e.target.value)}
                onKeyPress={handleKeyPress}
                className="w-full h-12 pl-4 bg-slate-50/80 border-slate-200/60 rounded-xl focus:bg-white focus:border-blue-300/60 transition-all duration-200"
              />
            </div>

            {/* 验证码输入框 */}
            <div className="flex space-x-3">
              <Input
                type="text"
                placeholder="输入验证码"
                value={formData.verificationCode}
                onChange={(e) => handleInputChange("verificationCode", e.target.value)}
                onKeyPress={handleKeyPress}
                maxLength={6}
                className="flex-1 h-12 pl-4 bg-slate-50/80 border-slate-200/60 rounded-xl focus:bg-white focus:border-blue-300/60 transition-all duration-200"
              />
              <Button
                onClick={handleSendCode}
                disabled={!formData.phone.trim() || isCodeSending || codeCountdown > 0}
                variant="outline"
                className="h-12 px-4 bg-slate-50/80 border-slate-200/60 hover:bg-slate-100/80 disabled:bg-slate-50/50 rounded-xl transition-all duration-200 whitespace-nowrap"
              >
                {isCodeSending ? (
                  <div className="w-4 h-4 border-2 border-slate-400 border-t-transparent rounded-full animate-spin" />
                ) : codeCountdown > 0 ? (
                  `${codeCountdown}s`
                ) : (
                  "发送验证码"
                )}
              </Button>
            </div>

            {/* 密码输入框 */}
            <div>
              <Input
                type="password"
                placeholder="密码"
                value={formData.password}
                onChange={(e) => handleInputChange("password", e.target.value)}
                onKeyPress={handleKeyPress}
                className="w-full h-12 pl-4 bg-slate-50/80 border-slate-200/60 rounded-xl focus:bg-white focus:border-blue-300/60 transition-all duration-200"
              />
            </div>

            {/* 确认密码输入框 */}
            <div className="relative">
              <Input
                type="password"
                placeholder="确认密码"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
                onKeyPress={handleKeyPress}
                className="w-full h-12 pl-4 pr-12 bg-slate-50/80 border-slate-200/60 rounded-xl focus:bg-white focus:border-blue-300/60 transition-all duration-200"
              />
              <Button
                onClick={handleRegister}
                disabled={!isFormValid || isLoading}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 bg-slate-600 hover:bg-slate-700 disabled:bg-slate-300 rounded-full transition-colors"
              >
                {isLoading ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <ArrowRight className="h-4 w-4 text-white" />
                )}
              </Button>
            </div>

            {/* 服务条款同意 */}
            <div className="flex items-start space-x-3 pt-2">
              <Checkbox
                id="terms"
                checked={agreeToTerms}
                onCheckedChange={(checked) => setAgreeToTerms(checked as boolean)}
                className="border-slate-300 mt-0.5"
              />
              <label
                htmlFor="terms"
                className="text-sm text-slate-600 cursor-pointer leading-relaxed"
              >
                我已阅读并同意{" "}
                <Button
                  variant="link"
                  className="text-blue-600 hover:text-blue-700 p-0 h-auto text-sm underline"
                >
                  服务条款
                </Button>
                {" "}和{" "}
                <Button
                  variant="link"
                  className="text-blue-600 hover:text-blue-700 p-0 h-auto text-sm underline"
                >
                  隐私政策
                </Button>
              </label>
            </div>
          </div>

          {/* 登录链接 */}
          <div className="text-center mt-8 pt-6 border-t border-slate-200/60">
            <p className="text-sm text-slate-600">
              已有 U-bund 账户？{" "}
              <Button
                onClick={onLoginClick}
                variant="link"
                className="text-blue-600 hover:text-blue-700 p-0 h-auto text-sm"
              >
                立即登录 →
              </Button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}