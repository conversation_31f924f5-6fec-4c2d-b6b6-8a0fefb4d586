import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { StatusCounts } from "@/types/order";

interface OrderStatusTabsProps {
  activeStatus: string;
  onStatusChange: (status: string) => void;
  statusCounts: StatusCounts;
}

export function OrderStatusTabs({ activeStatus, onStatusChange, statusCounts }: OrderStatusTabsProps) {
  const statusTabs = [
    { id: "all", label: "全部", count: statusCounts.total },
    { id: "pending", label: "待处理", count: statusCounts.pending },
    { id: "processing", label: "进行中", count: statusCounts.processing },
    { id: "shipped", label: "待交付", count: statusCounts.shipped },
    { id: "delivered", label: "已完成", count: statusCounts.delivered },
    { id: "cancelled", label: "已取消", count: statusCounts.cancelled },
    { id: "aftersale", label: "售后", count: statusCounts.aftersale }
  ];

  return (
    <div className="flex items-center gap-2 overflow-x-auto pb-2">
      {statusTabs.map((tab) => (
        <Button
          key={tab.id}
          variant={activeStatus === tab.id ? "default" : "ghost"}
          size="sm"
          onClick={() => onStatusChange(tab.id)}
          className={`flex items-center gap-2 whitespace-nowrap ${
            activeStatus === tab.id
              ? "bg-blue-600 text-white shadow-sm"
              : "text-slate-600 hover:text-slate-900 hover:bg-slate-100"
          }`}
        >
          <span>{tab.label}</span>
          {tab.count > 0 && (
            <Badge 
              variant="secondary" 
              className={`text-xs px-1.5 py-0.5 ${
                activeStatus === tab.id
                  ? "bg-white/20 text-white border-white/20"
                  : "bg-slate-200 text-slate-700"
              }`}
            >
              {tab.count}
            </Badge>
          )}
        </Button>
      ))}
    </div>
  );
}
