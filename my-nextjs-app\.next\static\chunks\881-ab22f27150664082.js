"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[881],{518:(e,s,t)=>{t.d(s,{l:()=>m});var a=t(8260),r=t(7688),i=t(3657),n=t(3414),l=t(1298),d=t(2256),o=t(231),c=t(6695),u=t(6506),x=t(9858),h=t(3923);function m(e){let{isOpen:s,onClose:t,onMinimize:m}=e,[f,v]=(0,r.useState)("official"),[b,g]=(0,r.useState)(""),p=[{id:"official",name:"官方客服",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",isOnline:!0,lastMessage:"有什么可以帮助您的吗？",unreadCount:1,type:"official"},{id:"merchant1",name:"设计师小王",avatar:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",isOnline:!0,lastMessage:"好的，我马上开始设计",unreadCount:2,type:"user"},{id:"merchant2",name:"程序员老李",avatar:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",isOnline:!1,lastMessage:"网站开发进度如何？",unreadCount:0,type:"user"}],[j]=(0,r.useState)({official:[{id:"1",content:"您好！欢迎使用U-bund平台，有什么可以帮助您的吗？",sender:"other",time:"14:30",type:"text"}],merchant1:[{id:"1",content:"您好，关于Logo设计的需求我已经收到了",sender:"other",time:"10:15",type:"text"},{id:"2",content:"好的，请问大概什么时候能完成？",sender:"me",time:"10:16",type:"text"},{id:"3",content:"预计3-5个工作日，我会先做几个方案给您选择",sender:"other",time:"10:18",type:"text"}],merchant2:[{id:"1",content:"网站开发项目已经开始了，预计下周完成",sender:"other",time:"昨天",type:"text"}]}),w=p.find(e=>e.id===f),N=j[f]||[],y=()=>{b.trim()&&(console.log("发送消息:",b),g(""))};return s?(0,a.jsxs)("div",{className:"fixed bottom-4 right-4 w-[640px] h-[768px] bg-white border border-slate-200 rounded-lg shadow-2xl z-[100] flex flex-col overflow-hidden",children:[(0,a.jsxs)("div",{className:"bg-slate-50/80 border-b border-slate-200 px-6 py-4 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(d.A,{className:"h-5 w-5 text-slate-600"}),(0,a.jsx)("span",{className:"text-slate-900 font-medium",children:"消息通知"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:m,className:"h-8 w-8 p-0 hover:bg-slate-200/60",children:(0,a.jsx)(o.A,{className:"h-4 w-4"})}),(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:t,className:"h-8 w-8 p-0 hover:bg-slate-200/60",children:(0,a.jsx)(c.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"flex flex-1 overflow-hidden",children:[(0,a.jsx)("div",{className:"w-64 border-r border-slate-200 bg-slate-50/30",children:(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-slate-900 mb-3",children:"最近联系"}),(0,a.jsx)("div",{className:"space-y-2",children:p.map(e=>(0,a.jsxs)("div",{onClick:()=>v(e.id),className:"flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors ".concat(f===e.id?"bg-blue-50 border border-blue-200":"hover:bg-slate-100/60"),children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(n.eu,{className:"h-10 w-10",children:[(0,a.jsx)(n.BK,{src:e.avatar}),(0,a.jsx)(n.q5,{children:"official"===e.type?(0,a.jsx)(u.A,{className:"h-5 w-5"}):(0,a.jsx)(x.A,{className:"h-5 w-5"})})]}),(0,a.jsx)("div",{className:"absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ".concat(e.isOnline?"bg-green-500":"bg-slate-400")})]}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-slate-900 truncate",children:e.name}),e.unreadCount>0&&(0,a.jsx)(l.E,{className:"h-5 w-5 p-0 flex items-center justify-center text-xs bg-red-500",children:e.unreadCount})]}),(0,a.jsx)("p",{className:"text-xs text-slate-600 truncate",children:e.lastMessage})]})]},e.id))})]})}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col",children:[w&&(0,a.jsx)("div",{className:"border-b border-slate-200 p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(n.eu,{className:"h-10 w-10",children:[(0,a.jsx)(n.BK,{src:w.avatar}),(0,a.jsx)(n.q5,{children:"official"===w.type?(0,a.jsx)(u.A,{className:"h-5 w-5"}):(0,a.jsx)(x.A,{className:"h-5 w-5"})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-slate-900",children:w.name}),(0,a.jsx)("p",{className:"text-sm text-slate-600",children:w.isOnline?"在线":"离线"})]})]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-4 space-y-4",children:N.map(e=>(0,a.jsx)("div",{className:"flex ".concat("me"===e.sender?"justify-end":"justify-start"),children:(0,a.jsxs)("div",{className:"max-w-xs lg:max-w-md px-4 py-2 rounded-lg ".concat("me"===e.sender?"bg-blue-600 text-white":"bg-slate-100 text-slate-900"),children:[(0,a.jsx)("p",{className:"text-sm",children:e.content}),(0,a.jsx)("p",{className:"text-xs mt-1 ".concat("me"===e.sender?"text-blue-100":"text-slate-500"),children:e.time})]})},e.id))}),(null==w?void 0:w.type)!=="official"&&(0,a.jsx)("div",{className:"border-t border-slate-200 p-4",children:(0,a.jsxs)("div",{className:"flex items-end gap-3",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("textarea",{value:b,onChange:e=>g(e.target.value),onKeyPress:e=>{"Enter"!==e.key||e.shiftKey||(e.preventDefault(),y())},placeholder:"输入消息...",className:"w-full px-3 py-2 border border-slate-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-300",rows:1})}),(0,a.jsx)(i.$,{onClick:y,disabled:!b.trim(),className:"bg-blue-600 hover:bg-blue-700",children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})]})})]})]})]}):null}},1298:(e,s,t)=>{t.d(s,{E:()=>d});var a=t(8260);t(7688);var r=t(3191),i=t(3280),n=t(7814);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d(e){let{className:s,variant:t,asChild:i=!1,...d}=e,o=i?r.DX:"span";return(0,a.jsx)(o,{"data-slot":"badge",className:(0,n.cn)(l({variant:t}),s),...d})}},2060:(e,s,t)=>{t.d(s,{H:()=>l});var a=t(8260),r=t(3657),i=t(1298),n=t(9231);function l(e){let{onClick:s,notificationCount:t=0,isVisible:l=!0}=e;return l?(0,a.jsx)("div",{className:"fixed bottom-6 right-6 z-50",children:(0,a.jsxs)(r.$,{onClick:s,size:"lg",className:"relative h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 group",children:[(0,a.jsx)(n.A,{className:"h-6 w-6 text-white group-hover:scale-110 transition-transform"}),t>0&&(0,a.jsx)(i.E,{className:"absolute -top-2 -right-2 h-6 w-6 p-0 flex items-center justify-center text-xs bg-red-500 text-white border-2 border-white",children:t>99?"99+":t}),(0,a.jsx)("div",{className:"absolute inset-0 rounded-full bg-blue-600 animate-ping opacity-20"})]})}):null}},3414:(e,s,t)=>{t.d(s,{BK:()=>l,eu:()=>n,q5:()=>d});var a=t(8260);t(7688);var r=t(9403),i=t(7814);function n(e){let{className:s,...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"avatar",className:(0,i.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",s),...t})}function l(e){let{className:s,...t}=e;return(0,a.jsx)(r._V,{"data-slot":"avatar-image",className:(0,i.cn)("aspect-square size-full",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)(r.H4,{"data-slot":"avatar-fallback",className:(0,i.cn)("bg-muted flex size-full items-center justify-center rounded-full",s),...t})}},3657:(e,s,t)=>{t.d(s,{$:()=>d});var a=t(8260);t(7688);var r=t(3191),i=t(3280),n=t(7814);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:s,variant:t,size:i,asChild:d=!1,...o}=e,c=d?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,n.cn)(l({variant:t,size:i,className:s})),...o})}},7814:(e,s,t)=>{t.d(s,{cn:()=>i});var a=t(1230),r=t(5202);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}}}]);