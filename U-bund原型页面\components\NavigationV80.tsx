import { useState } from "react";
import { But<PERSON> } from "./ui/button";
import { Package, Search, Smartphone, Palette, Compass } from "lucide-react";

interface NavigationV80Props {
  onNavigate?: (page: string) => void;
  onCategoryChange?: (category: string) => void;
  onLoginClick?: () => void;
}

export function NavigationV80({ onNavigate, onCategoryChange, onLoginClick }: NavigationV80Props) {
  const handleNavigate = (page: string) => {
    onNavigate?.(page);
  };

  const handleDownloadApp = () => {
    console.log("下载APP");
    // 这里可以添加实际的下载逻辑，比如跳转到应用商店或显示下载选项
  };

  return (
    <nav className="bg-white/70 backdrop-blur-xl border-b border-slate-200/60 sticky top-0 z-[70]">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* 左侧 - Logo */}
          <div className="flex items-center">
            <div className="flex items-center gap-3 cursor-pointer" onClick={() => handleNavigate("home")}>
              <div className="relative">
                <div className="absolute inset-0 bg-blue-500/10 rounded-xl blur-sm"></div>
                <div className="relative p-2 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-xl border border-blue-200/30">
                  <Package className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <span className="text-slate-900 text-lg font-bold tracking-tight">U-bund</span>
            </div>
          </div>

          {/* 右侧 - 导航按钮和登录注册 */}
          <div className="flex items-center gap-3">
            {/* 移动端搜索按钮 */}
            <div className="lg:hidden">
              <Button
                variant="ghost"
                size="sm"
                className="p-2 hover:bg-slate-50/80 transition-colors duration-200"
              >
                <Search className="h-5 w-5 text-slate-600" />
              </Button>
            </div>

            {/* 导航按钮组 */}
            <div className="hidden md:flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDownloadApp}
                className="flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 text-slate-600 hover:text-slate-900 hover:bg-slate-100/80"
              >
                <Smartphone className="h-4 w-4" />
                下载APP*
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleNavigate("support")}
                className="flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 text-slate-600 hover:text-slate-900 hover:bg-slate-100/80"
              >
                <Palette className="h-4 w-4" />
                定制服务*
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleNavigate("gallery")}
                className="flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 text-slate-600 hover:text-slate-900 hover:bg-slate-100/80"
              >
                <Compass className="h-4 w-4" />
                探索*
              </Button>
            </div>

            {/* 分隔线 */}
            <div className="hidden md:block w-px h-6 bg-slate-200/60"></div>

            {/* 登录/注册按钮 */}
            <Button
              size="sm"
              onClick={onLoginClick}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6"
            >
              登录 / 注册
            </Button>
          </div>
        </div>
      </div>
    </nav>
  );
}