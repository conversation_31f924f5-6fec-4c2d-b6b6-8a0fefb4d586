import { Order } from "../types/order";

export const mockOrders: Order[] = [
  {
    id: "1",
    orderNumber: "ORD202501290001",
    date: "2025-01-28",
    status: "delivered",
    total: 8999.00,
    shippingAddress: "",
    completedAt: "2025-01-29",
    estimatedCompletionTime: undefined,
    paymentMethod: "支付宝",
    shippingFee: 0,
    discountAmount: 0,
    workProgress: {
      currentStep: 3,
      steps: [
        { id: "1", label: "需求对接", description: "确认品牌设计需求和视觉方向", status: "completed", completedAt: "2025-01-28T10:30:00" },
        { id: "2", label: "设计制作", description: "Logo创意设计和方案制作", status: "completed", completedAt: "2025-01-28T16:45:00" },
        { id: "3", label: "修改确认", description: "客户反馈修改和最终确认", status: "completed", completedAt: "2025-01-29T09:15:00" },
        { id: "4", label: "项目交付", description: "设计文件输出和素材打包", status: "completed", completedAt: "2025-01-29T11:00:00" }
      ]
    },
    merchant: {
      id: "merchant_1",
      name: "墨刻品牌设计工作室",
      avatar: "https://images.unsplash.com/photo-1472214103451-9374bd1c798e?w=100&h=100&fit=crop&crop=faces",
      isOnline: true,
      phone: "************",
      rating: 4.9
    },
    items: [
      {
        id: "1",
        name: "企业品牌Logo设计 + VI视觉识别系统",
        image: "https://images.unsplash.com/photo-1634942537034-2531766767d1?w=400&h=400&fit=crop",
        quantity: 1,
        price: 8999.00,
        specifications: ["原创设计", "多方案对比", "全套VI手册", "商用授权"]
      }
    ]
  },
  {
    id: "2",
    orderNumber: "ORD202501280002",
    date: "2025-01-27",
    status: "shipped",
    total: 15999.00,
    shippingAddress: "",
    completedAt: undefined,
    estimatedCompletionTime: "2025-01-31",
    paymentMethod: "微信支付",
    shippingFee: 0,
    discountAmount: 1000,
    trackingNumber: "SF1234567890123",
    workProgress: {
      currentStep: 2,
      steps: [
        { id: "1", label: "需求分析", description: "分析网站功能需求和技术架构", status: "completed", completedAt: "2025-01-27T14:20:00" },
        { id: "2", label: "开发制作", description: "前后端开发和功能实现", status: "completed", completedAt: "2025-01-28T20:30:00" },
        { id: "3", label: "测试优化", description: "功能测试和性能优化", status: "current" },
        { id: "4", label: "上线部署", description: "域名配置和服务器部署", status: "pending" }
      ]
    },
    merchant: {
      id: "merchant_2",
      name: "码猿科技开发团队",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",
      isOnline: false,
      phone: "021-12345678",
      rating: 4.7
    },
    items: [
      {
        id: "2",
        name: "企业官网定制开发（响应式）",
        image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=400&fit=crop",
        quantity: 1,
        price: 12999.00,
        specifications: ["React技术栈", "响应式设计", "SEO优化", "后台管理系统"]
      },
      {
        id: "3",
        name: "域名注册和SSL证书配置",
        image: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=400&h=400&fit=crop",
        quantity: 1,
        price: 3000.00,
        specifications: [".com域名", "SSL证书", "DNS配置", "一年服务"]
      }
    ]
  }
];
