{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,6WAAC,+QAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6WAAC,+QAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,6WAAC,+QAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/NavigationV78.tsx"], "sourcesContent": ["import { useState } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { ImageWithFallback } from \"@/components/figma/ImageWithFallback\";\nimport { \n  Bell, \n  Settings, \n  ChevronDown, \n  Package,\n  User,\n  Star,\n  FileText,\n  Search,\n  Palette,\n  Compass,\n  Shield,\n  MessageCircle\n} from \"lucide-react\";\n\ninterface NavigationV78Props {\n  currentPage?: \"services\" | \"orders\" | \"favorites\" | \"support\" | \"gallery\";\n  onNavigate?: (page: string) => void;\n  onOpenChat?: () => void;\n}\n\nexport function NavigationV78({ currentPage = \"services\", onNavigate, onOpenChat }: NavigationV78Props) {\n  const [showUserMenu, setShowUserMenu] = useState(false);\n  const [searchQuery, setSearchQuery] = useState(\"\");\n  const [notificationCount] = useState(3); // 模拟通知数量\n\n  const handleNavigate = (page: string) => {\n    onNavigate?.(page);\n    setShowUserMenu(false);\n  };\n\n  const handleNotificationClick = () => {\n    onOpenChat?.();\n  };\n\n  return (\n    <nav className=\"bg-white/90 backdrop-blur-sm border-b border-slate-200/60 sticky top-0 z-[70]\">\n      <div className=\"max-w-7xl mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16\">\n          {/* 左侧 - Logo */}\n          <div className=\"flex items-center gap-8\">\n            <div className=\"flex items-center gap-3 cursor-pointer\" onClick={() => handleNavigate(\"home\")}>\n              <div className=\"relative\">\n                <div className=\"absolute inset-0 bg-blue-500/10 rounded-xl blur-sm\"></div>\n                <div className=\"relative p-2 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-xl border border-blue-200/30\">\n                  <Package className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              </div>\n              <span className=\"text-slate-900 text-lg font-bold tracking-tight\">U-bund</span>\n            </div>\n          </div>\n\n          {/* 中间 - 导航按钮和搜索框 */}\n          <div className=\"flex-1 max-w-4xl mx-8 hidden md:flex items-center gap-4\">\n            {/* 导航按钮组 */}\n            <div className=\"flex items-center gap-2\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => handleNavigate(\"support\")}\n                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${\n                  currentPage === \"support\"\n                    ? 'bg-blue-50/80 text-blue-700 shadow-sm' \n                    : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'\n                }`}\n              >\n                <Palette className=\"h-4 w-4\" />\n                定制服务*\n              </Button>\n              \n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => handleNavigate(\"services\")}\n                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${\n                  currentPage === \"services\"\n                    ? 'bg-blue-50/80 text-blue-700 shadow-sm' \n                    : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'\n                }`}\n              >\n                <Search className=\"h-4 w-4\" />\n                找服务\n              </Button>\n              \n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => handleNavigate(\"gallery\")}\n                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${\n                  currentPage === \"gallery\"\n                    ? 'bg-blue-50/80 text-blue-700 shadow-sm' \n                    : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'\n                }`}\n              >\n                <Compass className=\"h-4 w-4\" />\n                作品广场*\n              </Button>\n            </div>\n\n            {/* 搜索框 */}\n            <div className=\"flex-1 max-w-md relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400\" />\n              <input\n                type=\"text\"\n                placeholder=\"搜索服务、商家...\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 bg-slate-50/80 border border-slate-200/60 rounded-lg text-sm placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-300 transition-all duration-200\"\n              />\n            </div>\n          </div>\n\n          {/* 右侧 - 用户操作区 */}\n          <div className=\"flex items-center gap-3\">\n            {/* 我的订单 */}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => handleNavigate(\"orders\")}\n              className={`hidden sm:flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${\n                currentPage === \"orders\"\n                  ? 'bg-blue-50/80 text-blue-700 shadow-sm' \n                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'\n              }`}\n            >\n              <FileText className=\"h-4 w-4\" />\n              我的订单\n            </Button>\n\n            {/* 我的收藏 */}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={() => handleNavigate(\"favorites\")}\n              className={`hidden sm:flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${\n                currentPage === \"favorites\"\n                  ? 'bg-blue-50/80 text-blue-700 shadow-sm' \n                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'\n              }`}\n            >\n              <Star className=\"h-4 w-4\" />\n              我的收藏\n            </Button>\n\n            {/* 通知铃铛 */}\n            <div className=\"relative\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={handleNotificationClick}\n                className=\"relative p-2 rounded-lg text-slate-600 hover:text-slate-900 hover:bg-slate-100/80 transition-all duration-200\"\n              >\n                <Bell className=\"h-5 w-5\" />\n                {notificationCount > 0 && (\n                  <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium\">\n                    {notificationCount}\n                  </span>\n                )}\n              </Button>\n            </div>\n\n            {/* 用户头像和菜单 */}\n            <div className=\"relative\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setShowUserMenu(!showUserMenu)}\n                className=\"flex items-center gap-2 p-1 rounded-lg hover:bg-slate-100/80 transition-all duration-200\"\n              >\n                <Avatar className=\"h-8 w-8\">\n                  <AvatarImage src=\"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces\" />\n                  <AvatarFallback>用户</AvatarFallback>\n                </Avatar>\n                <ChevronDown className=\"h-4 w-4 text-slate-600\" />\n              </Button>\n\n              {/* 用户菜单下拉 */}\n              {showUserMenu && (\n                <div className=\"absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-slate-200/60 py-2 z-50\">\n                  <button\n                    onClick={() => handleNavigate(\"profile\")}\n                    className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 transition-colors\"\n                  >\n                    <User className=\"h-4 w-4\" />\n                    个人中心\n                  </button>\n                  <button\n                    onClick={() => handleNavigate(\"settings\")}\n                    className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 transition-colors\"\n                  >\n                    <Settings className=\"h-4 w-4\" />\n                    设置\n                  </button>\n                  <div className=\"border-t border-slate-200/60 my-2\"></div>\n                  <button\n                    onClick={() => handleNavigate(\"logout\")}\n                    className=\"w-full flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors\"\n                  >\n                    退出登录\n                  </button>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAqBO,SAAS,cAAc,EAAE,cAAc,UAAU,EAAE,UAAU,EAAE,UAAU,EAAsB;IACpG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,SAAS;IAElD,MAAM,iBAAiB,CAAC;QACtB,aAAa;QACb,gBAAgB;IAClB;IAEA,MAAM,0BAA0B;QAC9B;IACF;IAEA,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;sBACb,cAAA,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;4BAAyC,SAAS,IAAM,eAAe;;8CACpF,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;;;;;;sDACf,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC,4RAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGvB,6WAAC;oCAAK,WAAU;8CAAkD;;;;;;;;;;;;;;;;;kCAKtE,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,WAAW,CAAC,yEAAyE,EACnF,gBAAgB,YACZ,0CACA,6DACJ;;0DAEF,6WAAC,4RAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAIjC,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,WAAW,CAAC,yEAAyE,EACnF,gBAAgB,aACZ,0CACA,6DACJ;;0DAEF,6WAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAIhC,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,WAAW,CAAC,yEAAyE,EACnF,gBAAgB,YACZ,0CACA,6DACJ;;0DAEF,6WAAC,4RAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;0CAMnC,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6WAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;;;;;;;kCAMhB,6WAAC;wBAAI,WAAU;;0CAEb,6WAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe;gCAC9B,WAAW,CAAC,mFAAmF,EAC7F,gBAAgB,WACZ,0CACA,6DACJ;;kDAEF,6WAAC,kSAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAKlC,6WAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe;gCAC9B,WAAW,CAAC,mFAAmF,EAC7F,gBAAgB,cACZ,0CACA,6DACJ;;kDAEF,6WAAC,sRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAK9B,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;;sDAEV,6WAAC,sRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,oBAAoB,mBACnB,6WAAC;4CAAK,WAAU;sDACb;;;;;;;;;;;;;;;;;0CAOT,6WAAC;gCAAI,WAAU;;kDACb,6WAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,gBAAgB,CAAC;wCAChC,WAAU;;0DAEV,6WAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6WAAC,kIAAA,CAAA,cAAW;wDAAC,KAAI;;;;;;kEACjB,6WAAC,kIAAA,CAAA,iBAAc;kEAAC;;;;;;;;;;;;0DAElB,6WAAC,wSAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;oCAIxB,8BACC,6WAAC;wCAAI,WAAU;;0DACb,6WAAC;gDACC,SAAS,IAAM,eAAe;gDAC9B,WAAU;;kEAEV,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAG9B,6WAAC;gDACC,SAAS,IAAM,eAAe;gDAC9B,WAAU;;kEAEV,6WAAC,8RAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGlC,6WAAC;gDAAI,WAAU;;;;;;0DACf,6WAAC;gDACC,SAAS,IAAM,eAAe;gDAC9B,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}, {"offset": {"line": 548, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/CategoryNavigation.tsx"], "sourcesContent": ["import { useState, useRef, useEffect } from \"react\";\nimport { Button } from \"@/components/ui/button\";\nimport { \n  TrendingUp,\n  Palette, \n  Code, \n  Megaphone, \n  Camera, \n  PenTool,\n  Music,\n  Briefcase,\n  DollarSign,\n  Scale,\n  GraduationCap\n} from \"lucide-react\";\n\ninterface CategoryNavigationProps {\n  activeCategory?: string;\n  onCategoryChange?: (category: string) => void;\n  onSubCategoryClick?: (category: string, subCategory: string) => void;\n}\n\nexport function CategoryNavigation({ activeCategory = \"popular\", onCategoryChange, onSubCategoryClick }: CategoryNavigationProps) {\n  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);\n  const [isHovering, setIsHovering] = useState(false);\n  const [activeSubCategory, setActiveSubCategory] = useState<string | null>(null);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const timeoutRef = useRef<NodeJS.Timeout | null>(null);\n\n  const mainCategories = [\n    { id: \"popular\", label: \"热门\", icon: TrendingUp },\n    { id: \"design\", label: \"设计\", icon: Palette },\n    { id: \"tech\", label: \"技术\", icon: Code },\n    { id: \"marketing\", label: \"营销\", icon: Megaphone },\n    { id: \"video\", label: \"视频\", icon: Camera },\n    { id: \"writing\", label: \"文案\", icon: PenTool },\n    { id: \"music\", label: \"音乐\", icon: Music },\n    { id: \"business\", label: \"商务\", icon: Briefcase },\n    { id: \"finance\", label: \"金融\", icon: DollarSign },\n    { id: \"legal\", label: \"法律\", icon: Scale },\n    { id: \"academic\", label: \"学业\", icon: GraduationCap }\n  ];\n\n  // 子分类数据\n  const subCategories = {\n    popular: {\n      title: \"热门服务\",\n      sections: [\n        {\n          title: \"设计类\",\n          items: [\"Logo设计\", \"海报设计\", \"包装设计\", \"UI设计\"]\n        },\n        {\n          title: \"技术类\", \n          items: [\"网站开发\", \"小程序开发\", \"APP开发\", \"系统开发\"]\n        }\n      ]\n    },\n    design: {\n      title: \"设计\",\n      sections: [\n        {\n          title: \"平面设计\",\n          items: [\"Logo设计\", \"海报设计\", \"名片设计\", \"宣传册设计\"]\n        },\n        {\n          title: \"UI/UX设计\",\n          items: [\"网页设计\", \"APP界面\", \"小程序界面\", \"交互设计\"]\n        }\n      ]\n    },\n    tech: {\n      title: \"技术\",\n      sections: [\n        {\n          title: \"网站开发\",\n          items: [\"企业官网\", \"电商网站\", \"响应式网站\", \"定制开发\"]\n        },\n        {\n          title: \"移动开发\",\n          items: [\"微信小程序\", \"APP开发\", \"H5开发\", \"跨平台开发\"]\n        }\n      ]\n    }\n  };\n\n  const handleCategoryClick = (categoryId: string) => {\n    // 确保调用回调函数\n    if (onCategoryChange) {\n      onCategoryChange(categoryId);\n    }\n    setHoveredCategory(null);\n    setIsHovering(false);\n    setActiveSubCategory(null);\n    \n    // 添加调试信息\n    console.log(`切换到分类: ${categoryId}`);\n  };\n\n  const handleMouseEnter = (categoryId: string) => {\n    if (timeoutRef.current) {\n      clearTimeout(timeoutRef.current);\n    }\n    setHoveredCategory(categoryId);\n    setIsHovering(true);\n  };\n\n  const handleMouseLeave = () => {\n    timeoutRef.current = setTimeout(() => {\n      setHoveredCategory(null);\n      setIsHovering(false);\n    }, 150);\n  };\n\n  const handleSubCategoryClick = (subCategory: string) => {\n    if (hoveredCategory && onSubCategoryClick) {\n      onSubCategoryClick(hoveredCategory, subCategory);\n    }\n    setActiveSubCategory(subCategory);\n    setHoveredCategory(null);\n    setIsHovering(false);\n  };\n\n  useEffect(() => {\n    return () => {\n      if (timeoutRef.current) {\n        clearTimeout(timeoutRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <div className=\"bg-white/80 backdrop-blur-xl border-b border-slate-200/40 relative z-[60]\">\n      <div className=\"max-w-7xl mx-auto px-4\">\n        {/* Apple风格水平导航栏 */}\n        <div className=\"flex items-center justify-center space-x-8 py-3\">\n          {mainCategories.map((category) => (\n            <div\n              key={category.id}\n              className=\"relative\"\n              onMouseEnter={() => handleMouseEnter(category.id)}\n              onMouseLeave={handleMouseLeave}\n            >\n              <Button\n                variant=\"ghost\"\n                onClick={() => handleCategoryClick(category.id)}\n                className={`px-3 py-2 text-sm transition-colors duration-200 ${\n                  activeCategory === category.id\n                    ? 'text-slate-900'\n                    : 'text-slate-600 hover:text-slate-900'\n                }`}\n              >\n                {category.label}\n              </Button>\n            </div>\n          ))}\n        </div>\n\n        {/* 下拉菜单 */}\n        {isHovering && hoveredCategory && subCategories[hoveredCategory as keyof typeof subCategories] && (\n          <div\n            ref={dropdownRef}\n            className=\"absolute left-0 right-0 top-full bg-white/95 backdrop-blur-xl border-b border-slate-200/40 shadow-lg\"\n            onMouseEnter={() => {\n              if (timeoutRef.current) {\n                clearTimeout(timeoutRef.current);\n              }\n              setIsHovering(true);\n            }}\n            onMouseLeave={handleMouseLeave}\n          >\n            <div className=\"max-w-7xl mx-auto px-4 py-6\">\n              <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8\">\n                {subCategories[hoveredCategory as keyof typeof subCategories]?.sections.map((section, sectionIndex) => (\n                  <div key={sectionIndex} className=\"space-y-3\">\n                    <h3 className=\"font-semibold text-slate-900 text-sm\">{section.title}</h3>\n                    <ul className=\"space-y-2\">\n                      {section.items.map((item, itemIndex) => (\n                        <li key={itemIndex}>\n                          <button\n                            onClick={() => handleSubCategoryClick(item)}\n                            className={`text-sm transition-colors duration-200 hover:text-blue-600 ${\n                              activeSubCategory === item\n                                ? 'text-blue-600 font-medium'\n                                : 'text-slate-600'\n                            }`}\n                          >\n                            {item}\n                          </button>\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAoBO,SAAS,mBAAmB,EAAE,iBAAiB,SAAS,EAAE,gBAAgB,EAAE,kBAAkB,EAA2B;IAC9H,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,cAAc,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,aAAa,CAAA,GAAA,oUAAA,CAAA,SAAM,AAAD,EAAyB;IAEjD,MAAM,iBAAiB;QACrB;YAAE,IAAI;YAAW,OAAO;YAAM,MAAM,sSAAA,CAAA,aAAU;QAAC;QAC/C;YAAE,IAAI;YAAU,OAAO;YAAM,MAAM,4RAAA,CAAA,UAAO;QAAC;QAC3C;YAAE,IAAI;YAAQ,OAAO;YAAM,MAAM,sRAAA,CAAA,OAAI;QAAC;QACtC;YAAE,IAAI;YAAa,OAAO;YAAM,MAAM,gSAAA,CAAA,YAAS;QAAC;QAChD;YAAE,IAAI;YAAS,OAAO;YAAM,MAAM,0RAAA,CAAA,SAAM;QAAC;QACzC;YAAE,IAAI;YAAW,OAAO;YAAM,MAAM,gSAAA,CAAA,UAAO;QAAC;QAC5C;YAAE,IAAI;YAAS,OAAO;YAAM,MAAM,wRAAA,CAAA,QAAK;QAAC;QACxC;YAAE,IAAI;YAAY,OAAO;YAAM,MAAM,gSAAA,CAAA,YAAS;QAAC;QAC/C;YAAE,IAAI;YAAW,OAAO;YAAM,MAAM,sSAAA,CAAA,aAAU;QAAC;QAC/C;YAAE,IAAI;YAAS,OAAO;YAAM,MAAM,wRAAA,CAAA,QAAK;QAAC;QACxC;YAAE,IAAI;YAAY,OAAO;YAAM,MAAM,4SAAA,CAAA,gBAAa;QAAC;KACpD;IAED,QAAQ;IACR,MAAM,gBAAgB;QACpB,SAAS;YACP,OAAO;YACP,UAAU;gBACR;oBACE,OAAO;oBACP,OAAO;wBAAC;wBAAU;wBAAQ;wBAAQ;qBAAO;gBAC3C;gBACA;oBACE,OAAO;oBACP,OAAO;wBAAC;wBAAQ;wBAAS;wBAAS;qBAAO;gBAC3C;aACD;QACH;QACA,QAAQ;YACN,OAAO;YACP,UAAU;gBACR;oBACE,OAAO;oBACP,OAAO;wBAAC;wBAAU;wBAAQ;wBAAQ;qBAAQ;gBAC5C;gBACA;oBACE,OAAO;oBACP,OAAO;wBAAC;wBAAQ;wBAAS;wBAAS;qBAAO;gBAC3C;aACD;QACH;QACA,MAAM;YACJ,OAAO;YACP,UAAU;gBACR;oBACE,OAAO;oBACP,OAAO;wBAAC;wBAAQ;wBAAQ;wBAAS;qBAAO;gBAC1C;gBACA;oBACE,OAAO;oBACP,OAAO;wBAAC;wBAAS;wBAAS;wBAAQ;qBAAQ;gBAC5C;aACD;QACH;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,WAAW;QACX,IAAI,kBAAkB;YACpB,iBAAiB;QACnB;QACA,mBAAmB;QACnB,cAAc;QACd,qBAAqB;QAErB,SAAS;QACT,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,YAAY;IACpC;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,WAAW,OAAO,EAAE;YACtB,aAAa,WAAW,OAAO;QACjC;QACA,mBAAmB;QACnB,cAAc;IAChB;IAEA,MAAM,mBAAmB;QACvB,WAAW,OAAO,GAAG,WAAW;YAC9B,mBAAmB;YACnB,cAAc;QAChB,GAAG;IACL;IAEA,MAAM,yBAAyB,CAAC;QAC9B,IAAI,mBAAmB,oBAAoB;YACzC,mBAAmB,iBAAiB;QACtC;QACA,qBAAqB;QACrB,mBAAmB;QACnB,cAAc;IAChB;IAEA,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,WAAW,OAAO,EAAE;gBACtB,aAAa,WAAW,OAAO;YACjC;QACF;IACF,GAAG,EAAE;IAEL,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC;YAAI,WAAU;;8BAEb,6WAAC;oBAAI,WAAU;8BACZ,eAAe,GAAG,CAAC,CAAC,yBACnB,6WAAC;4BAEC,WAAU;4BACV,cAAc,IAAM,iBAAiB,SAAS,EAAE;4BAChD,cAAc;sCAEd,cAAA,6WAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS,IAAM,oBAAoB,SAAS,EAAE;gCAC9C,WAAW,CAAC,iDAAiD,EAC3D,mBAAmB,SAAS,EAAE,GAC1B,mBACA,uCACJ;0CAED,SAAS,KAAK;;;;;;2BAdZ,SAAS,EAAE;;;;;;;;;;gBAqBrB,cAAc,mBAAmB,aAAa,CAAC,gBAA8C,kBAC5F,6WAAC;oBACC,KAAK;oBACL,WAAU;oBACV,cAAc;wBACZ,IAAI,WAAW,OAAO,EAAE;4BACtB,aAAa,WAAW,OAAO;wBACjC;wBACA,cAAc;oBAChB;oBACA,cAAc;8BAEd,cAAA,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;sCACZ,aAAa,CAAC,gBAA8C,EAAE,SAAS,IAAI,CAAC,SAAS,6BACpF,6WAAC;oCAAuB,WAAU;;sDAChC,6WAAC;4CAAG,WAAU;sDAAwC,QAAQ,KAAK;;;;;;sDACnE,6WAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACxB,6WAAC;8DACC,cAAA,6WAAC;wDACC,SAAS,IAAM,uBAAuB;wDACtC,WAAW,CAAC,2DAA2D,EACrE,sBAAsB,OAClB,8BACA,kBACJ;kEAED;;;;;;mDATI;;;;;;;;;;;mCAJL;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2B5B", "debugId": null}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/PlaceholderPage.tsx"], "sourcesContent": ["import { Package } from \"lucide-react\";\n\ninterface PlaceholderPageProps {\n  title: string;\n  description: string;\n  iconColor: string;\n  gradientColors: string;\n  borderColor: string;\n}\n\nexport function PlaceholderPage({ \n  title, \n  description, \n  iconColor, \n  gradientColors, \n  borderColor \n}: PlaceholderPageProps) {\n  return (\n    <div className=\"max-w-7xl mx-auto px-4 py-12 text-center\">\n      <div className=\"relative inline-block mb-4\">\n        <div className={`absolute inset-0 ${iconColor}/10 rounded-xl blur-sm`}></div>\n        <div className={`relative p-3 bg-gradient-to-br ${gradientColors} rounded-xl border ${borderColor}`}>\n          <Package className={`h-8 w-8 ${iconColor}`} />\n        </div>\n      </div>\n      <h2 className=\"text-slate-900 mb-2\">{title}</h2>\n      <p className=\"text-slate-600\">{description}</p>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAUO,SAAS,gBAAgB,EAC9B,KAAK,EACL,WAAW,EACX,SAAS,EACT,cAAc,EACd,WAAW,EACU;IACrB,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAW,CAAC,iBAAiB,EAAE,UAAU,sBAAsB,CAAC;;;;;;kCACrE,6WAAC;wBAAI,WAAW,CAAC,+BAA+B,EAAE,eAAe,mBAAmB,EAAE,aAAa;kCACjG,cAAA,6WAAC,4RAAA,CAAA,UAAO;4BAAC,WAAW,CAAC,QAAQ,EAAE,WAAW;;;;;;;;;;;;;;;;;0BAG9C,6WAAC;gBAAG,WAAU;0BAAuB;;;;;;0BACrC,6WAAC;gBAAE,WAAU;0BAAkB;;;;;;;;;;;;AAGrC", "debugId": null}}, {"offset": {"line": 926, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6WAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 970, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ChatWindow.tsx"], "sourcesContent": ["import { useState } from \"react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { \n  X, \n  Minus, \n  Send, \n  Bell,\n  MessageCircle,\n  User,\n  Bot\n} from \"lucide-react\";\n\ninterface ChatWindowProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onMinimize: () => void;\n}\n\ninterface Contact {\n  id: string;\n  name: string;\n  avatar: string;\n  isOnline: boolean;\n  lastMessage: string;\n  unreadCount: number;\n  type: \"user\" | \"official\";\n}\n\ninterface Message {\n  id: string;\n  content: string;\n  sender: \"me\" | \"other\";\n  time: string;\n  type: \"text\" | \"image\" | \"file\";\n}\n\nexport function ChatWindow({ isOpen, onClose, onMinimize }: ChatWindowProps) {\n  const [activeContact, setActiveContact] = useState(\"official\");\n  const [messageInput, setMessageInput] = useState(\"\");\n\n  // 模拟联系人数据\n  const contacts: Contact[] = [\n    {\n      id: \"official\",\n      name: \"官方客服\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: true,\n      lastMessage: \"有什么可以帮助您的吗？\",\n      unreadCount: 1,\n      type: \"official\"\n    },\n    {\n      id: \"merchant1\",\n      name: \"设计师小王\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: true,\n      lastMessage: \"好的，我马上开始设计\",\n      unreadCount: 2,\n      type: \"user\"\n    },\n    {\n      id: \"merchant2\",\n      name: \"程序员老李\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces\",\n      isOnline: false,\n      lastMessage: \"网站开发进度如何？\",\n      unreadCount: 0,\n      type: \"user\"\n    }\n  ];\n\n  // 模拟消息数据\n  const [messages] = useState<Record<string, Message[]>>({\n    official: [\n      {\n        id: \"1\",\n        content: \"您好！欢迎使用U-bund平台，有什么可以帮助您的吗？\",\n        sender: \"other\",\n        time: \"14:30\",\n        type: \"text\"\n      }\n    ],\n    merchant1: [\n      {\n        id: \"1\",\n        content: \"您好，关于Logo设计的需求我已经收到了\",\n        sender: \"other\",\n        time: \"10:15\",\n        type: \"text\"\n      },\n      {\n        id: \"2\",\n        content: \"好的，请问大概什么时候能完成？\",\n        sender: \"me\",\n        time: \"10:16\",\n        type: \"text\"\n      },\n      {\n        id: \"3\",\n        content: \"预计3-5个工作日，我会先做几个方案给您选择\",\n        sender: \"other\",\n        time: \"10:18\",\n        type: \"text\"\n      }\n    ],\n    merchant2: [\n      {\n        id: \"1\",\n        content: \"网站开发项目已经开始了，预计下周完成\",\n        sender: \"other\",\n        time: \"昨天\",\n        type: \"text\"\n      }\n    ]\n  });\n\n  const currentContact = contacts.find(c => c.id === activeContact);\n  const currentMessages = messages[activeContact] || [];\n\n  const handleSendMessage = () => {\n    if (messageInput.trim()) {\n      console.log(\"发送消息:\", messageInput);\n      setMessageInput(\"\");\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div className=\"fixed bottom-4 right-4 w-[640px] h-[768px] bg-white border border-slate-200 rounded-lg shadow-2xl z-[100] flex flex-col overflow-hidden\">\n      {/* 顶部标题栏 */}\n      <div className=\"bg-slate-50/80 border-b border-slate-200 px-6 py-4 flex items-center justify-between\">\n        <div className=\"flex items-center gap-3\">\n          <Bell className=\"h-5 w-5 text-slate-600\" />\n          <span className=\"text-slate-900 font-medium\">消息通知</span>\n        </div>\n        <div className=\"flex items-center gap-2\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={onMinimize}\n            className=\"h-8 w-8 p-0 hover:bg-slate-200/60\"\n          >\n            <Minus className=\"h-4 w-4\" />\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={onClose}\n            className=\"h-8 w-8 p-0 hover:bg-slate-200/60\"\n          >\n            <X className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"flex flex-1 overflow-hidden\">\n        {/* 左侧联系人列表 */}\n        <div className=\"w-64 border-r border-slate-200 bg-slate-50/30\">\n          <div className=\"p-4\">\n            <h3 className=\"text-sm font-medium text-slate-900 mb-3\">最近联系</h3>\n            <div className=\"space-y-2\">\n              {contacts.map((contact) => (\n                <div\n                  key={contact.id}\n                  onClick={() => setActiveContact(contact.id)}\n                  className={`flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors ${\n                    activeContact === contact.id\n                      ? 'bg-blue-50 border border-blue-200'\n                      : 'hover:bg-slate-100/60'\n                  }`}\n                >\n                  <div className=\"relative\">\n                    <Avatar className=\"h-10 w-10\">\n                      <AvatarImage src={contact.avatar} />\n                      <AvatarFallback>\n                        {contact.type === \"official\" ? <Bot className=\"h-5 w-5\" /> : <User className=\"h-5 w-5\" />}\n                      </AvatarFallback>\n                    </Avatar>\n                    <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ${\n                      contact.isOnline ? 'bg-green-500' : 'bg-slate-400'\n                    }`}></div>\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center justify-between mb-1\">\n                      <span className=\"text-sm font-medium text-slate-900 truncate\">\n                        {contact.name}\n                      </span>\n                      {contact.unreadCount > 0 && (\n                        <Badge className=\"h-5 w-5 p-0 flex items-center justify-center text-xs bg-red-500\">\n                          {contact.unreadCount}\n                        </Badge>\n                      )}\n                    </div>\n                    <p className=\"text-xs text-slate-600 truncate\">\n                      {contact.lastMessage}\n                    </p>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* 右侧聊天区域 */}\n        <div className=\"flex-1 flex flex-col\">\n          {/* 聊天头部 */}\n          {currentContact && (\n            <div className=\"border-b border-slate-200 p-4\">\n              <div className=\"flex items-center gap-3\">\n                <Avatar className=\"h-10 w-10\">\n                  <AvatarImage src={currentContact.avatar} />\n                  <AvatarFallback>\n                    {currentContact.type === \"official\" ? <Bot className=\"h-5 w-5\" /> : <User className=\"h-5 w-5\" />}\n                  </AvatarFallback>\n                </Avatar>\n                <div>\n                  <h4 className=\"font-medium text-slate-900\">{currentContact.name}</h4>\n                  <p className=\"text-sm text-slate-600\">\n                    {currentContact.isOnline ? '在线' : '离线'}\n                  </p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* 消息列表 */}\n          <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n            {currentMessages.map((message) => (\n              <div\n                key={message.id}\n                className={`flex ${message.sender === 'me' ? 'justify-end' : 'justify-start'}`}\n              >\n                <div\n                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${\n                    message.sender === 'me'\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-slate-100 text-slate-900'\n                  }`}\n                >\n                  <p className=\"text-sm\">{message.content}</p>\n                  <p className={`text-xs mt-1 ${\n                    message.sender === 'me' ? 'text-blue-100' : 'text-slate-500'\n                  }`}>\n                    {message.time}\n                  </p>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          {/* 输入框 */}\n          {currentContact?.type !== \"official\" && (\n            <div className=\"border-t border-slate-200 p-4\">\n              <div className=\"flex items-end gap-3\">\n                <div className=\"flex-1\">\n                  <textarea\n                    value={messageInput}\n                    onChange={(e) => setMessageInput(e.target.value)}\n                    onKeyPress={handleKeyPress}\n                    placeholder=\"输入消息...\"\n                    className=\"w-full px-3 py-2 border border-slate-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-300\"\n                    rows={1}\n                  />\n                </div>\n                <Button\n                  onClick={handleSendMessage}\n                  disabled={!messageInput.trim()}\n                  className=\"bg-blue-600 hover:bg-blue-700\"\n                >\n                  <Send className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;AAkCO,SAAS,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAmB;IACzE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,UAAU;IACV,MAAM,WAAsB;QAC1B;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,UAAU;YACV,aAAa;YACb,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YACJ,MAAM;YACN,QAAQ;YACR,UAAU;YACV,aAAa;YACb,aAAa;YACb,MAAM;QACR;QACA;YACE,IAAI;YAC<PERSON>,MAAM;YACN,QAAQ;YACR,UAAU;YACV,aAAa;YACb,aAAa;YACb,MAAM;QACR;KACD;IAED,SAAS;IACT,MAAM,CAAC,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAA6B;QACrD,UAAU;YACR;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,MAAM;YACR;SACD;QACD,WAAW;YACT;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,MAAM;YACR;YACA;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,MAAM;YACR;SACD;QACD,WAAW;YACT;gBACE,IAAI;gBACJ,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,MAAM;YACR;SACD;IACH;IAEA,MAAM,iBAAiB,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACnD,MAAM,kBAAkB,QAAQ,CAAC,cAAc,IAAI,EAAE;IAErD,MAAM,oBAAoB;QACxB,IAAI,aAAa,IAAI,IAAI;YACvB,QAAQ,GAAG,CAAC,SAAS;YACrB,gBAAgB;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6WAAC;QAAI,WAAU;;0BAEb,6WAAC;gBAAI,WAAU;;kCACb,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,sRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;0CAChB,6WAAC;gCAAK,WAAU;0CAA6B;;;;;;;;;;;;kCAE/C,6WAAC;wBAAI,WAAU;;0CACb,6WAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6WAAC,wRAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,6WAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6WAAC,gRAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAKnB,6WAAC;gBAAI,WAAU;;kCAEb,6WAAC;wBAAI,WAAU;kCACb,cAAA,6WAAC;4BAAI,WAAU;;8CACb,6WAAC;oCAAG,WAAU;8CAA0C;;;;;;8CACxD,6WAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6WAAC;4CAEC,SAAS,IAAM,iBAAiB,QAAQ,EAAE;4CAC1C,WAAW,CAAC,wEAAwE,EAClF,kBAAkB,QAAQ,EAAE,GACxB,sCACA,yBACJ;;8DAEF,6WAAC;oDAAI,WAAU;;sEACb,6WAAC,kIAAA,CAAA,SAAM;4DAAC,WAAU;;8EAChB,6WAAC,kIAAA,CAAA,cAAW;oEAAC,KAAK,QAAQ,MAAM;;;;;;8EAChC,6WAAC,kIAAA,CAAA,iBAAc;8EACZ,QAAQ,IAAI,KAAK,2BAAa,6WAAC,oRAAA,CAAA,MAAG;wEAAC,WAAU;;;;;6FAAe,6WAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAGjF,6WAAC;4DAAI,WAAW,CAAC,2EAA2E,EAC1F,QAAQ,QAAQ,GAAG,iBAAiB,gBACpC;;;;;;;;;;;;8DAEJ,6WAAC;oDAAI,WAAU;;sEACb,6WAAC;4DAAI,WAAU;;8EACb,6WAAC;oEAAK,WAAU;8EACb,QAAQ,IAAI;;;;;;gEAEd,QAAQ,WAAW,GAAG,mBACrB,6WAAC,iIAAA,CAAA,QAAK;oEAAC,WAAU;8EACd,QAAQ,WAAW;;;;;;;;;;;;sEAI1B,6WAAC;4DAAE,WAAU;sEACV,QAAQ,WAAW;;;;;;;;;;;;;2CA/BnB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;kCAyCzB,6WAAC;wBAAI,WAAU;;4BAEZ,gCACC,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,6WAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,eAAe,MAAM;;;;;;8DACvC,6WAAC,kIAAA,CAAA,iBAAc;8DACZ,eAAe,IAAI,KAAK,2BAAa,6WAAC,oRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;6EAAe,6WAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGxF,6WAAC;;8DACC,6WAAC;oDAAG,WAAU;8DAA8B,eAAe,IAAI;;;;;;8DAC/D,6WAAC;oDAAE,WAAU;8DACV,eAAe,QAAQ,GAAG,OAAO;;;;;;;;;;;;;;;;;;;;;;;0CAQ5C,6WAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,wBACpB,6WAAC;wCAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,MAAM,KAAK,OAAO,gBAAgB,iBAAiB;kDAE9E,cAAA,6WAAC;4CACC,WAAW,CAAC,0CAA0C,EACpD,QAAQ,MAAM,KAAK,OACf,2BACA,+BACJ;;8DAEF,6WAAC;oDAAE,WAAU;8DAAW,QAAQ,OAAO;;;;;;8DACvC,6WAAC;oDAAE,WAAW,CAAC,aAAa,EAC1B,QAAQ,MAAM,KAAK,OAAO,kBAAkB,kBAC5C;8DACC,QAAQ,IAAI;;;;;;;;;;;;uCAdZ,QAAQ,EAAE;;;;;;;;;;4BAsBpB,gBAAgB,SAAS,4BACxB,6WAAC;gCAAI,WAAU;0CACb,cAAA,6WAAC;oCAAI,WAAU;;sDACb,6WAAC;4CAAI,WAAU;sDACb,cAAA,6WAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDAC/C,YAAY;gDACZ,aAAY;gDACZ,WAAU;gDACV,MAAM;;;;;;;;;;;sDAGV,6WAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,CAAC,aAAa,IAAI;4CAC5B,WAAU;sDAEV,cAAA,6WAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlC", "debugId": null}}, {"offset": {"line": 1489, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/components/ChatFloatingButton.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { MessageCircle } from \"lucide-react\";\n\ninterface ChatFloatingButtonProps {\n  onClick: () => void;\n  notificationCount?: number;\n  isVisible?: boolean;\n}\n\nexport function ChatFloatingButton({ \n  onClick, \n  notificationCount = 0, \n  isVisible = true \n}: ChatFloatingButtonProps) {\n  if (!isVisible) return null;\n\n  return (\n    <div className=\"fixed bottom-6 right-6 z-50\">\n      <Button\n        onClick={onClick}\n        size=\"lg\"\n        className=\"relative h-14 w-14 rounded-full bg-blue-600 hover:bg-blue-700 shadow-lg hover:shadow-xl transition-all duration-300 group\"\n      >\n        <MessageCircle className=\"h-6 w-6 text-white group-hover:scale-110 transition-transform\" />\n        \n        {/* 通知数量徽章 */}\n        {notificationCount > 0 && (\n          <Badge className=\"absolute -top-2 -right-2 h-6 w-6 p-0 flex items-center justify-center text-xs bg-red-500 text-white border-2 border-white\">\n            {notificationCount > 99 ? '99+' : notificationCount}\n          </Badge>\n        )}\n        \n        {/* 脉冲动画 */}\n        <div className=\"absolute inset-0 rounded-full bg-blue-600 animate-ping opacity-20\"></div>\n      </Button>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAQO,SAAS,mBAAmB,EACjC,OAAO,EACP,oBAAoB,CAAC,EACrB,YAAY,IAAI,EACQ;IACxB,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6WAAC;QAAI,WAAU;kBACb,cAAA,6WAAC,kIAAA,CAAA,SAAM;YACL,SAAS;YACT,MAAK;YACL,WAAU;;8BAEV,6WAAC,4SAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;gBAGxB,oBAAoB,mBACnB,6WAAC,iIAAA,CAAA,QAAK;oBAAC,WAAU;8BACd,oBAAoB,KAAK,QAAQ;;;;;;8BAKtC,6WAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 1571, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/app/support/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { NavigationV78 } from \"@/components/NavigationV78\";\nimport { CategoryNavigation } from \"@/components/CategoryNavigation\";\nimport { PlaceholderPage } from \"@/components/PlaceholderPage\";\nimport { ChatWindow } from \"@/components/ChatWindow\";\nimport { ChatFloatingButton } from \"@/components/ChatFloatingButton\";\nimport { useRouter } from \"next/navigation\";\n\nexport default function Support() {\n  const router = useRouter();\n  const [activeCategory, setActiveCategory] = useState(\"popular\");\n  const [isChatOpen, setIsChatOpen] = useState(false);\n  const [notificationCount] = useState(3);\n\n  const handleNavigate = (page: string) => {\n    switch (page) {\n      case \"home\":\n      case \"landing\":\n        router.push(\"/\");\n        break;\n      case \"services\":\n        router.push(\"/services\");\n        break;\n      case \"orders\":\n        router.push(\"/orders\");\n        break;\n      case \"favorites\":\n        router.push(\"/favorites\");\n        break;\n      case \"support\":\n        router.push(\"/support\");\n        break;\n      case \"gallery\":\n        router.push(\"/gallery\");\n        break;\n      case \"login\":\n        router.push(\"/login\");\n        break;\n      case \"register\":\n        router.push(\"/register\");\n        break;\n      default:\n        break;\n    }\n  };\n\n  const handleSubCategoryClick = (category: string, subCategory: string) => {\n    setActiveCategory(category);\n    console.log(`已选择子分类: ${subCategory} 在分类: ${category}`);\n  };\n\n  const handleCategoryChange = (category: string) => {\n    setActiveCategory(category);\n    console.log(`已切换到分类: ${category}`);\n  };\n\n  const handleOpenChat = () => {\n    setIsChatOpen(true);\n  };\n\n  const handleCloseChat = () => {\n    setIsChatOpen(false);\n  };\n\n  const handleMinimizeChat = () => {\n    setIsChatOpen(false);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30\">\n      <NavigationV78 \n        currentPage=\"support\" \n        onNavigate={handleNavigate}\n        onOpenChat={handleOpenChat}\n      />\n      \n      <CategoryNavigation \n        activeCategory={activeCategory}\n        onCategoryChange={handleCategoryChange}\n        onSubCategoryClick={handleSubCategoryClick}\n      />\n      \n      <PlaceholderPage\n        title=\"定制服务\"\n        description=\"专业定制服务平台正在建设中，敬请期待...\"\n        iconColor=\"text-green-600\"\n        gradientColors=\"from-green-500/10 to-emerald-500/10\"\n        borderColor=\"border-green-200/30\"\n      />\n\n      {/* 聊天窗口 */}\n      <ChatWindow isOpen={isChatOpen} onClose={handleCloseChat} onMinimize={handleMinimizeChat} />\n      \n      {/* 聊天悬浮球 */}\n      <ChatFloatingButton \n        onClick={handleOpenChat}\n        notificationCount={notificationCount}\n        isVisible={!isChatOpen}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iQAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;g<PERSON><PERSON>,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF,KAAK;gBACH,OAAO,IAAI,CAAC;gBACZ;YACF;gBACE;QACJ;IACF;IAEA,MAAM,yBAAyB,CAAC,UAAkB;QAChD,kBAAkB;QAClB,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,MAAM,EAAE,UAAU;IACvD;IAEA,MAAM,uBAAuB,CAAC;QAC5B,kBAAkB;QAClB,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU;IACnC;IAEA,MAAM,iBAAiB;QACrB,cAAc;IAChB;IAEA,MAAM,kBAAkB;QACtB,cAAc;IAChB;IAEA,MAAM,qBAAqB;QACzB,cAAc;IAChB;IAEA,qBACE,6WAAC;QAAI,WAAU;;0BACb,6WAAC,mIAAA,CAAA,gBAAa;gBACZ,aAAY;gBACZ,YAAY;gBACZ,YAAY;;;;;;0BAGd,6WAAC,wIAAA,CAAA,qBAAkB;gBACjB,gBAAgB;gBAChB,kBAAkB;gBAClB,oBAAoB;;;;;;0BAGtB,6WAAC,qIAAA,CAAA,kBAAe;gBACd,OAAM;gBACN,aAAY;gBACZ,WAAU;gBACV,gBAAe;gBACf,aAAY;;;;;;0BAId,6WAAC,gIAAA,CAAA,aAAU;gBAAC,QAAQ;gBAAY,SAAS;gBAAiB,YAAY;;;;;;0BAGtE,6WAAC,wIAAA,CAAA,qBAAkB;gBACjB,SAAS;gBACT,mBAAmB;gBACnB,WAAW,CAAC;;;;;;;;;;;;AAIpB", "debugId": null}}]}