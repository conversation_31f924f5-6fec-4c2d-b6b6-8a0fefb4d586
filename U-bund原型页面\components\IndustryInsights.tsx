import { useState } from "react";
import { Card, CardContent } from "./ui/card";
import { Badge } from "./ui/badge";
import { Button } from "./ui/button";
import { ImageWithFallback } from "./figma/ImageWithFallback";
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  Users, 
  Star, 
  Eye,
  ChevronRight,
  Sparkles,
  Target,
  Award,
  DollarSign
} from "lucide-react";

interface IndustryInsightsProps {
  activeCategory: string;
}

export function IndustryInsights({ activeCategory }: IndustryInsightsProps) {
  const [activeTab, setActiveTab] = useState<"ads" | "analysis">("ads");

  // 行业广告数据
  const industryAds = {
    popular: [
      {
        id: "ad1",
        title: "2024年度最佳服务商招募",
        description: "成为U-bund认证专家，获得更多曝光机会",
        image: "https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=160&fit=crop",
        badge: "热门推广",
        cta: "立即申请",
        highlight: true
      },
      {
        id: "ad2", 
        title: "企业级定制服务",
        description: "专业团队为您提供一站式解决方案",
        image: "https://images.unsplash.com/photo-1551434678-e076c223a692?w=300&h=160&fit=crop",
        badge: "企业专享",
        cta: "了解详情"
      }
    ],
    design: [
      {
        id: "ad3",
        title: "Adobe创意套件培训",
        description: "从入门到精通，成为设计大师",
        image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=300&h=160&fit=crop",
        badge: "设计必备",
        cta: "立即学习",
        highlight: true
      },
      {
        id: "ad4",
        title: "品牌VI设计大赛",
        description: "参与设计大赛，赢取丰厚奖金",
        image: "https://images.unsplash.com/photo-1558655146-d09347e92766?w=300&h=160&fit=crop",
        badge: "限时活动", 
        cta: "参与比赛"
      }
    ],
    programming: [
      {
        id: "ad5",
        title: "AI开发训练营",
        description: "掌握最新AI技术，开启未来职业",
        image: "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=300&h=160&fit=crop",
        badge: "前沿技术",
        cta: "报名参加",
        highlight: true
      },
      {
        id: "ad6",
        title: "云计算解决方案",
        description: "专业云服务架构设计与部署",
        image: "https://images.unsplash.com/photo-1451187580459-43490279c0fa?w=300&h=160&fit=crop",
        badge: "技术领先",
        cta: "咨询方案"
      }
    ]
  };

  // 行业分析数据
  const industryAnalysis = {
    popular: {
      marketTrend: "+23.5%",
      totalServices: "12,847",
      avgPrice: "¥1,299",
      satisfaction: "4.8",
      insights: [
        { icon: TrendingUp, text: "设计类服务需求增长最快", trend: "up" },
        { icon: Users, text: "新注册服务商环比增加45%", trend: "up" },
        { icon: DollarSign, text: "平均客单价提升12%", trend: "up" },
        { icon: Star, text: "客户满意度创新高", trend: "stable" }
      ]
    },
    design: {
      marketTrend: "+31.2%",
      totalServices: "3,456",
      avgPrice: "¥899",
      satisfaction: "4.9",
      insights: [
        { icon: Sparkles, text: "Logo设计需求占比最高", trend: "up" },
        { icon: Target, text: "品牌整体设计服务走俏", trend: "up" },
        { icon: Award, text: "高端设计师供不应求", trend: "up" },
        { icon: Eye, text: "视觉设计审美要求提升", trend: "stable" }
      ]
    },
    programming: {
      marketTrend: "+28.7%",
      totalServices: "2,891",
      avgPrice: "¥2,599",
      satisfaction: "4.7",
      insights: [
        { icon: TrendingUp, text: "AI相关开发项目激增", trend: "up" },
        { icon: Users, text: "小程序开发需求稳定增长", trend: "up" },
        { icon: BarChart3, text: "全栈工程师最受欢迎", trend: "up" },
        { icon: Target, text: "企业数字化转型加速", trend: "stable" }
      ]
    }
  };

  const getCurrentAds = () => {
    return industryAds[activeCategory as keyof typeof industryAds] || industryAds.popular;
  };

  const getCurrentAnalysis = () => {
    return industryAnalysis[activeCategory as keyof typeof industryAnalysis] || industryAnalysis.popular;
  };

  const getCategoryName = (category: string) => {
    switch (category) {
      case "popular": return "热门行业";
      case "design": return "设计行业";
      case "programming": return "技术行业";
      case "marketing": return "营销行业";
      default: return "综合行业";
    }
  };

  const analysis = getCurrentAnalysis();
  const ads = getCurrentAds();

  return (
    <div className="space-y-6">
      {/* 标签页切换 */}
      <div className="flex items-center justify-between">
        <h3 className="text-slate-900 font-medium">
          {getCategoryName(activeCategory)}洞察
        </h3>
        <div className="flex bg-slate-100/80 rounded-lg p-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setActiveTab("ads")}
            className={`px-3 py-1 text-sm transition-all duration-200 ${
              activeTab === "ads" 
                ? 'bg-white shadow-sm text-slate-900' 
                : 'text-slate-600 hover:text-slate-900'
            }`}
          >
            行业广告
          </Button>
          <Button
            variant="ghost" 
            size="sm"
            onClick={() => setActiveTab("analysis")}
            className={`px-3 py-1 text-sm transition-all duration-200 ${
              activeTab === "analysis"
                ? 'bg-white shadow-sm text-slate-900'
                : 'text-slate-600 hover:text-slate-900'
            }`}
          >
            市场分析
          </Button>
        </div>
      </div>

      {/* 行业广告 */}
      {activeTab === "ads" && (
        <div className="grid gap-4 lg:grid-cols-2">
          {ads.map((ad) => (
            <Card 
              key={ad.id} 
              className={`group cursor-pointer transition-all duration-300 hover:shadow-lg border-slate-200/60 overflow-hidden ${
                ad.highlight ? 'ring-2 ring-blue-500/20 bg-gradient-to-br from-blue-50/30 to-indigo-50/30' : 'bg-white/70'
              }`}
            >
              <CardContent className="p-0">
                <div className="relative">
                  <ImageWithFallback
                    src={ad.image}
                    alt={ad.title}
                    className="w-full h-32 object-cover"
                  />
                  <div className="absolute top-2 left-2">
                    <Badge 
                      variant="secondary" 
                      className={`text-xs ${
                        ad.highlight 
                          ? 'bg-blue-600 text-white' 
                          : 'bg-white/90 text-slate-700'
                      }`}
                    >
                      {ad.badge}
                    </Badge>
                  </div>
                  {ad.highlight && (
                    <div className="absolute top-2 right-2">
                      <Sparkles className="h-4 w-4 text-yellow-500" />
                    </div>
                  )}
                </div>
                <div className="p-4">
                  <h4 className="font-medium text-slate-900 mb-1 group-hover:text-blue-600 transition-colors">
                    {ad.title}
                  </h4>
                  <p className="text-sm text-slate-600 mb-3 line-clamp-2">
                    {ad.description}
                  </p>
                  <Button 
                    size="sm" 
                    className="w-full group-hover:shadow-md transition-all duration-200"
                  >
                    {ad.cta}
                    <ChevronRight className="h-3 w-3 ml-1" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* 市场分析 */}
      {activeTab === "analysis" && (
        <div className="space-y-4">
          {/* 关键指标 */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="bg-gradient-to-br from-green-50/80 to-emerald-50/80 border-green-200/60">
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                </div>
                <div className="text-xl font-bold text-green-700">{analysis.marketTrend}</div>
                <div className="text-xs text-green-600">市场增长</div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-blue-50/80 to-indigo-50/80 border-blue-200/60">
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <BarChart3 className="h-5 w-5 text-blue-600" />
                </div>
                <div className="text-xl font-bold text-blue-700">{analysis.totalServices}</div>
                <div className="text-xs text-blue-600">活跃服务</div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-50/80 to-violet-50/80 border-purple-200/60">
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <DollarSign className="h-5 w-5 text-purple-600" />
                </div>
                <div className="text-xl font-bold text-purple-700">{analysis.avgPrice}</div>
                <div className="text-xs text-purple-600">平均价格</div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-orange-50/80 to-amber-50/80 border-orange-200/60">
              <CardContent className="p-4 text-center">
                <div className="flex items-center justify-center mb-2">
                  <Star className="h-5 w-5 text-orange-600" />
                </div>
                <div className="text-xl font-bold text-orange-700">{analysis.satisfaction}</div>
                <div className="text-xs text-orange-600">满意度</div>
              </CardContent>
            </Card>
          </div>

          {/* 市场洞察 */}
          <Card className="bg-white/70 backdrop-blur-sm border-slate-200/60">
            <CardContent className="p-6">
              <h4 className="font-medium text-slate-900 mb-4 flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-blue-600" />
                市场洞察
              </h4>
              <div className="space-y-3">
                {analysis.insights.map((insight, index) => {
                  const Icon = insight.icon;
                  return (
                    <div key={index} className="flex items-center gap-3 p-3 rounded-lg bg-slate-50/80 hover:bg-slate-100/80 transition-colors">
                      <div className={`p-2 rounded-full ${
                        insight.trend === 'up' 
                          ? 'bg-green-100 text-green-600' 
                          : insight.trend === 'down'
                          ? 'bg-red-100 text-red-600'
                          : 'bg-blue-100 text-blue-600'
                      }`}>
                        <Icon className="h-4 w-4" />
                      </div>
                      <span className="text-slate-700 flex-1">{insight.text}</span>
                      {insight.trend === 'up' && (
                        <TrendingUp className="h-4 w-4 text-green-500" />
                      )}
                      {insight.trend === 'down' && (
                        <TrendingDown className="h-4 w-4 text-red-500" />
                      )}
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}