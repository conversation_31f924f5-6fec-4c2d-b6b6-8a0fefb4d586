import { Package } from "lucide-react";

interface PlaceholderPageProps {
  title: string;
  description: string;
  iconColor: string;
  gradientColors: string;
  borderColor: string;
}

export function PlaceholderPage({ 
  title, 
  description, 
  iconColor, 
  gradientColors, 
  borderColor 
}: PlaceholderPageProps) {
  return (
    <div className="max-w-7xl mx-auto px-4 py-12 text-center">
      <div className="relative inline-block mb-4">
        <div className={`absolute inset-0 ${iconColor}/10 rounded-xl blur-sm`}></div>
        <div className={`relative p-3 bg-gradient-to-br ${gradientColors} rounded-xl border ${borderColor}`}>
          <Package className={`h-8 w-8 ${iconColor}`} />
        </div>
      </div>
      <h2 className="text-slate-900 mb-2">{title}</h2>
      <p className="text-slate-600">{description}</p>
    </div>
  );
}
