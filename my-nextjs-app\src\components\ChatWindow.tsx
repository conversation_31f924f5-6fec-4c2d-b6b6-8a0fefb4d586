import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { 
  X, 
  Minus, 
  Send, 
  Bell,
  MessageCircle,
  User,
  Bot
} from "lucide-react";

interface ChatWindowProps {
  isOpen: boolean;
  onClose: () => void;
  onMinimize: () => void;
}

interface Contact {
  id: string;
  name: string;
  avatar: string;
  isOnline: boolean;
  lastMessage: string;
  unreadCount: number;
  type: "user" | "official";
}

interface Message {
  id: string;
  content: string;
  sender: "me" | "other";
  time: string;
  type: "text" | "image" | "file";
}

export function ChatWindow({ isOpen, onClose, onMinimize }: ChatWindowProps) {
  const [activeContact, setActiveContact] = useState("official");
  const [messageInput, setMessageInput] = useState("");

  // 模拟联系人数据
  const contacts: Contact[] = [
    {
      id: "official",
      name: "官方客服",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",
      isOnline: true,
      lastMessage: "有什么可以帮助您的吗？",
      unreadCount: 1,
      type: "official"
    },
    {
      id: "merchant1",
      name: "设计师小王",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",
      isOnline: true,
      lastMessage: "好的，我马上开始设计",
      unreadCount: 2,
      type: "user"
    },
    {
      id: "merchant2",
      name: "程序员老李",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",
      isOnline: false,
      lastMessage: "网站开发进度如何？",
      unreadCount: 0,
      type: "user"
    }
  ];

  // 模拟消息数据
  const [messages] = useState<Record<string, Message[]>>({
    official: [
      {
        id: "1",
        content: "您好！欢迎使用U-bund平台，有什么可以帮助您的吗？",
        sender: "other",
        time: "14:30",
        type: "text"
      }
    ],
    merchant1: [
      {
        id: "1",
        content: "您好，关于Logo设计的需求我已经收到了",
        sender: "other",
        time: "10:15",
        type: "text"
      },
      {
        id: "2",
        content: "好的，请问大概什么时候能完成？",
        sender: "me",
        time: "10:16",
        type: "text"
      },
      {
        id: "3",
        content: "预计3-5个工作日，我会先做几个方案给您选择",
        sender: "other",
        time: "10:18",
        type: "text"
      }
    ],
    merchant2: [
      {
        id: "1",
        content: "网站开发项目已经开始了，预计下周完成",
        sender: "other",
        time: "昨天",
        type: "text"
      }
    ]
  });

  const currentContact = contacts.find(c => c.id === activeContact);
  const currentMessages = messages[activeContact] || [];

  const handleSendMessage = () => {
    if (messageInput.trim()) {
      console.log("发送消息:", messageInput);
      setMessageInput("");
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed bottom-4 right-4 w-[640px] h-[768px] bg-white border border-slate-200 rounded-lg shadow-2xl z-[100] flex flex-col overflow-hidden">
      {/* 顶部标题栏 */}
      <div className="bg-slate-50/80 border-b border-slate-200 px-6 py-4 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Bell className="h-5 w-5 text-slate-600" />
          <span className="text-slate-900 font-medium">消息通知</span>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onMinimize}
            className="h-8 w-8 p-0 hover:bg-slate-200/60"
          >
            <Minus className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0 hover:bg-slate-200/60"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* 左侧联系人列表 */}
        <div className="w-64 border-r border-slate-200 bg-slate-50/30">
          <div className="p-4">
            <h3 className="text-sm font-medium text-slate-900 mb-3">最近联系</h3>
            <div className="space-y-2">
              {contacts.map((contact) => (
                <div
                  key={contact.id}
                  onClick={() => setActiveContact(contact.id)}
                  className={`flex items-center gap-3 p-3 rounded-lg cursor-pointer transition-colors ${
                    activeContact === contact.id
                      ? 'bg-blue-50 border border-blue-200'
                      : 'hover:bg-slate-100/60'
                  }`}
                >
                  <div className="relative">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={contact.avatar} />
                      <AvatarFallback>
                        {contact.type === "official" ? <Bot className="h-5 w-5" /> : <User className="h-5 w-5" />}
                      </AvatarFallback>
                    </Avatar>
                    <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ${
                      contact.isOnline ? 'bg-green-500' : 'bg-slate-400'
                    }`}></div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-sm font-medium text-slate-900 truncate">
                        {contact.name}
                      </span>
                      {contact.unreadCount > 0 && (
                        <Badge className="h-5 w-5 p-0 flex items-center justify-center text-xs bg-red-500">
                          {contact.unreadCount}
                        </Badge>
                      )}
                    </div>
                    <p className="text-xs text-slate-600 truncate">
                      {contact.lastMessage}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 右侧聊天区域 */}
        <div className="flex-1 flex flex-col">
          {/* 聊天头部 */}
          {currentContact && (
            <div className="border-b border-slate-200 p-4">
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10">
                  <AvatarImage src={currentContact.avatar} />
                  <AvatarFallback>
                    {currentContact.type === "official" ? <Bot className="h-5 w-5" /> : <User className="h-5 w-5" />}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h4 className="font-medium text-slate-900">{currentContact.name}</h4>
                  <p className="text-sm text-slate-600">
                    {currentContact.isOnline ? '在线' : '离线'}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* 消息列表 */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {currentMessages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'me' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.sender === 'me'
                      ? 'bg-blue-600 text-white'
                      : 'bg-slate-100 text-slate-900'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <p className={`text-xs mt-1 ${
                    message.sender === 'me' ? 'text-blue-100' : 'text-slate-500'
                  }`}>
                    {message.time}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* 输入框 */}
          {currentContact?.type !== "official" && (
            <div className="border-t border-slate-200 p-4">
              <div className="flex items-end gap-3">
                <div className="flex-1">
                  <textarea
                    value={messageInput}
                    onChange={(e) => setMessageInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="输入消息..."
                    className="w-full px-3 py-2 border border-slate-200 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-300"
                    rows={1}
                  />
                </div>
                <Button
                  onClick={handleSendMessage}
                  disabled={!messageInput.trim()}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
