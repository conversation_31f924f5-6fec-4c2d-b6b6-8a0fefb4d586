import { useState } from "react";
import { But<PERSON> } from "./ui/button";
import { ImageWithFallback } from "./figma/ImageWithFallback";
import { 
  Bell, 
  Settings, 
  ChevronDown, 
  Package,
  User,
  Star,
  FileText,
  MapPin,
  Palette,
  Search,
  Compass
} from "lucide-react";

interface NavigationProps {
  currentPage?: "home" | "services" | "orders" | "support" | "gallery";
  onNavigate?: (page: string) => void;
}

export function Navigation({ currentPage = "home", onNavigate }: NavigationProps) {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const navigationItems: Array<{ id: string; label: string; icon: any }> = [];

  const handleNavigate = (page: string) => {
    onNavigate?.(page);
    setShowUserMenu(false);
  };

  return (
    <nav className="bg-white/70 backdrop-blur-xl border-b border-slate-200/60 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* 左侧 - Logo和导航 */}
          <div className="flex items-center gap-8">
            {/* Logo */}
            <div className="flex items-center gap-3 cursor-pointer" onClick={() => handleNavigate("home")}>
              <div className="relative">
                <div className="absolute inset-0 bg-blue-500/10 rounded-xl blur-sm"></div>
                <div className="relative p-2 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-xl border border-blue-200/30">
                  <Package className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <span className="text-slate-900 text-lg font-bold tracking-tight">U-bund</span>
            </div>

            {/* 导航下拉菜单 - 移动版本的实现 */}
            <div className="hidden md:flex items-center gap-6">
              {/* 下拉菜单1 - 服务分类 */}
              <div className="relative group">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-slate-600 hover:text-slate-900 hover:bg-slate-50/80 flex items-center gap-1"
                >
                  服务分类
                  <ChevronDown className="h-3 w-3" />
                </Button>
                <div className="absolute top-full left-0 mt-2 w-48 bg-white/95 backdrop-blur-sm border border-slate-200/60 rounded-xl shadow-lg py-2 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleNavigate("services")}
                    className="w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80"
                  >
                    热门服务
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleNavigate("services")}
                    className="w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80"
                  >
                    设计服务
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleNavigate("services")}
                    className="w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80"
                  >
                    技术开发
                  </Button>
                </div>
              </div>

              {/* 下拉菜单2 - 创作者中心 */}
              <div className="relative group">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-slate-600 hover:text-slate-900 hover:bg-slate-50/80 flex items-center gap-1"
                >
                  创作者中心
                  <ChevronDown className="h-3 w-3" />
                </Button>
                <div className="absolute top-full left-0 mt-2 w-48 bg-white/95 backdrop-blur-sm border border-slate-200/60 rounded-xl shadow-lg py-2 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80"
                  >
                    成为创作者
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80"
                  >
                    创作者学院
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80"
                  >
                    收益管理
                  </Button>
                </div>
              </div>

              {/* 下拉菜单3 - 企业服务 */}
              <div className="relative group">
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-slate-600 hover:text-slate-900 hover:bg-slate-50/80 flex items-center gap-1"
                >
                  企业服务
                  <ChevronDown className="h-3 w-3" />
                </Button>
                <div className="absolute top-full left-0 mt-2 w-48 bg-white/95 backdrop-blur-sm border border-slate-200/60 rounded-xl shadow-lg py-2 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80"
                  >
                    企业采购
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80"
                  >
                    团队协作
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80"
                  >
                    API接入
                  </Button>
                </div>
              </div>
            </div>

          </div>

          {/* 中间区域 - 根据页面类型显示不同内容 */}
          <div className="flex-1 max-w-3xl mx-8 hidden lg:flex items-center gap-4">
            {/* 首页显示搜索框 */}
            {currentPage === "home" && (
              <div className="relative flex-1">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-slate-400" />
                </div>
                <input
                  type="text"
                  placeholder="搜索商品、服务、作品..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 bg-white/80 border border-slate-200/60 rounded-full text-[rgba(76,114,169,1)] placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-300 transition-all duration-200 backdrop-blur-sm"
                />
              </div>
            )}
            
            {/* 其他页面显示导航按钮 */}
            {currentPage !== "home" && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleNavigate("services")}
                  className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                    currentPage === "services"
                      ? 'bg-blue-50/80 text-blue-700 shadow-sm' 
                      : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50/80'
                  }`}
                >
                  服务市场
                </Button>
                
                <div className="relative flex-1 max-w-md">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-4 w-4 text-slate-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="搜索服务..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 bg-white/80 border border-slate-200/60 rounded-full text-[rgba(76,114,169,1)] placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-300 transition-all duration-200 backdrop-blur-sm"
                  />
                </div>
              </>
            )}
            
            {/* 定制服务按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleNavigate("support")}
              className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                currentPage === "support"
                  ? 'bg-blue-50/80 text-blue-700 shadow-sm' 
                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50/80'
              }`}
            >
              <Palette className="h-4 w-4" />
              定制服务
            </Button>
            
            {/* 探索按钮 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleNavigate("gallery")}
              className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                currentPage === "gallery"
                  ? 'bg-blue-50/80 text-blue-700 shadow-sm' 
                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-50/80'
              }`}
            >
              <Compass className="h-4 w-4" />
              探索
            </Button>
          </div>

          {/* 右侧 - 用户操作 */}
          <div className="flex items-center gap-3">
            {/* 移动端搜索按钮 */}
            <div className="lg:hidden">
              <Button
                variant="ghost"
                size="sm"
                className="p-2 hover:bg-slate-50/80 transition-colors duration-200"
              >
                <Search className="h-5 w-5 text-slate-600" />
              </Button>
            </div>

            {/* 注册和登录按钮 - 只在首页显示 */}
            {currentPage === "home" && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  className="hidden sm:flex bg-white/80 border-slate-200/60 text-slate-600 hover:text-slate-900 hover:bg-white"
                >
                  注册
                </Button>

                <Button
                  size="sm"
                  className="hidden sm:flex bg-blue-600 hover:bg-blue-700 text-white"
                >
                  登录
                </Button>
              </>
            )}

            {/* 通知 */}
            <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                className="p-2 hover:bg-slate-50/80 transition-colors duration-200 relative"
              >
                <Bell className="h-5 w-5 text-slate-600" />
                {/* 通知小红点 */}
                <div className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></div>
              </Button>
            </div>

            {/* 收藏 */}
            <Button
              variant="ghost"
              size="sm"
              className="p-2 hover:bg-slate-50/80 transition-colors duration-200"
            >
              <Star className="h-5 w-5 text-slate-600" />
            </Button>

            {/* 订单 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleNavigate("orders")}
              className={`p-2 transition-colors duration-200 ${
                currentPage === "orders"
                  ? 'bg-blue-50/80 text-blue-700' 
                  : 'text-slate-600 hover:bg-slate-50/80 hover:text-slate-900'
              }`}
            >
              <Package className="h-5 w-5" />
            </Button>

            {/* 用户菜单 - 在非首页显示 */}
            {currentPage !== "home" && (
              <div className="relative">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center gap-2 p-1 pr-2 hover:bg-slate-50/80 transition-colors duration-200"
                >
                  <div className="relative">
                    <ImageWithFallback
                      src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces"
                      alt="用户头像"
                      className="w-8 h-8 rounded-full object-cover border border-slate-200/60"
                    />
                    <div className="absolute -top-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                  </div>
                  <span className="hidden sm:block text-slate-700 text-sm">张小明</span>
                  <ChevronDown className={`h-4 w-4 text-slate-400 transition-transform duration-200 ${
                    showUserMenu ? 'rotate-180' : ''
                  }`} />
                </Button>

                {/* 用户下拉菜单 */}
                {showUserMenu && (
                  <div className="absolute right-0 top-full mt-2 w-48 bg-white/90 backdrop-blur-sm border border-slate-200/60 rounded-xl shadow-lg py-2 z-[100]">
                    <div className="px-4 py-3 border-b border-slate-200/60">
                      <p className="text-slate-900 text-sm">张小明</p>
                      <p className="text-slate-500 text-xs"><EMAIL></p>
                    </div>
                    <div className="py-1">

                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80"
                      >
                        <User className="h-4 w-4 mr-3" />
                        个人资料
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80"
                      >
                        <Star className="h-4 w-4 mr-3" />
                        我的收藏
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80"
                      >
                        <FileText className="h-4 w-4 mr-3" />
                        售后服务
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80"
                      >
                        <Settings className="h-4 w-4 mr-3" />
                        账户设置
                      </Button>
                    </div>
                    <div className="border-t border-slate-200/60 pt-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-full justify-start px-4 py-2 text-red-600 hover:text-red-700 hover:bg-red-50/80"
                      >
                        退出登录
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}