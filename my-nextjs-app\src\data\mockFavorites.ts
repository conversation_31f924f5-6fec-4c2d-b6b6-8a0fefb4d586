import { FavoriteItem } from "../types/favorites";

export const mockFavorites: FavoriteItem[] = [
  {
    id: "fav_1",
    title: "企业品牌Logo设计 + VI视觉识别系统",
    description: "专业Logo设计，包含完整VI手册，商用授权，多方案对比选择",
    price: 8999,
    originalPrice: 12999,
    image: "https://images.unsplash.com/photo-1634942537034-2531766767d1?w=400&h=400&fit=crop",
    creator: {
      name: "墨刻品牌设计",
      avatar: "https://images.unsplash.com/photo-1472214103451-9374bd1c798e?w=100&h=100&fit=crop&crop=faces",
      isOnline: true,
      location: "上海"
    },
    rating: 4.9,
    reviewCount: 234,
    tags: ["U pro", "好评Top"],
    category: "图像设计",
    addedAt: "2025-01-25"
  },
  {
    id: "fav_2",
    title: "响应式企业官网定制开发",
    description: "React技术栈，响应式设计，SEO优化，包含后台管理系统",
    price: 15999,
    image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=400&fit=crop",
    creator: {
      name: "码猿科技团队",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",
      isOnline: false,
      location: "北京"
    },
    rating: 4.8,
    reviewCount: 156,
    tags: ["技能之星"],
    category: "编程技术",
    addedAt: "2025-01-24"
  }
];
