'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { AuthAPI, AuthResponse, LoginRequest, RegisterRequest } from '@/lib/auth-api';
import { ApiClientError } from '@/lib/api-client';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/error-handler';
import apiClient from '@/lib/api-client';

// 认证状态类型
interface AuthState {
  user: AuthResponse['user'] | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

// 认证上下文类型
interface AuthContextType extends AuthState {
  login: (data: LoginRequest) => Promise<boolean>;
  register: (data: RegisterRequest) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<void>;
  updateUser: (user: Partial<AuthResponse['user']>) => void;
}

// 创建认证上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 本地存储键名
const TOKEN_KEY = 'auth_token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const USER_KEY = 'auth_user';

// 认证提供者组件
interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    token: null,
    isLoading: true,
    isAuthenticated: false,
  });

  // 初始化认证状态
  useEffect(() => {
    initializeAuth();
  }, []);

  // 初始化认证
  const initializeAuth = async () => {
    try {
      const token = localStorage.getItem(TOKEN_KEY);
      const userStr = localStorage.getItem(USER_KEY);

      if (token && userStr) {
        const user = JSON.parse(userStr);
        
        // 设置API客户端的认证token
        apiClient.setAuthToken(token);
        
        // 验证token是否仍然有效
        try {
          await AuthAPI.validateToken();
          
          setAuthState({
            user,
            token,
            isLoading: false,
            isAuthenticated: true,
          });
        } catch (error) {
          // Token无效，尝试刷新
          await tryRefreshToken();
        }
      } else {
        setAuthState(prev => ({
          ...prev,
          isLoading: false,
        }));
      }
    } catch (error) {
      console.error('Initialize auth error:', error);
      clearAuthData();
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
      }));
    }
  };

  // 尝试刷新token
  const tryRefreshToken = async () => {
    try {
      const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);
      
      if (refreshToken) {
        const response = await AuthAPI.refreshToken({ refreshToken });
        
        if (response.success && response.data) {
          saveAuthData(response.data);
          setAuthState({
            user: response.data.user,
            token: response.data.token,
            isLoading: false,
            isAuthenticated: true,
          });
          return;
        }
      }
      
      // 刷新失败，清除认证数据
      clearAuthData();
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
      }));
    } catch (error) {
      clearAuthData();
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
      }));
    }
  };

  // 保存认证数据
  const saveAuthData = (authData: AuthResponse) => {
    localStorage.setItem(TOKEN_KEY, authData.token);
    localStorage.setItem(USER_KEY, JSON.stringify(authData.user));
    
    if (authData.refreshToken) {
      localStorage.setItem(REFRESH_TOKEN_KEY, authData.refreshToken);
    }
    
    // 设置API客户端的认证token
    apiClient.setAuthToken(authData.token);
  };

  // 清除认证数据
  const clearAuthData = () => {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    localStorage.removeItem(USER_KEY);
    
    // 移除API客户端的认证token
    apiClient.removeAuthToken();
  };

  // 登录
  const login = async (data: LoginRequest): Promise<boolean> => {
    try {
      const response = await AuthAPI.login(data);
      
      if (response.success && response.data) {
        saveAuthData(response.data);
        
        setAuthState({
          user: response.data.user,
          token: response.data.token,
          isLoading: false,
          isAuthenticated: true,
        });
        
        ErrorHandler.showSuccess('登录成功！');
        return true;
      }
      
      return false;
    } catch (error) {
      if (error instanceof ApiClientError) {
        ErrorHandler.showError(error);
      } else {
        ErrorHandler.showError('登录失败，请重试');
      }
      return false;
    }
  };

  // 注册
  const register = async (data: RegisterRequest): Promise<boolean> => {
    try {
      const response = await AuthAPI.register(data);
      
      if (response.success && response.data) {
        saveAuthData(response.data);
        
        setAuthState({
          user: response.data.user,
          token: response.data.token,
          isLoading: false,
          isAuthenticated: true,
        });
        
        ErrorHandler.showSuccess('注册成功！');
        return true;
      }
      
      return false;
    } catch (error) {
      if (error instanceof ApiClientError) {
        ErrorHandler.showError(error);
      } else {
        ErrorHandler.showError('注册失败，请重试');
      }
      return false;
    }
  };

  // 登出
  const logout = async () => {
    try {
      await AuthAPI.logout();
    } catch (error) {
      // 即使登出API失败，也要清除本地数据
      console.error('Logout API error:', error);
    } finally {
      clearAuthData();
      
      setAuthState({
        user: null,
        token: null,
        isLoading: false,
        isAuthenticated: false,
      });
      
      ErrorHandler.showSuccess('已退出登录');
    }
  };

  // 刷新认证状态
  const refreshAuth = async () => {
    try {
      const response = await AuthAPI.getCurrentUser();
      
      if (response.success && response.data) {
        const updatedUser = response.data;
        localStorage.setItem(USER_KEY, JSON.stringify(updatedUser));
        
        setAuthState(prev => ({
          ...prev,
          user: updatedUser,
        }));
      }
    } catch (error) {
      console.error('Refresh auth error:', error);
      // 如果获取用户信息失败，可能token已过期
      if (error instanceof ApiClientError && ErrorHandler.isAuthError(error)) {
        await logout();
      }
    }
  };

  // 更新用户信息
  const updateUser = (userData: Partial<AuthResponse['user']>) => {
    setAuthState(prev => {
      if (prev.user) {
        const updatedUser = { ...prev.user, ...userData };
        localStorage.setItem(USER_KEY, JSON.stringify(updatedUser));
        
        return {
          ...prev,
          user: updatedUser,
        };
      }
      return prev;
    });
  };

  const contextValue: AuthContextType = {
    ...authState,
    login,
    register,
    logout,
    refreshAuth,
    updateUser,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// 使用认证上下文的Hook
export function useAuth() {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
}

// 检查是否已认证的Hook
export function useRequireAuth() {
  const { isAuthenticated, isLoading } = useAuth();
  
  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      // 可以在这里重定向到登录页面
      ErrorHandler.showWarning('请先登录');
    }
  }, [isAuthenticated, isLoading]);
  
  return { isAuthenticated, isLoading };
}

export default AuthContext;
