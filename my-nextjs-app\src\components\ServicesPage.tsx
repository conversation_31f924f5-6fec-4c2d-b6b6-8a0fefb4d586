import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Search, 
  Filter,
  Grid,
  List,
  Star,
  Heart,
  ShoppingCart,
  Sparkles,
  TrendingUp,
  Award,
  Zap,
  ChevronDown,
  SlidersHorizontal,
  User,
  Bot,
  X,
  Home,
  MapPin
} from "lucide-react";

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  rating: number;
  reviews: number;
  category: string;
  tags: string[];
  isBestseller?: boolean;
  isNew?: boolean;
  creator: {
    name: string;
    avatar: string;
    verified?: boolean;
    location?: string;
  };
  favoriteCount: number;
  specialBadge?: "upro" | "top" | "star";
}

const mockProducts: Product[] = [
  {
    id: "1",
    name: "✨绝美品牌视觉设计，让你的LOGO成为爆款记忆点！",
    description: "姐妹们！这个设计师真的太会了😍 从LOGO到整套VI，各种风格都能拿捏住，原创度100%，商用无忧虑～绝对是品牌升级的必备神器！",
    price: 999,
    image: "https://images.unsplash.com/photo-1558655146-d09347e92766?w=400&h=400&fit=crop",
    rating: 5.0,
    reviews: 1234,
    category: "设计",
    tags: ["热门", "原创"],
    isBestseller: true,
    isNew: false,
    creator: {
      name: "设计师小王",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "北京"
    },
    favoriteCount: 892,
    specialBadge: "upro"
  },
  {
    id: "2",
    name: "🚀全栈网站开发YYDS！React+Node.js技术栈，让你的网站飞起来",
    description: "宝贝们看过来！这位程序员小哥哥技术真的绝了🔥 响应式设计+全栈开发+一键部署，从前端到后端全搞定，网站速度嗖嗖的，用户体验直接拉满！",
    price: 4999,
    image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=400&fit=crop",
    rating: 4.9,
    reviews: 856,
    category: "技术",
    tags: ["专业", "全栈"],
    isBestseller: true,
    creator: {
      name: "程序员老李",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "上海"
    },
    favoriteCount: 634,
    specialBadge: "top"
  },
  {
    id: "3",
    name: "📱小程序开发专家！微信生态全覆盖，让你的生意更上一层楼",
    description: "小程序界的扛把子！🏆 从商城到工具类，各种类型都能搞定，用户体验丝滑到飞起，后台管理系统也是一应俱全！",
    price: 2999,
    image: "https://images.unsplash.com/photo-1551650975-87deedd944c3?w=400&h=400&fit=crop",
    rating: 4.8,
    reviews: 445,
    category: "技术",
    tags: ["小程序", "微信"],
    creator: {
      name: "小程序达人",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "深圳"
    },
    favoriteCount: 378,
    specialBadge: "star"
  },
  {
    id: "4",
    name: "🎨UI设计大神在线！让你的界面颜值爆表，用户爱不释手",
    description: "设计界的颜值担当！✨ 从APP到网页，每一个像素都精雕细琢，交互设计更是让人眼前一亮，保证让你的产品脱颖而出！",
    price: 1999,
    image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=400&fit=crop",
    rating: 4.9,
    reviews: 667,
    category: "设计",
    tags: ["UI设计", "交互"],
    creator: {
      name: "UI设计师小美",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616c45e1f94?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "杭州"
    },
    favoriteCount: 523,
    specialBadge: "upro"
  }
];

interface ServicesPageProps {
  activeCategory?: string;
  activeSubCategory?: string | null;
  onCategoryChange?: (category: string) => void;
  onSubCategoryChange?: (subCategory: string | null) => void;
  onViewProductDetail?: (productId: string) => void;
}

export function ServicesPage({ 
  activeCategory = "popular", 
  activeSubCategory, 
  onCategoryChange, 
  onSubCategoryChange,
  onViewProductDetail 
}: ServicesPageProps) {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [favorites, setFavorites] = useState<string[]>([]);
  
  // 快速筛选状态
  const [quickFilters, setQuickFilters] = useState({
    uproServices: false,
    onlineOnly: false
  });

  // 筛选产品
  const filteredProducts = mockProducts.filter(product => {
    let matchesCategory = true;
    
    // 根据分类筛选
    switch (activeCategory) {
      case "popular":
        matchesCategory = product.tags.includes("热门") || product.isBestseller;
        break;
      case "design":
        matchesCategory = product.category === "设计";
        break;
      case "tech":
        matchesCategory = product.category === "技术";
        break;
      default:
        matchesCategory = true;
    }

    // 快速筛选逻辑
    let matchesQuickFilters = true;
    
    if (quickFilters.uproServices) {
      matchesQuickFilters = matchesQuickFilters && product.specialBadge === "upro";
    }
    
    if (quickFilters.onlineOnly) {
      matchesQuickFilters = matchesQuickFilters && product.creator.verified;
    }

    return matchesCategory && matchesQuickFilters;
  });

  const formatPrice = (price: number) => `${price}起`;

  const toggleFavorite = (productId: string) => {
    setFavorites(prev => 
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* 页面标题和筛选器 */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-2xl font-bold text-slate-900 mb-2">专业服务</h1>
            <p className="text-slate-600">发现优质服务，找到专业的人做专业的事</p>
          </div>
          
          {/* 视图切换 */}
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === "grid" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("grid")}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "outline"}
              size="sm"
              onClick={() => setViewMode("list")}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* 快速筛选 */}
        <div className="flex items-center gap-4 mb-6">
          <Button
            variant={quickFilters.uproServices ? "default" : "outline"}
            size="sm"
            onClick={() => setQuickFilters(prev => ({ ...prev, uproServices: !prev.uproServices }))}
            className="flex items-center gap-2"
          >
            <Award className="h-4 w-4" />
            U Pro服务
          </Button>
          <Button
            variant={quickFilters.onlineOnly ? "default" : "outline"}
            size="sm"
            onClick={() => setQuickFilters(prev => ({ ...prev, onlineOnly: !prev.onlineOnly }))}
            className="flex items-center gap-2"
          >
            <Zap className="h-4 w-4" />
            在线服务商
          </Button>
        </div>
      </div>

      {/* 产品网格 */}
      <div className={`grid gap-6 ${
        viewMode === "grid" 
          ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" 
          : "grid-cols-1"
        }`}>
        {filteredProducts.map((product) => (
          <Card 
            key={product.id} 
            className="group bg-white/90 backdrop-blur-sm border-slate-200/60 hover:shadow-xl hover:shadow-slate-200/40 transition-all duration-300 cursor-pointer overflow-hidden"
            onClick={() => onViewProductDetail?.(product.id)}
          >
            <CardContent className="p-0">
              {/* 商品图片 */}
              <div className="relative aspect-square overflow-hidden">
                <img 
                  src={product.image} 
                  alt={product.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                
                {/* 特殊标签 */}
                <div className="absolute top-3 left-3 flex flex-col gap-2">
                  {product.specialBadge === "upro" && (
                    <Badge className="bg-gradient-to-r from-purple-600 to-blue-600 text-white border-0 shadow-lg">
                      <Sparkles className="h-3 w-3 mr-1" />
                      U pro
                    </Badge>
                  )}
                  {product.specialBadge === "top" && (
                    <Badge className="bg-gradient-to-r from-orange-500 to-red-500 text-white border-0 shadow-lg">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      好评Top
                    </Badge>
                  )}
                  {product.isBestseller && (
                    <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 text-white border-0 shadow-lg">
                      热门
                    </Badge>
                  )}
                </div>

                {/* 收藏按钮 */}
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute top-3 right-3 h-8 w-8 p-0 bg-white/80 hover:bg-white backdrop-blur-sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleFavorite(product.id);
                  }}
                >
                  <Heart 
                    className={`h-4 w-4 ${
                      favorites.includes(product.id) 
                        ? 'text-red-500 fill-current' 
                        : 'text-slate-600'
                    }`} 
                  />
                </Button>
              </div>
              
              {/* 商品信息 */}
              <div className="p-4">
                {/* 创作者信息 */}
                <div className="flex items-center gap-2 mb-3">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={product.creator.avatar} />
                    <AvatarFallback className="text-xs">{product.creator.name[0]}</AvatarFallback>
                  </Avatar>
                  <span className="text-sm text-slate-600">{product.creator.name}</span>
                  {product.creator.verified && (
                    <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
                      认证
                    </Badge>
                  )}
                  <div className="flex items-center gap-1 ml-auto">
                    <Star className="h-3 w-3 text-yellow-500 fill-current" />
                    <span className="text-xs text-slate-600">{product.rating}</span>
                    <span className="text-xs text-slate-500">({product.reviews})</span>
                  </div>
                </div>
                
                {/* 服务名称 */}
                <h3 className="text-slate-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors leading-tight">
                  {product.name}
                </h3>
                
                {/* 服务描述 */}
                <p className="text-slate-600 text-sm mb-4 line-clamp-2 leading-relaxed">
                  {product.description}
                </p>
                
                {/* 底部信息 */}
                <div className="flex items-center justify-between">
                  {/* 收藏数量 */}
                  <div className="flex items-center gap-1 text-slate-500">
                    <Heart className="h-3 w-3" />
                    <span className="text-xs">{product.favoriteCount}</span>
                  </div>
                  
                  {/* 价格 */}
                  <div className="flex items-center gap-1">
                    <span className="text-slate-900">
                      RMB {formatPrice(product.price)}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 空状态 */}
      {filteredProducts.length === 0 && (
        <div className="text-center py-16">
          <div className="text-slate-400 mb-4">
            <Search className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-slate-900 mb-2">暂无相关服务</h3>
          <p className="text-slate-600">请尝试调整筛选条件或浏览其他分类</p>
        </div>
      )}
    </div>
  );
}
