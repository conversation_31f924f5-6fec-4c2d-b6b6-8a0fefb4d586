import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import mysql from 'mysql2/promise';
import { z } from 'zod';

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '123456',
  database: process.env.DB_NAME || 'ubund_db',
  port: parseInt(process.env.DB_PORT || '3306'),
};

// JWT密钥
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

// 登录请求验证schema
const loginSchema = z.object({
  username: z.string()
    .min(1, '请输入用户名'),
  password: z.string()
    .min(1, '请输入密码'),
});

// 统一响应格式
function createResponse(success: boolean, data?: any, message?: string, code?: string, status: number = 200) {
  return NextResponse.json({
    success,
    data,
    message,
    code,
  }, { status });
}

// 错误响应
function createErrorResponse(message: string, code?: string, status: number = 400, errors?: Record<string, string[]>) {
  return NextResponse.json({
    success: false,
    message,
    code,
    errors,
  }, { status });
}

export async function POST(request: NextRequest) {
  let connection: mysql.Connection | null = null;

  try {
    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = loginSchema.safeParse(body);
    if (!validationResult.success) {
      const errors: Record<string, string[]> = {};
      validationResult.error.errors.forEach((error) => {
        const field = error.path[0] as string;
        if (!errors[field]) {
          errors[field] = [];
        }
        errors[field].push(error.message);
      });

      return createErrorResponse(
        '请填写完整的登录信息',
        'VALIDATION_ERROR',
        422,
        errors
      );
    }

    const { username, password } = validationResult.data;

    // 连接数据库
    connection = await mysql.createConnection(dbConfig);

    // 查找用户
    const [userRows] = await connection.execute(
      'SELECT id, username, password, created_at FROM users WHERE username = ?',
      [username]
    );

    const user = Array.isArray(userRows) && userRows.length > 0 ? userRows[0] as any : null;

    if (!user) {
      return createErrorResponse(
        '用户名或密码错误',
        'INVALID_CREDENTIALS',
        401
      );
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid) {
      return createErrorResponse(
        '用户名或密码错误',
        'INVALID_CREDENTIALS',
        401
      );
    }

    // 生成JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        username: user.username,
        type: 'access'
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    // 生成refresh token
    const refreshToken = jwt.sign(
      { 
        userId: user.id, 
        username: user.username,
        type: 'refresh'
      },
      JWT_SECRET,
      { expiresIn: '30d' }
    );

    // 更新用户最后登录时间
    await connection.execute(
      'UPDATE users SET last_login_at = NOW(), updated_at = NOW() WHERE id = ?',
      [user.id]
    );

    // 返回成功响应
    return createResponse(true, {
      user: {
        id: user.id.toString(),
        username: user.username,
        createdAt: user.created_at,
      },
      token,
      refreshToken,
      expiresIn: 7 * 24 * 60 * 60, // 7天，单位：秒
    }, '登录成功');

  } catch (error) {
    console.error('Login error:', error);

    // 数据库连接错误
    if (error instanceof Error && error.message.includes('ECONNREFUSED')) {
      return createErrorResponse(
        '数据库连接失败，请稍后重试',
        'DATABASE_CONNECTION_ERROR',
        503
      );
    }

    // JWT错误
    if (error instanceof Error && error.message.includes('jwt')) {
      return createErrorResponse(
        '认证服务异常，请稍后重试',
        'JWT_ERROR',
        500
      );
    }

    // 其他错误
    return createErrorResponse(
      '登录失败，请稍后重试',
      'INTERNAL_SERVER_ERROR',
      500
    );
  } finally {
    // 关闭数据库连接
    if (connection) {
      await connection.end();
    }
  }
}

// 处理不支持的HTTP方法
export async function GET() {
  return createErrorResponse(
    '不支持的请求方法',
    'METHOD_NOT_ALLOWED',
    405
  );
}

export async function PUT() {
  return createErrorResponse(
    '不支持的请求方法',
    'METHOD_NOT_ALLOWED',
    405
  );
}

export async function DELETE() {
  return createErrorResponse(
    '不支持的请求方法',
    'METHOD_NOT_ALLOWED',
    405
  );
}
