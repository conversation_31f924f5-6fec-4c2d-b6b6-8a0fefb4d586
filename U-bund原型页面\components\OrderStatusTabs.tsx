import { cn } from "./ui/utils";
import { Package, Clock, Truck, CheckCircle, XCircle, CreditCard, Headphones } from "lucide-react";

interface OrderStatusTabsProps {
  activeStatus: string;
  onStatusChange: (status: string) => void;
  statusCounts: {
    total: number;
    pending: number;
    processing: number;
    shipped: number;
    delivered: number;
    cancelled: number;
    aftersale: number;
  };
}

export function OrderStatusTabs({ activeStatus, onStatusChange, statusCounts }: OrderStatusTabsProps) {
  const tabs = [
    {
      id: "all",
      label: "全部订单",
      count: statusCounts.total,
      icon: Package,
      activeColor: "text-blue-600",
      activeBadgeColor: "bg-blue-50 text-blue-700 border-blue-200"
    },
    {
      id: "pending",
      label: "待付款",
      count: statusCounts.pending,
      icon: CreditCard,
      activeColor: "text-amber-600",
      activeBadgeColor: "bg-amber-50 text-amber-700 border-amber-200"
    },
    {
      id: "processing",
      label: "处理中",
      count: statusCounts.processing,
      icon: Clock,
      activeColor: "text-blue-600",
      activeBadgeColor: "bg-blue-50 text-blue-700 border-blue-200"
    },
    {
      id: "shipped",
      label: "已发货",
      count: statusCounts.shipped,
      icon: Truck,
      activeColor: "text-purple-600",
      activeBadgeColor: "bg-purple-50 text-purple-700 border-purple-200"
    },
    {
      id: "delivered",
      label: "已完成",
      count: statusCounts.delivered,
      icon: CheckCircle,
      activeColor: "text-green-600",
      activeBadgeColor: "bg-green-50 text-green-700 border-green-200"
    },
    {
      id: "cancelled",
      label: "已取消",
      count: statusCounts.cancelled,
      icon: XCircle,
      activeColor: "text-red-600",
      activeBadgeColor: "bg-red-50 text-red-700 border-red-200"
    },
    {
      id: "aftersale",
      label: "售后中",
      count: statusCounts.aftersale,
      icon: Headphones,
      activeColor: "text-orange-600",
      activeBadgeColor: "bg-orange-50 text-orange-700 border-orange-200"
    }
  ];

  return (
    <div className="relative">
      {/* 背景容器 */}
      <div className="bg-slate-50/50 backdrop-blur-sm rounded-2xl p-2 border border-slate-200/60">
        <div className="flex flex-wrap gap-1">
          {tabs.map((tab) => {
            const isActive = activeStatus === tab.id;
            const Icon = tab.icon;
            
            return (
              <button
                key={tab.id}
                onClick={() => onStatusChange(tab.id)}
                className={cn(
                  "relative group flex items-center gap-2 px-4 py-3 rounded-xl transition-all duration-200 ease-out min-w-0 flex-1 md:flex-none",
                  "hover:bg-white/80 hover:shadow-sm hover:scale-[1.02] active:scale-[0.98]",
                  isActive 
                    ? "bg-white shadow-md shadow-black/5 ring-1 ring-black/5" 
                    : "hover:bg-white/60"
                )}
              >
                {/* 活跃状态指示器 */}
                {isActive && (
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5 rounded-xl" />
                )}
                
                {/* 图标 */}
                <Icon 
                  className={cn(
                    "h-4 w-4 transition-colors duration-200 flex-shrink-0",
                    isActive ? tab.activeColor : "text-slate-500",
                    "group-hover:scale-110"
                  )} 
                />
                
                {/* 标签文本 */}
                <span className={cn(
                  "font-medium transition-colors duration-200 whitespace-nowrap text-sm",
                  isActive ? "text-slate-900" : "text-slate-600",
                  "group-hover:text-slate-900"
                )}>
                  {tab.label}
                </span>
                
                {/* 数字角标 */}
                {tab.count > 0 && (
                  <div className={cn(
                    "inline-flex items-center justify-center px-2 py-0.5 rounded-full text-xs font-medium min-w-[20px] h-5 transition-all duration-200 flex-shrink-0",
                    "border border-transparent",
                    isActive 
                      ? tab.activeBadgeColor + " shadow-sm" 
                      : "bg-slate-100/80 text-slate-600 group-hover:bg-slate-200/80"
                  )}>
                    {tab.count}
                  </div>
                )}
                
                {/* 悬停效果 */}
                <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
              </button>
            );
          })}
        </div>
      </div>
      
      {/* 底部装饰线 */}
      <div className="absolute -bottom-px left-4 right-4 h-px bg-gradient-to-r from-transparent via-slate-200 to-transparent" />
    </div>
  );
}