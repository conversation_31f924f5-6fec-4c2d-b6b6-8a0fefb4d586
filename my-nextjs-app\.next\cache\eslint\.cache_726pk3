[{"E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\components\\App.tsx": "1", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\favorites\\page.tsx": "2", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\gallery\\page.tsx": "3", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\layout.tsx": "4", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\login\\page.tsx": "5", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\orders\\page.tsx": "6", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\page.tsx": "7", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\profile\\page.tsx": "8", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\services\\page.tsx": "9", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\support\\page.tsx": "10", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\CategoryNavigation.tsx": "11", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ChatFloatingButton.tsx": "12", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ChatWindow.tsx": "13", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\FavoritesPage.tsx": "14", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\figma\\ImageWithFallback.tsx": "15", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\LandingPage.tsx": "16", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\NavigationV78.tsx": "17", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\NavigationV80.tsx": "18", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\OrderCard.tsx": "19", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\OrderFilters.tsx": "20", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\OrderStatusTabs.tsx": "21", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\PlaceholderPage.tsx": "22", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ServicesPage.tsx": "23", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ui\\avatar.tsx": "24", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ui\\badge.tsx": "25", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ui\\breadcrumb.tsx": "26", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ui\\button.tsx": "27", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ui\\card.tsx": "28", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ui\\dropdown-menu.tsx": "29", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ui\\input.tsx": "30", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ui\\select.tsx": "31", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\WorkProgressBar.tsx": "32", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\data\\mockFavorites.ts": "33", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\data\\mockOrders.ts": "34", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\lib\\utils.ts": "35", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\types\\favorites.ts": "36", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\types\\order.ts": "37", "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\utils\\orderUtils.ts": "38"}, {"size": 10546, "mtime": 1753953645872, "results": "39", "hashOfConfig": "40"}, {"size": 1999, "mtime": 1753944417764, "results": "41", "hashOfConfig": "40"}, {"size": 2901, "mtime": 1753945029682, "results": "42", "hashOfConfig": "40"}, {"size": 689, "mtime": 1753866755324, "results": "43", "hashOfConfig": "40"}, {"size": 2263, "mtime": 1753868506410, "results": "44", "hashOfConfig": "40"}, {"size": 5325, "mtime": 1753868452105, "results": "45", "hashOfConfig": "40"}, {"size": 3162, "mtime": 1753944655053, "results": "46", "hashOfConfig": "40"}, {"size": 2319, "mtime": 1753945177003, "results": "47", "hashOfConfig": "40"}, {"size": 3161, "mtime": 1753945149514, "results": "48", "hashOfConfig": "40"}, {"size": 2901, "mtime": 1753944995665, "results": "49", "hashOfConfig": "40"}, {"size": 6607, "mtime": 1753867545727, "results": "50", "hashOfConfig": "40"}, {"size": 1282, "mtime": 1753868207428, "results": "51", "hashOfConfig": "40"}, {"size": 9608, "mtime": 1753868249685, "results": "52", "hashOfConfig": "40"}, {"size": 10459, "mtime": 1753953737844, "results": "53", "hashOfConfig": "40"}, {"size": 1153, "mtime": 1753944944124, "results": "54", "hashOfConfig": "40"}, {"size": 13938, "mtime": 1753868800641, "results": "55", "hashOfConfig": "40"}, {"size": 11877, "mtime": 1753953704313, "results": "56", "hashOfConfig": "40"}, {"size": 3905, "mtime": 1753867483462, "results": "57", "hashOfConfig": "40"}, {"size": 9239, "mtime": 1753868019905, "results": "58", "hashOfConfig": "40"}, {"size": 3495, "mtime": 1753868049290, "results": "59", "hashOfConfig": "40"}, {"size": 1915, "mtime": 1753868031443, "results": "60", "hashOfConfig": "40"}, {"size": 856, "mtime": 1753944969158, "results": "61", "hashOfConfig": "40"}, {"size": 24583, "mtime": 1753953821946, "results": "62", "hashOfConfig": "40"}, {"size": 1097, "mtime": 1753867456680, "results": "63", "hashOfConfig": "40"}, {"size": 1631, "mtime": 1753867456673, "results": "64", "hashOfConfig": "40"}, {"size": 2729, "mtime": 1753946104406, "results": "65", "hashOfConfig": "40"}, {"size": 2123, "mtime": 1753867207102, "results": "66", "hashOfConfig": "40"}, {"size": 1989, "mtime": 1753867456660, "results": "67", "hashOfConfig": "40"}, {"size": 7309, "mtime": 1753946152836, "results": "68", "hashOfConfig": "40"}, {"size": 967, "mtime": 1753867456641, "results": "69", "hashOfConfig": "40"}, {"size": 6253, "mtime": 1753868062817, "results": "70", "hashOfConfig": "40"}, {"size": 6089, "mtime": 1753945101326, "results": "71", "hashOfConfig": "40"}, {"size": 7589, "mtime": 1753944517221, "results": "72", "hashOfConfig": "40"}, {"size": 3539, "mtime": 1753867183766, "results": "73", "hashOfConfig": "40"}, {"size": 166, "mtime": 1753866839922, "results": "74", "hashOfConfig": "40"}, {"size": 347, "mtime": 1753867147201, "results": "75", "hashOfConfig": "40"}, {"size": 1248, "mtime": 1753867141285, "results": "76", "hashOfConfig": "40"}, {"size": 2205, "mtime": 1753867158297, "results": "77", "hashOfConfig": "40"}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "1j7a2et", {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 20, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\components\\App.tsx", ["192", "193", "194", "195", "196", "197"], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\favorites\\page.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\gallery\\page.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\layout.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\login\\page.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\orders\\page.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\page.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\profile\\page.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\services\\page.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\support\\page.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\CategoryNavigation.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ChatFloatingButton.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ChatWindow.tsx", ["198"], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\FavoritesPage.tsx", ["199", "200"], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\figma\\ImageWithFallback.tsx", ["201", "202"], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\LandingPage.tsx", ["203", "204", "205", "206", "207"], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\NavigationV78.tsx", ["208", "209", "210", "211"], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\NavigationV80.tsx", ["212", "213"], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\OrderCard.tsx", ["214", "215", "216"], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\OrderFilters.tsx", ["217"], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\OrderStatusTabs.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\PlaceholderPage.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ServicesPage.tsx", ["218", "219", "220", "221", "222", "223", "224", "225", "226", "227", "228", "229", "230", "231", "232", "233", "234", "235", "236", "237"], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ui\\avatar.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ui\\badge.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ui\\breadcrumb.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ui\\button.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ui\\card.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ui\\dropdown-menu.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ui\\input.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\ui\\select.tsx", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\components\\WorkProgressBar.tsx", ["238", "239"], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\data\\mockFavorites.ts", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\data\\mockOrders.ts", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\lib\\utils.ts", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\types\\favorites.ts", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\types\\order.ts", [], [], "E:\\7mouthMission\\1500\\my-nextjs-app\\src\\utils\\orderUtils.ts", [], [], {"ruleId": "240", "severity": 1, "message": "241", "line": 4, "column": 10, "nodeType": null, "messageId": "242", "endLine": 4, "endColumn": 16}, {"ruleId": "240", "severity": 1, "message": "243", "line": 20, "column": 7, "nodeType": null, "messageId": "242", "endLine": 20, "endColumn": 19}, {"ruleId": "240", "severity": 1, "message": "244", "line": 52, "column": 10, "nodeType": null, "messageId": "242", "endLine": 52, "endColumn": 25}, {"ruleId": "240", "severity": 1, "message": "245", "line": 53, "column": 10, "nodeType": null, "messageId": "242", "endLine": 53, "endColumn": 27}, {"ruleId": "246", "severity": 2, "message": "247", "line": 60, "column": 28, "nodeType": "248", "messageId": "249", "endLine": 60, "endColumn": 31, "suggestions": "250"}, {"ruleId": "246", "severity": 2, "message": "247", "line": 134, "column": 81, "nodeType": "248", "messageId": "249", "endLine": 134, "endColumn": 84, "suggestions": "251"}, {"ruleId": "240", "severity": 1, "message": "252", "line": 10, "column": 3, "nodeType": null, "messageId": "242", "endLine": 10, "endColumn": 16}, {"ruleId": "240", "severity": 1, "message": "253", "line": 2, "column": 25, "nodeType": null, "messageId": "242", "endLine": 2, "endColumn": 31}, {"ruleId": "254", "severity": 1, "message": "255", "line": 74, "column": 11, "nodeType": "256", "endLine": 78, "endColumn": 13}, {"ruleId": "254", "severity": 1, "message": "255", "line": 21, "column": 9, "nodeType": "256", "endLine": 21, "endColumn": 96}, {"ruleId": "254", "severity": 1, "message": "255", "line": 25, "column": 5, "nodeType": "256", "endLine": 25, "endColumn": 100}, {"ruleId": "240", "severity": 1, "message": "257", "line": 8, "column": 3, "nodeType": null, "messageId": "242", "endLine": 8, "endColumn": 7}, {"ruleId": "240", "severity": 1, "message": "258", "line": 67, "column": 9, "nodeType": null, "messageId": "242", "endLine": 67, "endColumn": 19}, {"ruleId": "240", "severity": 1, "message": "259", "line": 130, "column": 9, "nodeType": null, "messageId": "242", "endLine": 130, "endColumn": 28}, {"ruleId": "240", "severity": 1, "message": "260", "line": 130, "column": 32, "nodeType": null, "messageId": "242", "endLine": 130, "endColumn": 42}, {"ruleId": "254", "severity": 1, "message": "255", "line": 282, "column": 21, "nodeType": "256", "endLine": 286, "endColumn": 23}, {"ruleId": "240", "severity": 1, "message": "261", "line": 3, "column": 10, "nodeType": null, "messageId": "242", "endLine": 3, "endColumn": 16}, {"ruleId": "240", "severity": 1, "message": "262", "line": 3, "column": 18, "nodeType": null, "messageId": "242", "endLine": 3, "endColumn": 32}, {"ruleId": "240", "severity": 1, "message": "263", "line": 3, "column": 34, "nodeType": null, "messageId": "242", "endLine": 3, "endColumn": 45}, {"ruleId": "240", "severity": 1, "message": "252", "line": 17, "column": 3, "nodeType": null, "messageId": "242", "endLine": 17, "endColumn": 16}, {"ruleId": "240", "severity": 1, "message": "264", "line": 1, "column": 10, "nodeType": null, "messageId": "242", "endLine": 1, "endColumn": 18}, {"ruleId": "240", "severity": 1, "message": "265", "line": 11, "column": 45, "nodeType": null, "messageId": "242", "endLine": 11, "endColumn": 61}, {"ruleId": "240", "severity": 1, "message": "266", "line": 12, "column": 3, "nodeType": null, "messageId": "242", "endLine": 12, "endColumn": 7}, {"ruleId": "240", "severity": 1, "message": "267", "line": 147, "column": 35, "nodeType": null, "messageId": "242", "endLine": 147, "endColumn": 40}, {"ruleId": "254", "severity": 1, "message": "255", "line": 150, "column": 17, "nodeType": "256", "endLine": 154, "endColumn": 19}, {"ruleId": "240", "severity": 1, "message": "253", "line": 10, "column": 18, "nodeType": null, "messageId": "242", "endLine": 10, "endColumn": 24}, {"ruleId": "240", "severity": 1, "message": "261", "line": 5, "column": 10, "nodeType": null, "messageId": "242", "endLine": 5, "endColumn": 16}, {"ruleId": "240", "severity": 1, "message": "262", "line": 5, "column": 18, "nodeType": null, "messageId": "242", "endLine": 5, "endColumn": 32}, {"ruleId": "240", "severity": 1, "message": "263", "line": 5, "column": 34, "nodeType": null, "messageId": "242", "endLine": 5, "endColumn": 45}, {"ruleId": "240", "severity": 1, "message": "268", "line": 15, "column": 3, "nodeType": null, "messageId": "242", "endLine": 15, "endColumn": 15}, {"ruleId": "240", "severity": 1, "message": "269", "line": 16, "column": 3, "nodeType": null, "messageId": "242", "endLine": 16, "endColumn": 22}, {"ruleId": "240", "severity": 1, "message": "270", "line": 17, "column": 3, "nodeType": null, "messageId": "242", "endLine": 17, "endColumn": 19}, {"ruleId": "240", "severity": 1, "message": "271", "line": 18, "column": 3, "nodeType": null, "messageId": "242", "endLine": 18, "endColumn": 20}, {"ruleId": "240", "severity": 1, "message": "272", "line": 19, "column": 3, "nodeType": null, "messageId": "242", "endLine": 19, "endColumn": 24}, {"ruleId": "240", "severity": 1, "message": "273", "line": 20, "column": 3, "nodeType": null, "messageId": "242", "endLine": 20, "endColumn": 22}, {"ruleId": "240", "severity": 1, "message": "274", "line": 21, "column": 3, "nodeType": null, "messageId": "242", "endLine": 21, "endColumn": 27}, {"ruleId": "240", "severity": 1, "message": "253", "line": 25, "column": 3, "nodeType": null, "messageId": "242", "endLine": 25, "endColumn": 9}, {"ruleId": "240", "severity": 1, "message": "275", "line": 30, "column": 3, "nodeType": null, "messageId": "242", "endLine": 30, "endColumn": 15}, {"ruleId": "240", "severity": 1, "message": "276", "line": 36, "column": 3, "nodeType": null, "messageId": "242", "endLine": 36, "endColumn": 20}, {"ruleId": "240", "severity": 1, "message": "277", "line": 39, "column": 3, "nodeType": null, "messageId": "242", "endLine": 39, "endColumn": 4}, {"ruleId": "240", "severity": 1, "message": "278", "line": 41, "column": 3, "nodeType": null, "messageId": "242", "endLine": 41, "endColumn": 9}, {"ruleId": "240", "severity": 1, "message": "279", "line": 172, "column": 3, "nodeType": null, "messageId": "242", "endLine": 172, "endColumn": 22}, {"ruleId": "240", "severity": 1, "message": "280", "line": 180, "column": 10, "nodeType": null, "messageId": "242", "endLine": 180, "endColumn": 17}, {"ruleId": "240", "severity": 1, "message": "281", "line": 180, "column": 19, "nodeType": null, "messageId": "242", "endLine": 180, "endColumn": 29}, {"ruleId": "254", "severity": 1, "message": "255", "line": 558, "column": 17, "nodeType": "256", "endLine": 562, "endColumn": 19}, {"ruleId": "254", "severity": 1, "message": "255", "line": 591, "column": 23, "nodeType": "256", "endLine": 595, "endColumn": 25}, {"ruleId": "240", "severity": 1, "message": "267", "line": 66, "column": 29, "nodeType": null, "messageId": "242", "endLine": 66, "endColumn": 34}, {"ruleId": "240", "severity": 1, "message": "282", "line": 69, "column": 19, "nodeType": null, "messageId": "242", "endLine": 69, "endColumn": 28}, "@typescript-eslint/no-unused-vars", "'Button' is defined but never used.", "unusedVar", "'mockProducts' is assigned a value but never used.", "'selectedOrderId' is assigned a value but never used.", "'selectedProductId' is assigned a value but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["283", "284"], ["285", "286"], "'MessageCircle' is defined but never used.", "'Filter' is defined but never used.", "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "'Play' is defined but never used.", "'categories' is assigned a value but never used.", "'handleCategoryClick' is assigned a value but never used.", "'categoryId' is defined but never used.", "'Avatar' is defined but never used.", "'AvatarFallback' is defined but never used.", "'AvatarImage' is defined but never used.", "'useState' is defined but never used.", "'onCategoryChange' is defined but never used.", "'User' is defined but never used.", "'index' is defined but never used.", "'DropdownMenu' is defined but never used.", "'DropdownMenuContent' is defined but never used.", "'DropdownMenuItem' is defined but never used.", "'DropdownMenuLabel' is defined but never used.", "'DropdownMenuSeparator' is defined but never used.", "'DropdownMenuTrigger' is defined but never used.", "'DropdownMenuCheckboxItem' is defined but never used.", "'ShoppingCart' is defined but never used.", "'SlidersHorizontal' is defined but never used.", "'X' is defined but never used.", "'MapPin' is defined but never used.", "'onViewProductDetail' is defined but never used.", "'filters' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'isPending' is assigned a value but never used.", {"messageId": "287", "fix": "288", "desc": "289"}, {"messageId": "290", "fix": "291", "desc": "292"}, {"messageId": "287", "fix": "293", "desc": "289"}, {"messageId": "290", "fix": "294", "desc": "292"}, "suggestUnknown", {"range": "295", "text": "296"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "297", "text": "298"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "299", "text": "296"}, {"range": "300", "text": "298"}, [2484, 2487], "unknown", [2484, 2487], "never", [4432, 4435], [4432, 4435]]