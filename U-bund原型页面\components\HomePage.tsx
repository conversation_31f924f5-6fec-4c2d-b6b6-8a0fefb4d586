// This file has been replaced by ServicesPage.tsx
// Please use ServicesPage component instead of HomePage
export { ServicesPage as HomePage } from "./ServicesPage";

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  image: string;
  rating: number;
  reviews: number;
  category: string;
  tags: string[];
  discount?: number;
  isBestseller?: boolean;
  isNew?: boolean;
  creator: {
    name: string;
    avatar: string;
    verified?: boolean;
  };
  favoriteCount: number;
  specialBadge?: "upro" | "top" | "star";
}

const mockProducts: Product[] = [
  {
    id: "1",
    name: "品牌视觉设计",
    description: "专业品牌LOGO设计，包含VI系统设计，多种风格可选，原创保证，商用授权",
    price: 999,
    originalPrice: 1299,
    image: "https://images.unsplash.com/photo-1558655146-d09347e92766?w=400&h=400&fit=crop",
    rating: 5.0,
    reviews: 1234,
    category: "设计",
    tags: ["热门", "原创"],
    discount: 23,
    isBestseller: true,
    isNew: false,
    creator: {
      name: "设计师小王",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",
      verified: true
    },
    favoriteCount: 892,
    specialBadge: "upro"
  },
  {
    id: "2",
    name: "网站开发定制",
    description: "响应式网站开发，React+Node.js技术栈，包含前后端开发和部署服务",
    price: 4999,
    image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=400&fit=crop",
    rating: 4.9,
    reviews: 856,
    category: "技术",
    tags: ["专业", "全栈"],
    isBestseller: true,
    creator: {
      name: "程序员老李",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",
      verified: true
    },
    favoriteCount: 634,
    specialBadge: "top"
  },
  {
    id: "3",
    name: "短视频制作",
    description: "抖音快手短视频策划制作，包含脚本编写、拍摄剪辑、后期特效处理",
    price: 1999,
    image: "https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=400&h=400&fit=crop",
    rating: 4.8,
    reviews: 2341,
    category: "视频",
    tags: ["创意", "营销"],
    isNew: true,
    creator: {
      name: "视频达人小张",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616c45e1f94?w=100&h=100&fit=crop&crop=faces",
      verified: false
    },
    favoriteCount: 1523,
    specialBadge: "star"
  },
  {
    id: "4",
    name: "文案策划服务",
    description: "品牌文案策划，广告语创作，社交媒体内容策划，营销文案撰写服务",
    price: 699,
    originalPrice: 899,
    image: "https://images.unsplash.com/photo-1455390582262-044cdead277a?w=400&h=400&fit=crop",
    rating: 4.7,
    reviews: 567,
    category: "写作",
    tags: ["营销", "创意"],
    discount: 22,
    creator: {
      name: "文案师小刘",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=faces",
      verified: true
    },
    favoriteCount: 789
  },
  {
    id: "5",
    name: "音频后期制作",
    description: "专业音频录制、混音、母带处理，播客制作，音效设计，配音服务",
    price: 899,
    image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=400&fit=crop",
    rating: 4.9,
    reviews: 423,
    category: "音频",
    tags: ["专业", "原创"],
    creator: {
      name: "音频师小陈",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=faces",
      verified: true
    },
    favoriteCount: 456
  },
  {
    id: "6",
    name: "电商运营策划",
    description: "淘宝天猫店铺运营，直通车优化，数据分析，产品推广策略制定",
    price: 2999,
    image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=400&fit=crop",
    rating: 4.6,
    reviews: 234,
    category: "营销",
    tags: ["电商", "数据"],
    isBestseller: true,
    creator: {
      name: "运营专家老王",
      avatar: "https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=100&h=100&fit=crop&crop=faces",
      verified: true
    },
    favoriteCount: 312,
    specialBadge: "upro"
  },
  {
    id: "7",
    name: "UI/UX设计",
    description: "移动应用界面设计，用户体验优化，原型设计，交互设计全流程服务",
    price: 3999,
    image: "https://images.unsplash.com/photo-1586717799252-bd134ad00e26?w=400&h=400&fit=crop",
    rating: 5.0,
    reviews: 678,
    category: "设计",
    tags: ["UI", "移动端"],
    isNew: true,
    creator: {
      name: "UI设计师小美",
      avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop&crop=faces",
      verified: true
    },
    favoriteCount: 923,
    specialBadge: "top"
  },
  {
    id: "8",
    name: "3D建模渲染",
    description: "产品3D建模，场景渲染，动画制作，适用于产品展示和游戏开发",
    price: 1599,
    originalPrice: 1999,
    image: "https://images.unsplash.com/photo-1558618047-5c1c7d5a6e36?w=400&h=400&fit=crop",
    rating: 4.8,
    reviews: 345,
    category: "3D",
    tags: ["建模", "渲染"],
    discount: 20,
    creator: {
      name: "3D艺术家小林",
      avatar: "https://images.unsplash.com/photo-1521119989659-a83eee488004?w=100&h=100&fit=crop&crop=faces",
      verified: false
    },
    favoriteCount: 278
  },
  // 新增11个商品卡片
  {
    id: "9",
    name: "小程序开发",
    description: "微信小程序定制开发，包含需求分析、界面设计、功能开发、测试上线",
    price: 2599,
    originalPrice: 3299,
    image: "https://images.unsplash.com/photo-1512428559087-560fa5ceab42?w=400&h=400&fit=crop",
    rating: 4.7,
    reviews: 423,
    category: "技术",
    tags: ["小程序", "移动端"],
    discount: 21,
    isNew: true,
    creator: {
      name: "小程序专家小赵",
      avatar: "https://images.unsplash.com/photo-1519648023493-d82b5f8d7b8a?w=100&h=100&fit=crop&crop=faces",
      verified: true
    },
    favoriteCount: 567,
    specialBadge: "upro"
  },
  {
    id: "10",
    name: "SEO优化服务",
    description: "网站SEO优化，关键词研究，内容优化，外链建设，排名提升保证",
    price: 1299,
    image: "https://images.unsplash.com/photo-1432888622747-4eb9a8efeb07?w=400&h=400&fit=crop",
    rating: 4.6,
    reviews: 789,
    category: "营销",
    tags: ["SEO", "流量"],
    isBestseller: true,
    creator: {
      name: "SEO专家小周",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",
      verified: true
    },
    favoriteCount: 445,
    specialBadge: "top"
  },
  {
    id: "11",
    name: "商业插画设计",
    description: "手绘商业插画，IP形象设计，包装插画，儿童绘本插画，风格多样",
    price: 599,
    originalPrice: 799,
    image: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=400&fit=crop",
    rating: 4.9,
    reviews: 334,
    category: "设计",
    tags: ["插画", "原创"],
    discount: 25,
    creator: {
      name: "插画师小汤",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=faces",
      verified: false
    },
    favoriteCount: 689,
    specialBadge: "star"
  },
  {
    id: "12",
    name: "播客制作服务",
    description: "专业播客录制、剪辑、后期制作，音频优化，多平台发布指导",
    price: 799,
    image: "https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=400&h=400&fit=crop",
    rating: 4.8,
    reviews: 156,
    category: "音频",
    tags: ["播客", "媒体"],
    isNew: true,
    creator: {
      name: "播客制作人小孙",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=faces",
      verified: true
    },
    favoriteCount: 234
  },
  {
    id: "13",
    name: "财务报表分析",
    description: "企业财务报表深度分析，财务健康诊断，投资决策建议，风险评估",
    price: 1999,
    image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=400&fit=crop",
    rating: 4.7,
    reviews: 201,
    category: "财务",
    tags: ["专业", "数据"],
    creator: {
      name: "财务分析师小吴",
      avatar: "https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=100&h=100&fit=crop&crop=faces",
      verified: true
    },
    favoriteCount: 178,
    specialBadge: "upro"
  },
  {
    id: "14",
    name: "法律合同审查",
    description: "商业合同审查，法律风险评估，条款优化建议，纠纷预防指导",
    price: 899,
    originalPrice: 1199,
    image: "https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=400&h=400&fit=crop",
    rating: 4.6,
    reviews: 89,
    category: "法律",
    tags: ["合规", "专业"],
    discount: 25,
    creator: {
      name: "法律顾问小郑",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",
      verified: true
    },
    favoriteCount: 123
  },
  {
    id: "15",
    name: "论文写作指导",
    description: "学术论文写作指导，文献综述，数据分析，格式规范，查重降重",
    price: 699,
    image: "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=400&h=400&fit=crop",
    rating: 4.8,
    reviews: 267,
    category: "学术",
    tags: ["教育", "写作"],
    isBestseller: true,
    creator: {
      name: "学术导师小钱",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=faces",
      verified: true
    },
    favoriteCount: 345,
    specialBadge: "top"
  },
  {
    id: "16",
    name: "产品摄影服务",
    description: "专业产品摄影，电商主图拍摄，静物摄影，商业摄影，后期精修",
    price: 599,
    originalPrice: 799,
    image: "https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=400&h=400&fit=crop",
    rating: 4.9,
    reviews: 456,
    category: "摄影",
    tags: ["电商", "商业"],
    discount: 25,
    isNew: true,
    creator: {
      name: "摄影师小徐",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",
      verified: true
    },
    favoriteCount: 567,
    specialBadge: "star"
  },
  {
    id: "17",
    name: "营销策略咨询",
    description: "品牌营销策略制定，市场调研分析，竞品分析，营销方案执行指导",
    price: 3999,
    image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=400&fit=crop",
    rating: 4.7,
    reviews: 134,
    category: "营销",
    tags: ["策略", "咨询"],
    creator: {
      name: "营销顾问小何",
      avatar: "https://images.unsplash.com/photo-1521119989659-a83eee488004?w=100&h=100&fit=crop&crop=faces",
      verified: true
    },
    favoriteCount: 234,
    specialBadge: "upro"
  },
  {
    id: "18",
    name: "数据可视化",
    description: "专业数据可视化设计，图表制作，仪表板开发，商业智能分析展示",
    price: 1599,
    image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=400&fit=crop",
    rating: 4.8,
    reviews: 178,
    category: "技术",
    tags: ["数据", "可视化"],
    isNew: true,
    creator: {
      name: "数据工程师小宋",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=faces",
      verified: true
    },
    favoriteCount: 289
  },
  {
    id: "19",
    name: "翻译服务",
    description: "专业中英翻译，商务文档翻译，技术资料翻译，本地化服务",
    price: 299,
    originalPrice: 399,
    image: "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=400&fit=crop",
    rating: 4.6,
    reviews: 567,
    category: "写作",
    tags: ["翻译", "语言"],
    discount: 25,
    isBestseller: true,
    creator: {
      name: "翻译专家小冯",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616c45e1f94?w=100&h=100&fit=crop&crop=faces",
      verified: true
    },
    favoriteCount: 445,
    specialBadge: "top"
  }
];

const filterTags = [
  { id: "bestseller", label: "热销榜", icon: TrendingUp },
  { id: "new", label: "新品", icon: Sparkles },
  { id: "discount", label: "特惠", icon: Zap },
  { id: "professional", label: "专业级", icon: Award }
];

interface HomePageProps {
  activeCategory?: string;
  activeSubCategory?: string | null;
  onCategoryChange?: (category: string) => void;
  onSubCategoryChange?: (subCategory: string | null) => void;
}

export function HomePage({ activeCategory = "popular", activeSubCategory, onCategoryChange, onSubCategoryChange }: HomePageProps) {
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState("default");
  const [favorites, setFavorites] = useState<string[]>([]);
  
  // 筛选状态
  const [filters, setFilters] = useState({
    timeRange: "all",
    priceRange: "all", 
    rating: "all",
    serviceType: "all"
  });

  // 快速筛选状态
  const [quickFilters, setQuickFilters] = useState({
    uproServices: false,
    onlineOnly: false
  });

  // 筛选产品 - 适配新的服务分类系统
  const filteredProducts = mockProducts.filter(product => {
    let matchesCategory = true;
    
    // 根据新的服务分类系统筛选
    switch (activeCategory) {
      case "popular":
        matchesCategory = product.isBestseller || product.isNew;
        break;
      case "design":
        matchesCategory = product.category === "设计" || product.category === "3D";
        break;
      case "programming":
        matchesCategory = product.category === "技术";
        break;
      case "marketing":
        matchesCategory = product.category === "营销";
        break;
      case "video":
        matchesCategory = product.category === "视频";
        break;
      case "writing":
        matchesCategory = product.category === "写作";
        break;
      case "music":
        matchesCategory = product.category === "音频";
        break;
      case "business":
        matchesCategory = product.tags.includes("专业") || product.tags.includes("数据");
        break;
      case "finance":
        matchesCategory = product.price > 3000;
        break;
      case "legal":
        matchesCategory = product.tags.includes("合规");
        break;
      case "academic":
        matchesCategory = product.tags.includes("教��");
        break;
      default:
        matchesCategory = true;
    }
    
    let matchesTags = true;
    if (selectedTags.length > 0) {
      matchesTags = selectedTags.every(tag => {
        switch (tag) {
          case "bestseller":
            return product.isBestseller;
          case "new":
            return product.isNew;
          case "discount":
            return product.discount;
          case "professional":
            return product.tags.includes("专业");
          default:
            return true;
        }
      });
    }

    // 快速筛选逻辑
    let matchesQuickFilters = true;
    
    // Upro服务筛选
    if (quickFilters.uproServices) {
      matchesQuickFilters = matchesQuickFilters && product.specialBadge === "upro";
    }
    
    // 在线状态筛选 (基于创作者认证状态作为在线指标)
    if (quickFilters.onlineOnly) {
      matchesQuickFilters = matchesQuickFilters && product.creator.verified;
    }

    return matchesCategory && matchesTags && matchesQuickFilters;
  });

  // 排序产品
  const sortedProducts = [...filteredProducts].sort((a, b) => {
    switch (sortBy) {
      case "price-low":
        return a.price - b.price;
      case "price-high":
        return b.price - a.price;
      case "rating":
        return b.rating - a.rating;
      case "reviews":
        return b.reviews - a.reviews;
      default:
        return 0;
    }
  });

  const formatPrice = (price: number) => `${price}起`;

  const toggleTag = (tagId: string) => {
    setSelectedTags(prev => 
      prev.includes(tagId) 
        ? prev.filter(t => t !== tagId)
        : [...prev, tagId]
    );
  };

  const toggleFavorite = (productId: string) => {
    setFavorites(prev => 
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  // 获取活跃筛选数量
  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.timeRange !== "all") count++;
    if (filters.priceRange !== "all") count++;
    if (filters.rating !== "all") count++;
    if (filters.serviceType !== "all") count++;
    // 包含快速筛选的数量
    if (quickFilters.uproServices) count++;
    if (quickFilters.onlineOnly) count++;
    return count;
  };

  // 清除所有筛选
  const clearAllFilters = () => {
    setFilters({
      timeRange: "all",
      priceRange: "all", 
      rating: "all",
      serviceType: "all"
    });
  };

  // 切换快速筛选
  const toggleQuickFilter = (filterType: 'uproServices' | 'onlineOnly') => {
    setQuickFilters(prev => ({
      ...prev,
      [filterType]: !prev[filterType]
    }));
  };

  // 筛选选项数据
  const filterOptions = {
    timeRange: [
      { value: "all", label: "全部时间" },
      { value: "recent", label: "最近发布" },
      { value: "week", label: "本周" },
      { value: "month", label: "本月" }
    ],
    priceRange: [
      { value: "all", label: "全部价格" },
      { value: "0-100", label: "¥100以下" },
      { value: "100-500", label: "¥100-500" },
      { value: "500-1000", label: "¥500-1000" },
      { value: "1000+", label: "¥1000以上" }
    ],
    rating: [
      { value: "all", label: "全部评分" },
      { value: "5", label: "5星" },
      { value: "4+", label: "4星以上" },
      { value: "3+", label: "3星以上" }
    ],
    serviceType: [
      { value: "all", label: "全部类型" },
      { value: "verified", label: "认证服务" },
      { value: "professional", label: "专业服务" },
      { value: "bestseller", label: "热销服务" }
    ]
  };

  // 获取分类名称
  const getCategoryName = (category: string) => {
    switch (category) {
      case "popular": return "热门项目";
      case "design": return "图像与设计";
      case "programming": return "编程与技术";
      case "marketing": return "数字营销";
      case "video": return "视频动画摄影";
      case "writing": return "写作&翻译";
      case "music": return "音乐与音频";
      case "business": return "商业";
      case "finance": return "财务";
      case "legal": return "法律";
      case "academic": return "学业";
      default: return "服务";
    }
  };

  // 获取分类描述
  const getCategoryDescription = (category: string) => {
    switch (category) {
      case "popular": return "发现最受欢迎的服务项目";
      case "design": return "创意设计和视觉表达服务";
      case "programming": return "专业技术开发解决方案";
      case "marketing": return "数字化营销推广服务";
      case "video": return "专业影像制作服务";
      case "writing": return "文字创作和多语言服务";
      case "music": return "音频制作和音乐创作";
      case "business": return "商业咨询和管理服务";
      case "finance": return "财务分析和投资顾问";
      case "legal": return "法律咨询和文书服务";
      case "academic": return "学术辅导和教育服务";
      default: return "发现专业服务";
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-6">
      {/* 面包屑导航 */}
      <div className="mb-4">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink 
                href="#" 
                onClick={(e) => {
                  e.preventDefault();
                  onCategoryChange?.("popular");
                  onSubCategoryChange?.(null);
                }}
                className="flex items-center gap-1 text-slate-500 hover:text-slate-700 transition-colors cursor-pointer"
              >
                <Home className="h-4 w-4" />
                首页
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            {activeSubCategory ? (
              <>
                <BreadcrumbItem>
                  <BreadcrumbLink 
                    href="#" 
                    onClick={(e) => {
                      e.preventDefault();
                      onSubCategoryChange?.(null);
                    }}
                    className="text-slate-500 hover:text-slate-700 transition-colors cursor-pointer"
                  >
                    {getCategoryName(activeCategory)}
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage className="text-slate-900">
                    {activeSubCategory}
                  </BreadcrumbPage>
                </BreadcrumbItem>
              </>
            ) : (
              <BreadcrumbItem>
                <BreadcrumbPage className="text-slate-900">
                  {getCategoryName(activeCategory)}
                </BreadcrumbPage>
              </BreadcrumbItem>
            )}
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {/* 页面标题 */}
      <div className="mb-6 flex items-start justify-between">
        <div>
          <h1 className="text-slate-900 mb-1">
            {activeSubCategory || getCategoryName(activeCategory)}
          </h1>
          <p className="text-slate-600">
            {activeSubCategory 
              ? `${getCategoryName(activeCategory)} - ${activeSubCategory} 相关服务`
              : getCategoryDescription(activeCategory)
            }
          </p>
        </div>

        {/* 右侧咨询按钮 - 只在热门项目显示 */}
        {activeCategory === "popular" && (
          <div className="flex flex-col gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50/60 px-3 py-2 h-auto justify-start text-sm font-normal"
            >
              <User className="h-4 w-4 mr-2" />
              是否需要定制化选购？
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-purple-600 hover:text-purple-700 hover:bg-purple-50/60 px-3 py-2 h-auto justify-start text-sm font-normal"
            >
              <Bot className="h-4 w-4 mr-2" />
              可以问问AI我先需要准备哪些
            </Button>
          </div>
        )}
      </div>

      {/* 顶部筛选区域 */}
      <div className="bg-white/70 backdrop-blur-xl border border-slate-200/60 rounded-2xl p-6 mb-6">
        {/* 主要筛选区域 */}
        <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between mb-6">
          {/* 控制区域 */}
          <div className="flex items-center gap-3 w-full justify-end">
            {/* 排序方式 */}
            <div className="relative">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="appearance-none bg-slate-50/80 border border-slate-200/60 rounded-lg px-3 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/20"
              >
                <option value="default">排序方式</option>
                <option value="price-low">价格从低到高</option>
                <option value="price-high">价格从高到低</option>
                <option value="rating">评分最高</option>
                <option value="reviews">评价最多</option>
              </select>
              <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400 pointer-events-none" />
            </div>

            {/* 视图切换 */}
            <div className="flex items-center bg-slate-100/80 rounded-lg p-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewMode("grid")}
                className={`p-1 h-8 w-8 ${viewMode === "grid" ? 'bg-white shadow-sm' : ''}`}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewMode("list")}
                className={`p-1 h-8 w-8 ${viewMode === "list" ? 'bg-white shadow-sm' : ''}`}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* 特性筛选标签 */}
        <div className="flex flex-wrap gap-2">
          {filterTags.map(tag => {
            const Icon = tag.icon;
            const isSelected = selectedTags.includes(tag.id);
            return (
              <Button
                key={tag.id}
                variant="ghost"
                size="sm"
                onClick={() => toggleTag(tag.id)}
                className={`rounded-full px-3 py-2 flex items-center gap-2 transition-all duration-200 ${
                  isSelected
                    ? 'bg-gradient-to-r from-blue-50/80 to-indigo-50/80 text-blue-700 border-blue-200/60 shadow-sm'
                    : 'bg-slate-50/80 text-slate-600 hover:bg-slate-100/80 border-slate-200/60'
                } border`}
              >
                <Icon className="h-3 w-3" />
                {tag.label}
              </Button>
            );
          })}
        </div>
      </div>

      {/* 结果统计 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <span className="text-slate-500 text-sm">
            共 {sortedProducts.length} 个结果
          </span>
        </div>
        <div className="flex items-center gap-4">
          {/* 快速筛选切换按钮 */}
          <div className="flex items-center gap-3">
            {/* Upro服务切换 */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => toggleQuickFilter('uproServices')}
                className={`relative inline-flex items-center w-11 h-6 rounded-full transition-colors duration-200 ${
                  quickFilters.uproServices 
                    ? 'bg-blue-600' 
                    : 'bg-slate-300'
                }`}
              >
                <span
                  className={`inline-block w-4 h-4 bg-white rounded-full transition-transform duration-200 ${
                    quickFilters.uproServices ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
              <span className="text-sm text-slate-600">
                专业服务
              </span>
            </div>

            {/* 在线状态切换 */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => toggleQuickFilter('onlineOnly')}
                className={`relative inline-flex items-center w-11 h-6 rounded-full transition-colors duration-200 ${
                  quickFilters.onlineOnly 
                    ? 'bg-blue-600' 
                    : 'bg-slate-300'
                }`}
              >
                <span
                  className={`inline-block w-4 h-4 bg-white rounded-full transition-transform duration-200 ${
                    quickFilters.onlineOnly ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
              <span className="text-sm text-slate-600">
                即时响应
              </span>
            </div>
          </div>

          {/* 筛选按钮 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="outline" 
                size="sm"
                className="relative bg-white/80 border-slate-200/60 hover:bg-slate-50/80"
              >
                <Filter className="h-4 w-4 mr-2" />
                筛选
                {getActiveFiltersCount() > 0 && (
                  <span className="ml-1 text-xs text-blue-600">
                    ({getActiveFiltersCount()})
                  </span>
                )}
                <ChevronDown className="h-4 w-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-64 bg-white/95 backdrop-blur-lg border-slate-200/60">
              {/* 时间范围 */}
              <DropdownMenuLabel className="text-slate-700">时间范围</DropdownMenuLabel>
              {filterOptions.timeRange.map(option => (
                <DropdownMenuCheckboxItem
                  key={option.value}
                  checked={filters.timeRange === option.value}
                  onCheckedChange={() => setFilters(prev => ({ ...prev, timeRange: option.value }))}
                  className="text-slate-600 hover:bg-slate-50/80"
                >
                  {option.label}
                </DropdownMenuCheckboxItem>
              ))}
              
              <DropdownMenuSeparator />
              
              {/* 价格范围 */}
              <DropdownMenuLabel className="text-slate-700">价格范围</DropdownMenuLabel>
              {filterOptions.priceRange.map(option => (
                <DropdownMenuCheckboxItem
                  key={option.value}
                  checked={filters.priceRange === option.value}
                  onCheckedChange={() => setFilters(prev => ({ ...prev, priceRange: option.value }))}
                  className="text-slate-600 hover:bg-slate-50/80"
                >
                  {option.label}
                </DropdownMenuCheckboxItem>
              ))}
              
              <DropdownMenuSeparator />
              
              {/* 评分筛选 */}
              <DropdownMenuLabel className="text-slate-700">评分</DropdownMenuLabel>
              {filterOptions.rating.map(option => (
                <DropdownMenuCheckboxItem
                  key={option.value}
                  checked={filters.rating === option.value}
                  onCheckedChange={() => setFilters(prev => ({ ...prev, rating: option.value }))}
                  className="text-slate-600 hover:bg-slate-50/80"
                >
                  {option.label}
                </DropdownMenuCheckboxItem>
              ))}
              
              <DropdownMenuSeparator />
              
              {/* 服务类型 */}
              <DropdownMenuLabel className="text-slate-700">服务类型</DropdownMenuLabel>
              {filterOptions.serviceType.map(option => (
                <DropdownMenuCheckboxItem
                  key={option.value}
                  checked={filters.serviceType === option.value}
                  onCheckedChange={() => setFilters(prev => ({ ...prev, serviceType: option.value }))}
                  className="text-slate-600 hover:bg-slate-50/80"
                >
                  {option.label}
                </DropdownMenuCheckboxItem>
              ))}
              
              {getActiveFiltersCount() > 0 && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => {
                      clearAllFilters();
                      setQuickFilters({ uproServices: false, onlineOnly: false });
                    }}
                    className="text-red-600 hover:bg-red-50/80 focus:bg-red-50/80"
                  >
                    <X className="h-4 w-4 mr-2" />
                    清除所有筛选
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
          
          {selectedTags.length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSelectedTags([])}
              className="text-slate-500 hover:text-slate-700"
            >
              清除标签
            </Button>
          )}
        </div>
      </div>

      {/* 产品网格 - 统一使用4列布局 */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {sortedProducts.map((product) => {
          const isFavorited = favorites.includes(product.id);
          return (
            <Card 
              key={product.id} 
              className="group bg-white/90 backdrop-blur-sm border-slate-200/60 hover:shadow-xl hover:shadow-slate-200/40 transition-all duration-300 cursor-pointer overflow-hidden relative"
            >
              {/* 星级评分 - 右上角 */}
              <div className="absolute top-3 right-3 z-10 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full flex items-center gap-1">
                <Star className="h-3 w-3 text-yellow-500 fill-current" />
                <span className="text-xs font-medium text-slate-700">{product.rating}</span>
              </div>

              {/* 商品图片 */}
              <div className="relative aspect-[4/3] bg-gradient-to-br from-slate-50 to-slate-100/50 overflow-hidden">
                <ImageWithFallback
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                
                {/* 商品标签 - 图片左下角 */}
                {product.specialBadge && (
                  <div className="absolute bottom-2 left-2 z-10">
                    {product.specialBadge === "upro" && (
                      <Badge className="bg-purple-600 text-white border-0 text-xs px-2 py-0.5">
                        U PRO
                      </Badge>
                    )}
                    {product.specialBadge === "top" && (
                      <Badge className="bg-amber-600 text-white border-0 text-xs px-2 py-0.5">
                        好评Top
                      </Badge>
                    )}
                    {product.specialBadge === "star" && (
                      <Badge className="bg-blue-600 text-white border-0 text-xs px-2 py-0.5">
                        技能之星
                      </Badge>
                    )}
                  </div>
                )}
              </div>

              <CardContent className="px-4 pt-2 pb-4">
                {/* 创作者信息和评价 */}
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2 flex-1 min-w-0">
                    <div className="relative">
                      <ImageWithFallback
                        src={product.creator.avatar}
                        alt={product.creator.name}
                        className="w-6 h-6 rounded-full object-cover border border-slate-200"
                      />
                      {product.creator.verified && (
                        <div className="absolute -top-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                      )}
                    </div>
                    <span className="text-sm text-slate-600 truncate">{product.creator.name}</span>
                    {product.creator.verified && (
                      <span className="text-xs text-blue-600 bg-blue-50 px-1.5 py-0.5 rounded">认证</span>
                    )}
                  </div>
                  <div className="flex items-center gap-1 flex-shrink-0">
                    <span className="text-xs text-slate-500">
                      ({product.reviews} 评价)
                    </span>
                  </div>
                </div>

                {/* 商品名称 */}
                <h3 className="text-slate-900 mb-1 group-hover:text-blue-700 transition-colors duration-200 line-clamp-1">
                  {product.name}
                </h3>

                {/* 商品描述 */}
                <p className="text-slate-600 text-sm mb-4 line-clamp-2 min-h-[2.5rem]">
                  {product.description}
                </p>

                {/* 底部区域 */}
                <div className="flex items-end justify-between">
                  {/* 左下角 - 收藏按钮和数量 */}
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleFavorite(product.id);
                      }}
                      className={`h-8 w-8 p-0 rounded-full transition-all duration-200 ${
                        isFavorited 
                          ? 'bg-red-50/90 text-red-500 hover:bg-red-100/90' 
                          : 'bg-slate-50/80 text-slate-400 hover:bg-slate-100/90 hover:text-red-500'
                      }`}
                    >
                      <Heart className={`h-4 w-4 ${isFavorited ? 'fill-current' : ''}`} />
                    </Button>
                    <span className="text-xs text-slate-500">
                      {product.favoriteCount}
                    </span>
                  </div>

                  {/* 右下角 - 价格 */}
                  <div className="text-right">
                    <div className="flex items-center gap-1 justify-end">
                      <span className="text-slate-700 font-medium">RMB</span>
                      <span className="text-slate-900 font-medium">
                        {product.price}
                      </span>
                      <span className="text-slate-500 font-medium">起</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* 空状态 */}
      {sortedProducts.length === 0 && (
        <div className="text-center py-12">
          <div className="relative inline-block mb-4">
            <div className="absolute inset-0 bg-slate-200/50 rounded-full blur-xl"></div>
            <Search className="relative h-12 w-12 text-slate-400 mx-auto" />
          </div>
          <h3 className="text-slate-900 mb-2">没有找到相关服务</h3>
          <p className="text-slate-600">请尝试调整搜索条件或筛选选项</p>
        </div>
      )}
    </div>
  );
}