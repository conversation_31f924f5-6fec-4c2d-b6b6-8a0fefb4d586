import { useState, useRef, useEffect } from "react";
import { Button } from "./ui/button";
import { 
  TrendingUp,
  Palette, 
  Code, 
  Megaphone, 
  Camera, 
  PenTool,
  Music,
  Briefcase,
  DollarSign,
  Scale,
  GraduationCap
} from "lucide-react";

interface CategoryNavigationProps {
  activeCategory?: string;
  onCategoryChange?: (category: string) => void;
  onSubCategoryClick?: (category: string, subCategory: string) => void;
}

export function CategoryNavigation({ activeCategory = "popular", onCategoryChange, onSubCategoryClick }: CategoryNavigationProps) {
  const [hoveredCategory, setHoveredCategory] = useState<string | null>(null);
  const [isHovering, setIsHovering] = useState(false);
  const [activeSubCategory, setActiveSubCategory] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 服务类分类
  const mainCategories = [
    { id: "popular", label: "热门项目", icon: TrendingUp },
    { id: "design", label: "图像与设计", icon: Palette },
    { id: "programming", label: "编程与技术", icon: Code },
    { id: "marketing", label: "数字营销", icon: Megaphone },
    { id: "video", label: "视频动画摄影", icon: Camera },
    { id: "writing", label: "写作&翻译", icon: PenTool },
    { id: "music", label: "音乐与音频", icon: Music },
    { id: "business", label: "商业", icon: Briefcase },
    { id: "finance", label: "财务", icon: DollarSign },
    { id: "legal", label: "法律", icon: Scale },
    { id: "academic", label: "学业", icon: GraduationCap }
  ];

  // 子分类数据
  const subCategories = {
    popular: {
      title: "热门项目",
      sections: [
        {
          title: "热门服务",
          items: ["Logo设计", "网站开发", "移动应用开发", "品牌设计", "视频制作", "文案撰写"]
        },
        {
          title: "新兴领域",
          items: ["AI工具开发", "区块链项目", "元宇宙设计", "NFT创作"]
        },
        {
          title: "高需求技能",
          items: ["数据分析", "UI/UX设计", "社交媒体营销", "SEO优化"]
        }
      ]
    },
    design: {
      title: "图像与设计",
      sections: [
        {
          title: "平面设计",
          items: ["Logo设计", "品牌识别", "海报设计", "包装设计", "名片设计"]
        },
        {
          title: "数字设计",
          items: ["UI/UX设计", "网页设计", "移动应用界面", "图标设计"]
        },
        {
          title: "创意服务",
          items: ["插画设计", "3D建模", "产品渲染", "角色设计"]
        }
      ]
    },
    programming: {
      title: "编程与技术",
      sections: [
        {
          title: "网站开发",
          items: ["前端开发", "后端开发", "全栈开发", "电商网站", "企业官网"]
        },
        {
          title: "移动应用",
          items: ["iOS开发", "Android开发", "跨平台应用", "小程序开发"]
        },
        {
          title: "其他技术",
          items: ["数据库设计", "API开发", "系统集成", "技术咨询"]
        }
      ]
    },
    marketing: {
      title: "数字营销",
      sections: [
        {
          title: "社交媒体",
          items: ["社媒运营", "内容策划", "广告投放", "粉丝增长"]
        },
        {
          title: "搜索引擎",
          items: ["SEO优化", "SEM推广", "关键词研究", "网站分析"]
        },
        {
          title: "营销策略",
          items: ["品牌策略", "市场调研", "竞品分析", "营销方案"]
        }
      ]
    },
    video: {
      title: "视频动画摄影",
      sections: [
        {
          title: "视频制作",
          items: ["宣传片制作", "产品视频", "教学视频", "短视频制作"]
        },
        {
          title: "动画设计",
          items: ["2D动画", "3D动画", "MG动画", "角色动画"]
        },
        {
          title: "摄影服务",
          items: ["商业摄影", "产品摄影", "人像摄影", "活动摄影"]
        }
      ]
    },
    writing: {
      title: "写作&翻译",
      sections: [
        {
          title: "内容创作",
          items: ["文案撰写", "博客写作", "产品描述", "新闻稿"]
        },
        {
          title: "翻译服务",
          items: ["中英翻译", "多语种翻译", "本地化服务", "口译服务"]
        },
        {
          title: "编辑校对",
          items: ["文章编辑", "校对服务", "润色修改", "格式调整"]
        }
      ]
    },
    music: {
      title: "音乐与音频",
      sections: [
        {
          title: "音乐制作",
          items: ["原创音乐", "编曲制作", "音乐混音", "音效设计"]
        },
        {
          title: "音频服务",
          items: ["配音服务", "播客制作", "音频编辑", "音频修复"]
        }
      ]
    },
    business: {
      title: "商业",
      sections: [
        {
          title: "商业计划",
          items: ["商业策划", "市场分析", "财务预测", "投资建议"]
        },
        {
          title: "运营管理",
          items: ["流程优化", "项目管理", "团队培训", "绩效评估"]
        }
      ]
    },
    finance: {
      title: "财务",
      sections: [
        {
          title: "财务分析",
          items: ["财务报告", "数据分析", "成本控制", "预算规划"]
        },
        {
          title: "投资理财",
          items: ["投资咨询", "理财规划", "风险评估", "资产配置"]
        }
      ]
    },
    legal: {
      title: "法律",
      sections: [
        {
          title: "法律咨询",
          items: ["合同审查", "法律意见", "纠纷调解", "知识产权"]
        },
        {
          title: "文书服务",
          items: ["法律文书", "协议起草", "法规解读", "合规审查"]
        }
      ]
    },
    academic: {
      title: "学业",
      sections: [
        {
          title: "学术辅导",
          items: ["论文指导", "学术写作", "研究方法", "数据分析"]
        },
        {
          title: "教育服务",
          items: ["课程设计", "教材编写", "在线辅导", "考试辅导"]
        }
      ]
    }
  };

  const handleCategoryClick = (categoryId: string) => {
    // 确保调用回调函数
    if (onCategoryChange) {
      onCategoryChange(categoryId);
    }
    setHoveredCategory(null);
    setIsHovering(false);
    setActiveSubCategory(null);
    
    // 添加调试信息
    console.log(`切换到分类: ${categoryId}`);
  };

  const handleSubCategoryClick = (subCategoryItem: string) => {
    if (!hoveredCategory) return;
    
    // 设置活跃的子分类
    setActiveSubCategory(subCategoryItem);
    
    // 切换到主分类
    onCategoryChange?.(hoveredCategory);
    
    // 调用子分类点击回调（如果提供）
    onSubCategoryClick?.(hoveredCategory, subCategoryItem);
    
    // 关闭下拉菜单
    setHoveredCategory(null);
    setIsHovering(false);
    
    // 提供用户反馈
    console.log(`已选择: ${subCategoryItem} (分类: ${hoveredCategory})`);
  };

  const handleMouseEnter = (categoryId: string) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setHoveredCategory(categoryId);
    setIsHovering(true);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setHoveredCategory(null);
      setIsHovering(false);
    }, 300);
  };

  const handleDropdownMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsHovering(true);
  };

  const handleDropdownMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setHoveredCategory(null);
      setIsHovering(false);
    }, 300);
  };

  // 清理timeout
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <div className="bg-white/80 backdrop-blur-xl border-b border-slate-200/40 relative z-[60]">
      <div className="max-w-7xl mx-auto px-4">
        {/* Apple风格水平导航栏 */}
        <div className="flex items-center justify-center space-x-8 py-3">
          {mainCategories.map((category) => (
            <div
              key={category.id}
              className="relative"
              onMouseEnter={() => handleMouseEnter(category.id)}
              onMouseLeave={handleMouseLeave}
            >
              <Button
                variant="ghost"
                onClick={() => handleCategoryClick(category.id)}
                className={`px-3 py-2 text-sm transition-colors duration-200 ${
                  activeCategory === category.id
                    ? 'text-slate-900'
                    : 'text-slate-600 hover:text-slate-900'
                }`}
              >
                {category.label}
              </Button>
            </div>
          ))}
        </div>

        {/* 悬停下拉菜单 */}
        {hoveredCategory && subCategories[hoveredCategory as keyof typeof subCategories] && isHovering && (
          <div
            ref={dropdownRef}
            className="absolute left-0 right-0 top-full bg-white/95 backdrop-blur-xl border-b border-slate-200/40 shadow-lg z-[50]"
            onMouseEnter={handleDropdownMouseEnter}
            onMouseLeave={handleDropdownMouseLeave}
          >
            <div className="max-w-7xl mx-auto px-4 py-8">
              <div className="max-w-4xl mx-auto">
                {/* 下拉菜单标题 */}
                <h3 className="text-slate-900 text-lg mb-6">
                  {subCategories[hoveredCategory as keyof typeof subCategories].title}
                </h3>

                {/* 子分类内容 */}
                <div className="grid grid-cols-3 gap-x-8 gap-y-6">
                  {subCategories[hoveredCategory as keyof typeof subCategories].sections.map((section, sectionIndex) => (
                    <div key={sectionIndex}>
                      <h4 className="text-slate-700 text-sm mb-3">{section.title}</h4>
                      <ul className="space-y-2">
                        {section.items.map((item, itemIndex) => (
                          <li key={itemIndex}>
                            <Button
                              variant="ghost"
                              onClick={() => handleSubCategoryClick(item)}
                              className={`w-full justify-start px-0 py-1 h-auto text-left text-sm transition-colors duration-200 ${
                                activeSubCategory === item && activeCategory === hoveredCategory
                                  ? 'text-blue-600 font-medium'
                                  : 'text-slate-600 hover:text-blue-600'
                              }`}
                            >
                              {item}
                            </Button>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}