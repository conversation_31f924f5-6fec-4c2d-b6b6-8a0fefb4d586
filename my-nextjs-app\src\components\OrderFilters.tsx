import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Search, Filter, X } from "lucide-react";

interface OrderFiltersProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
  statusFilter: string;
  onStatusFilterChange: (status: string) => void;
  dateFilter: string;
  onDateFilterChange: (date: string) => void;
}

export function OrderFilters({
  searchTerm,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  dateFilter,
  onDateFilterChange
}: OrderFiltersProps) {
  const hasActiveFilters = searchTerm || statusFilter !== "all" || dateFilter !== "all";

  const clearAllFilters = () => {
    onSearchChange("");
    onStatusFilterChange("all");
    onDateFilterChange("all");
  };

  return (
    <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
      {/* 搜索框 */}
      <div className="relative flex-1 max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
        <Input
          type="text"
          placeholder="搜索订单号、商品名称或商家..."
          value={searchTerm}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-10 pr-4 bg-white/80 border-slate-200/60 focus:border-blue-300 focus:ring-blue-500/20"
        />
        {searchTerm && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onSearchChange("")}
            className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-slate-100"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* 状态筛选 */}
      <Select value={statusFilter} onValueChange={onStatusFilterChange}>
        <SelectTrigger className="w-full sm:w-[140px] bg-white/80 border-slate-200/60">
          <SelectValue placeholder="订单状态" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">全部状态</SelectItem>
          <SelectItem value="pending">待处理</SelectItem>
          <SelectItem value="processing">进行中</SelectItem>
          <SelectItem value="shipped">待交付</SelectItem>
          <SelectItem value="delivered">已完成</SelectItem>
          <SelectItem value="cancelled">已取消</SelectItem>
          <SelectItem value="aftersale">售后中</SelectItem>
        </SelectContent>
      </Select>

      {/* 时间筛选 */}
      <Select value={dateFilter} onValueChange={onDateFilterChange}>
        <SelectTrigger className="w-full sm:w-[140px] bg-white/80 border-slate-200/60">
          <SelectValue placeholder="时间范围" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">全部时间</SelectItem>
          <SelectItem value="recent">最近30天</SelectItem>
          <SelectItem value="quarter">最近3个月</SelectItem>
          <SelectItem value="year">最近一年</SelectItem>
        </SelectContent>
      </Select>

      {/* 清除筛选按钮 */}
      {hasActiveFilters && (
        <Button
          variant="outline"
          size="sm"
          onClick={clearAllFilters}
          className="flex items-center gap-2 whitespace-nowrap"
        >
          <X className="h-4 w-4" />
          清除筛选
        </Button>
      )}
    </div>
  );
}
