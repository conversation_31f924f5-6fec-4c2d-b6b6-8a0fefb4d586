import { useState } from "react";
import { <PERSON><PERSON> } from "./ui/button";
import { ImageWithFallback } from "./figma/ImageWithFallback";
import { Card, CardContent } from "./ui/card";
import { Badge } from "./ui/badge";
import { IndustryInsights } from "./IndustryInsights";
import {
  Breadcrumb,
  BreadcrumbList,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "./ui/breadcrumb";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "./ui/dropdown-menu";
import { 
  Search, 
  Filter,
  Grid,
  List,
  Star,
  Heart,
  ShoppingCart,
  Sparkles,
  TrendingUp,
  Award,
  Zap,
  ChevronDown,
  SlidersHorizontal,
  User,
  Bot,
  X,
  Home,
  MapPin
} from "lucide-react";

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  image: string;
  rating: number;
  reviews: number;
  category: string;
  tags: string[];
  isBestseller?: boolean;
  isNew?: boolean;
  creator: {
    name: string;
    avatar: string;
    verified?: boolean;
    location?: string;
  };
  favoriteCount: number;
  specialBadge?: "upro" | "top" | "star";
}

const mockProducts: Product[] = [
  {
    id: "1",
    name: "✨绝美品牌视觉设计，让你的LOGO成为爆款记忆点！",
    description: "姐妹们！这个设计师真的太会了😍 从LOGO到整套VI，各种风格都能拿捏住，原创度100%，商用无忧虑～绝对是品牌升级的必备神器！",
    price: 999,
    image: "https://images.unsplash.com/photo-1558655146-d09347e92766?w=400&h=400&fit=crop",
    rating: 5.0,
    reviews: 1234,
    category: "设计",
    tags: ["热门", "原创"],
    isBestseller: true,
    isNew: false,
    creator: {
      name: "设计师小王",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "北京"
    },
    favoriteCount: 892,
    specialBadge: "upro"
  },
  {
    id: "2",
    name: "🚀全栈网站开发YYDS！React+Node.js技术栈，让你的网站飞起来",
    description: "宝贝们看过来！这位程序员小哥哥技术真的绝了🔥 响应式设计+全栈开发+一键部署，从前端到后端全搞定，网站速度嗖嗖的，用户体验直接拉满！",
    price: 4999,
    image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=400&fit=crop",
    rating: 4.9,
    reviews: 856,
    category: "技术",
    tags: ["专业", "全栈"],
    isBestseller: true,
    creator: {
      name: "程序员老李",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "上海"
    },
    favoriteCount: 634,
    specialBadge: "top"
  },
  {
    id: "3",
    name: "🎬短视频制作神器！抖音快手爆款视频就是这样诞生的",
    description: "集美们，这位小姐姐的视频制作真的绝绝子！💥 脚本策划+专业拍摄+炫酷剪辑+特效加持，每个环节都精雕细琢，保证你的短视频能火出圈～",
    price: 1999,
    image: "https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=400&h=400&fit=crop",
    rating: 4.8,
    reviews: 2341,
    category: "视频",
    tags: ["创意", "营销"],
    isNew: true,
    creator: {
      name: "视频达人小张",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616c45e1f94?w=100&h=100&fit=crop&crop=faces",
      verified: false,
      location: "深圳"
    },
    favoriteCount: 1523,
    specialBadge: "star"
  },
  {
    id: "4",
    name: "💡文案策划大佬在线！让你的品牌文案直击用户心房",
    description: "文案救星来了！📝 无论是品牌slogan还是营销软文，这位文案大神都能写出让人眼前一亮的金句，转化率直接翻倍，客户看了都说好！",
    price: 699,
    image: "https://images.unsplash.com/photo-1455390582262-044cdead277a?w=400&h=400&fit=crop",
    rating: 4.7,
    reviews: 567,
    category: "写作",
    tags: ["营销", "创意"],
    creator: {
      name: "文案师小刘",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "杭州"
    },
    favoriteCount: 789
  },
  {
    id: "5",
    name: "🎵专业音频制作，让你的声音变成治愈系神器！",
    description: "音频控的福音来啦！🎧 录音+混音+后期处理，一条龙服务，播客制作、配音服务样样精通，音质绝对碾压同行，耳朵怀孕系列！",
    price: 899,
    image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=400&fit=crop",
    rating: 4.9,
    reviews: 423,
    category: "音频",
    tags: ["专业", "原创"],
    creator: {
      name: "音频师小陈",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "广州"
    },
    favoriteCount: 456
  },
  {
    id: "6",
    name: "🛒电商运营大神驾到！让你的店铺销量暴涨300%",
    description: "电商小白的救星！💪 淘宝天猫运营、直通车优化、数据分析，这位大佬样样精通，跟着他学准没错，月销百万不是梦！",
    price: 2999,
    image: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=400&h=400&fit=crop",
    rating: 4.6,
    reviews: 234,
    category: "营销",
    tags: ["电商", "数据"],
    isBestseller: true,
    creator: {
      name: "运营专家老王",
      avatar: "https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "杭州"
    },
    favoriteCount: 312,
    specialBadge: "upro"
  },
  {
    id: "7",
    name: "📱UI/UX设计女神出手！用户体验直接封神级别",
    description: "设计界的颜值担当来了！✨ 移动端界面设计+用户体验优化，每个像素都完美无缺，用户用了都说好，这就是传说中的设计天花板！",
    price: 3999,
    image: "https://images.unsplash.com/photo-1586717799252-bd134ad00e26?w=400&h=400&fit=crop",
    rating: 5.0,
    reviews: 678,
    category: "设计",
    tags: ["UI", "移动端"],
    isNew: true,
    creator: {
      name: "UI设计师小美",
      avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "深圳"
    },
    favoriteCount: 923,
    specialBadge: "top"
  },
  {
    id: "8",
    name: "🎨3D建模渲染魔法师！把你的想象力变成现实",
    description: "3D大神出手了！🔥 产品建模+场景渲染+动画制作，技术绝对是顶级的，做出来的效果简直像电影一样逼真，客户看了都震惊！",
    price: 1599,
    image: "https://images.unsplash.com/photo-1558618047-5c1c7d5a6e36?w=400&h=400&fit=crop",
    rating: 4.8,
    reviews: 345,
    category: "3D",
    tags: ["建模", "渲染"],
    creator: {
      name: "3D艺术家小林",
      avatar: "https://images.unsplash.com/photo-1521119989659-a83eee488004?w=100&h=100&fit=crop&crop=faces",
      verified: false,
      location: "成都"
    },
    favoriteCount: 278
  },
  {
    id: "9",
    name: "📲小程序开发达人！微信生态玩得明明白白",
    description: "小程序开发界的扛把子！💯 需求分析+界面设计+功能开发+上线部署，全流程服务，做出来的小程序用户留存率超高！",
    price: 2599,
    image: "https://images.unsplash.com/photo-1512428559087-560fa5ceab42?w=400&h=400&fit=crop",
    rating: 4.7,
    reviews: 423,
    category: "技术",
    tags: ["小程序", "移动端"],
    isNew: true,
    creator: {
      name: "小程序专家小赵",
      avatar: "https://images.unsplash.com/photo-1519648023493-d82b5f8d7b8a?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "北京"
    },
    favoriteCount: 567,
    specialBadge: "upro"
  },
  {
    id: "10",
    name: "🔍SEO优化高手！让你的网站排名蹭蹭往上涨",
    description: "SEO界的隐藏大佬现身！📈 关键词研究+内容优化+外链建设，排名提升有保证，流量暴涨不是梦，网站曝光度直接起飞！",
    price: 1299,
    image: "https://images.unsplash.com/photo-1432888622747-4eb9a8efeb07?w=400&h=400&fit=crop",
    rating: 4.6,
    reviews: 789,
    category: "营销",
    tags: ["SEO", "流量"],
    isBestseller: true,
    creator: {
      name: "SEO专家小周",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "上海"
    },
    favoriteCount: 445,
    specialBadge: "top"
  },
  {
    id: "11",
    name: "🖌️商业插画仙女下凡！每一笔都是艺术品级别",
    description: "插画界的神仙小姐姐！🌟 手绘商业插画+IP形象设计，风格超多样，每幅作品都是独一无二的艺术品，客户收到都舍不得用！",
    price: 599,
    image: "https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=400&fit=crop",
    rating: 4.9,
    reviews: 334,
    category: "设计",
    tags: ["插画", "原创"],
    creator: {
      name: "插画师小汤",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=faces",
      verified: false,
      location: "武汉"
    },
    favoriteCount: 689,
    specialBadge: "star"
  },
  {
    id: "12",
    name: "🎙️播客制作专家！让你的声音传遍全世界",
    description: "播客圈的技术大拿！🎵 录制+剪辑+后期+发布指导，一条龙服务，音质清晰到仿佛在耳边说话，听众粘性超级强！",
    price: 799,
    image: "https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=400&h=400&fit=crop",
    rating: 4.8,
    reviews: 156,
    category: "音频",
    tags: ["播客", "媒体"],
    isNew: true,
    creator: {
      name: "播客制作人小孙",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "杭州"
    },
    favoriteCount: 234
  },
  {
    id: "13",
    name: "📊财务报表分析大师！数字背后的秘密都被他看穿",
    description: "财务界的福尔摩斯！💼 企业财务深度分析+健康诊断+投资建议，专业度爆表，每个数据都能给你讲出故事来，老板必备神器！",
    price: 1999,
    image: "https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=400&fit=crop",
    rating: 4.7,
    reviews: 201,
    category: "财务",
    tags: ["专业", "数据"],
    creator: {
      name: "财务分析师小吴",
      avatar: "https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "北京"
    },
    favoriteCount: 178,
    specialBadge: "upro"
  },
  {
    id: "14",
    name: "⚖️法律合同审查神探！合同陷阱一眼就能识破",
    description: "法律界的守护神！📋 商业合同审查+风险评估+条款优化，专业到位，每个条款都给你把关，让你签合同再也不用担心！",
    price: 899,
    image: "https://images.unsplash.com/photo-1589829545856-d10d557cf95f?w=400&h=400&fit=crop",
    rating: 4.6,
    reviews: 89,
    category: "法律",
    tags: ["合规", "专业"],
    creator: {
      name: "法律顾问小郑",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "上海"
    },
    favoriteCount: 123
  },
  {
    id: "15",
    name: "📚学术论文写作导师！让你的论文从及格变优秀",
    description: "学术界的贴心老师！👩‍🏫 论文写作指导+文献综述+数据分析，每个环节都精心指导，让你的论文脱胎换骨，导师看了都夸赞！",
    price: 699,
    image: "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=400&h=400&fit=crop",
    rating: 4.8,
    reviews: 267,
    category: "学术",
    tags: ["教育", "写作"],
    isBestseller: true,
    creator: {
      name: "学术导师小钱",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "武汉"
    },
    favoriteCount: 345,
    specialBadge: "top"
  },
  {
    id: "16",
    name: "📷产品摄影大师！让你的商品照片美到犯规",
    description: "摄影界的魔法师！✨ 电商主图+产品摄影+后期精修，每张照片都是艺术品，商品颜值直接拉满，买家看了就想下单！",
    price: 599,
    image: "https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=400&h=400&fit=crop",
    rating: 4.9,
    reviews: 456,
    category: "摄影",
    tags: ["电商", "商业"],
    isNew: true,
    creator: {
      name: "摄影师小徐",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "深圳"
    },
    favoriteCount: 567,
    specialBadge: "star"
  },
  {
    id: "17",
    name: "🎯营销策略咨询大咖！让你的品牌营销一击即中",
    description: "营销界的军师！🧠 品牌策略制定+市场调研+竞品分析，每个方案都精准有效，执行下去销量蹭蹭涨，老板看了都开心！",
    price: 3999,
    image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=400&fit=crop",
    rating: 4.7,
    reviews: 134,
    category: "营销",
    tags: ["策略", "咨询"],
    creator: {
      name: "营销顾问小何",
      avatar: "https://images.unsplash.com/photo-1521119989659-a83eee488004?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "广州"
    },
    favoriteCount: 234,
    specialBadge: "upro"
  },
  {
    id: "18",
    name: "📈数据可视化艺术家！让枯燥数据变成美丽图表",
    description: "数据界的颜值担当！🎨 专业可视化设计+图表制作+仪表板开发，每个图表都美得像艺术品，老板汇报必备神器！",
    price: 1599,
    image: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=400&h=400&fit=crop",
    rating: 4.8,
    reviews: 178,
    category: "技术",
    tags: ["数据", "可视化"],
    isNew: true,
    creator: {
      name: "数据工程师小宋",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "北京"
    },
    favoriteCount: 289
  },
  {
    id: "19",
    name: "🌍翻译服务专家！让语言不再是沟通的障碍",
    description: "翻译界的百科全书！📖 中英互译+商务文档+技术资料，翻译质量杠杠的，表达地道到老外都以为是母语写的！",
    price: 299,
    image: "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=400&fit=crop",
    rating: 4.6,
    reviews: 567,
    category: "写作",
    tags: ["翻译", "语言"],
    isBestseller: true,
    creator: {
      name: "翻译专家小冯",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616c45e1f94?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "广州"
    },
    favoriteCount: 445,
    specialBadge: "top"
  }
];

interface ServicesPageProps {
  activeCategory?: string;
  activeSubCategory?: string | null;
  onCategoryChange?: (category: string) => void;
  onSubCategoryChange?: (subCategory: string | null) => void;
  onViewProductDetail?: (productId: string) => void;
}

export function ServicesPage({ 
  activeCategory = "popular", 
  activeSubCategory, 
  onCategoryChange, 
  onSubCategoryChange,
  onViewProductDetail 
}: ServicesPageProps) {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [favorites, setFavorites] = useState<string[]>([]);
  
  // 快速筛选状态
  const [quickFilters, setQuickFilters] = useState({
    uproServices: false,
    onlineOnly: false
  });

  // 筛选产品 - 适配新的服务分类系统
  const filteredProducts = mockProducts.filter(product => {
    let matchesCategory = true;
    
    // 根据新的服务分类系统筛选
    switch (activeCategory) {
      case "popular":
        matchesCategory = product.isBestseller || product.isNew;
        break;
      case "design":
        matchesCategory = product.category === "设计" || product.category === "3D";
        break;
      case "programming":
        matchesCategory = product.category === "技术";
        break;
      case "marketing":
        matchesCategory = product.category === "营销";
        break;
      case "video":
        matchesCategory = product.category === "视频";
        break;
      case "writing":
        matchesCategory = product.category === "写作";
        break;
      case "music":
        matchesCategory = product.category === "音频";
        break;
      case "business":
        matchesCategory = product.tags.includes("专业") || product.tags.includes("数据");
        break;
      case "finance":
        matchesCategory = product.price > 3000;
        break;
      case "legal":
        matchesCategory = product.tags.includes("合规");
        break;
      case "academic":
        matchesCategory = product.tags.includes("教育");
        break;
      default:
        matchesCategory = true;
    }

    // 快速筛选逻辑
    let matchesQuickFilters = true;
    
    // Upro服务筛选
    if (quickFilters.uproServices) {
      matchesQuickFilters = matchesQuickFilters && product.specialBadge === "upro";
    }
    
    // 在线状态筛选 (基于创作者认证状态作为在线指标)
    if (quickFilters.onlineOnly) {
      matchesQuickFilters = matchesQuickFilters && product.creator.verified;
    }

    return matchesCategory && matchesQuickFilters;
  });

  const formatPrice = (price: number) => `${price}起`;

  const toggleFavorite = (productId: string) => {
    setFavorites(prev => 
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  // 切换快速筛选
  const toggleQuickFilter = (filterType: 'uproServices' | 'onlineOnly') => {
    setQuickFilters(prev => ({
      ...prev,
      [filterType]: !prev[filterType]
    }));
  };

  // 获取分类名称
  const getCategoryName = (category: string) => {
    switch (category) {
      case "popular": return "热门项目";
      case "design": return "图像与设计";
      case "programming": return "编程与技术";
      case "marketing": return "数字营销";
      case "video": return "视频动画摄影";
      case "writing": return "写作&翻译";
      case "music": return "音乐与音频";
      case "business": return "商业";
      case "finance": return "财务";
      case "legal": return "法律";
      case "academic": return "学业";
      default: return "服务";
    }
  };

  // 获取分类描述
  const getCategoryDescription = (category: string) => {
    switch (category) {
      case "popular": return "发现最受欢迎的服务项目";
      case "design": return "创意设计和视觉表达服务";
      case "programming": return "专业技术开发解决方案";
      case "marketing": return "数字化营销推广服务";
      case "video": return "专业影像制作服务";
      case "writing": return "文字创作和多语言服务";
      case "music": return "音频制作和音乐创作";
      case "business": return "商业咨询和管理服务";
      case "finance": return "财务分析和投资顾问";
      case "legal": return "法律咨询和文书服务";
      case "academic": return "学术辅导和教育服务";
      default: return "发现专业服务";
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-6">
      {/* 面包屑导航 */}
      <div className="mb-4">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink 
                href="#" 
                onClick={(e) => {
                  e.preventDefault();
                  onCategoryChange?.("popular");
                  onSubCategoryChange?.(null);
                }}
                className="flex items-center gap-1 text-slate-500 hover:text-slate-700 transition-colors cursor-pointer"
              >
                <Home className="h-4 w-4" />
                首页
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            {activeSubCategory ? (
              <>
                <BreadcrumbItem>
                  <BreadcrumbLink 
                    href="#" 
                    onClick={(e) => {
                      e.preventDefault();
                      onSubCategoryChange?.(null);
                    }}
                    className="text-slate-500 hover:text-slate-700 transition-colors cursor-pointer"
                  >
                    {getCategoryName(activeCategory)}
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  <BreadcrumbPage className="text-slate-900">
                    {activeSubCategory}
                  </BreadcrumbPage>
                </BreadcrumbItem>
              </>
            ) : (
              <BreadcrumbItem>
                <BreadcrumbPage className="text-slate-900">
                  {getCategoryName(activeCategory)}
                </BreadcrumbPage>
              </BreadcrumbItem>
            )}
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      {/* 页面标题 */}
      <div className="mb-6 flex items-start justify-between">
        <div>
          <h1 className="text-slate-900 mb-1">
            {activeSubCategory || getCategoryName(activeCategory)}
          </h1>
          <p className="text-slate-600">
            {activeSubCategory 
              ? `${getCategoryName(activeCategory)} - ${activeSubCategory} 相关服务`
              : getCategoryDescription(activeCategory)
            }
          </p>
        </div>

        {/* 右侧咨询按钮 - 只在热门项目显示 */}
        {activeCategory === "popular" && (
          <div className="flex flex-col gap-2">
            <Button
              variant="ghost"
              size="sm"
              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50/60 px-3 py-2 h-auto justify-start text-sm font-normal flex-col items-start"
            >
              <div className="flex items-center">
                <User className="h-4 w-4 mr-2" />
                咨询定制专家
              </div>
              <div className="text-xs text-slate-500 ml-6">专业顾问一对一服务</div>
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="text-purple-600 hover:text-purple-700 hover:bg-purple-50/60 px-3 py-2 h-auto justify-start text-sm font-normal flex-col items-start"
            >
              <div className="flex items-center">
                <Bot className="h-4 w-4 mr-2" />
                使用AI整理需求
              </div>
              <div className="text-xs text-slate-500 ml-6">智能分析匹配最佳方案</div>
            </Button>
          </div>
        )}
      </div>

      {/* 行业洞察卡片区域 */}
      <div className="bg-white/70 backdrop-blur-xl border border-slate-200/60 rounded-2xl p-6 mb-6">
        <IndustryInsights activeCategory={activeCategory} />
      </div>

      {/* 结果统计 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <span className="text-slate-500 text-sm">
            共 {filteredProducts.length} 个结果
          </span>
        </div>
        <div className="flex items-center gap-4">
          {/* 快速筛选切换按钮 */}
          <div className="flex items-center gap-3">
            {/* Upro服务切换 */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => toggleQuickFilter('uproServices')}
                className={`relative inline-flex items-center w-11 h-6 rounded-full transition-colors duration-200 ${
                  quickFilters.uproServices 
                    ? 'bg-blue-600' 
                    : 'bg-slate-300'
                }`}
              >
                <span
                  className={`inline-block w-4 h-4 bg-white rounded-full transition-transform duration-200 ${
                    quickFilters.uproServices ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
              <span className="text-sm text-slate-600">
                专业服务
              </span>
            </div>

            {/* 在线状态切换 */}
            <div className="flex items-center gap-2">
              <button
                onClick={() => toggleQuickFilter('onlineOnly')}
                className={`relative inline-flex items-center w-11 h-6 rounded-full transition-colors duration-200 ${
                  quickFilters.onlineOnly 
                    ? 'bg-blue-600' 
                    : 'bg-slate-300'
                }`}
              >
                <span
                  className={`inline-block w-4 h-4 bg-white rounded-full transition-transform duration-200 ${
                    quickFilters.onlineOnly ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
              <span className="text-sm text-slate-600">
                即时响应
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* 产品网格 */}
      <div className={`grid gap-6 ${
        viewMode === "grid" 
          ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" 
          : "grid-cols-1"
        }`}>
        {filteredProducts.map((product) => (
          <Card 
            key={product.id} 
            className="group bg-white/90 backdrop-blur-sm border-slate-200/60 hover:shadow-xl hover:shadow-slate-200/40 transition-all duration-300 cursor-pointer overflow-hidden"
            onClick={() => onViewProductDetail?.(product.id)}
          >
            <CardContent className="p-0">
              {/* 商品图片 */}
              <div className="relative aspect-square overflow-hidden">
                <ImageWithFallback 
                  src={product.image} 
                  alt={product.name}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                />
                
                {/* 评分显示 - 右上角 */}
                <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 flex items-center gap-1">
                  <Star className="h-3 w-3 text-yellow-500 fill-current" />
                  <span className="text-xs text-slate-700">{product.rating}</span>
                </div>
                
                {/* 商品标签 - 左下角 */}
                {product.specialBadge && (
                  <div className="absolute bottom-3 left-3">
                    <span className={`px-2 py-1 rounded-md text-xs text-white ${
                      product.specialBadge === "upro" 
                        ? "bg-blue-600" 
                        : product.specialBadge === "top" 
                        ? "bg-orange-500" 
                        : "bg-purple-600"
                    }`}>
                      {product.specialBadge === "upro" ? "U pro" : 
                       product.specialBadge === "top" ? "好评Top" : "技能之星"}
                    </span>
                  </div>
                )}

                {/* 收藏按钮 */}
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleFavorite(product.id);
                  }}
                  className="absolute top-3 left-3 p-2 bg-white/80 backdrop-blur-sm rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-white"
                >
                  <Heart className={`h-4 w-4 ${
                    favorites.includes(product.id) 
                      ? "text-red-500 fill-current" 
                      : "text-slate-600"
                  }`} />
                </button>
              </div>
              
              {/* 商品信息 */}
              <div className="p-4">
                {/* 创作者信息 */}
                <div className="flex items-center gap-2 mb-1">
                  <div className="relative">
                    <ImageWithFallback 
                      src={product.creator.avatar} 
                      alt={product.creator.name}
                      className="w-6 h-6 rounded-full object-cover"
                    />
                    {/* 在线状态指示器 */}
                    <div className={`absolute -top-0.5 -right-0.5 w-2 h-2 rounded-full border border-white ${
                      product.creator.verified 
                        ? 'bg-green-500' 
                        : 'bg-slate-400'
                    }`} />
                  </div>
                  <span className="text-sm text-slate-700">{product.creator.name}</span>
                  
                  {/* 认证标识 */}
                  {product.creator.verified && (
                    <span className="text-blue-600 text-xs px-1.5 py-0.5 bg-blue-50 rounded">认证</span>
                  )}
                  
                  {/* 评论信息 */}
                  <span className="text-xs text-slate-500 ml-auto">({product.reviews} 评论)</span>
                </div>
                
                {/* 服务名称 */}
                <h3 className="text-slate-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors leading-tight">
                  {product.name}
                </h3>
                
                {/* 服务描述 */}
                <p className="text-slate-600 text-sm mb-4 line-clamp-2 leading-relaxed">
                  {product.description}
                </p>
                
                {/* 底部信息 */}
                <div className="flex items-center justify-between">
                  {/* 收藏数量 */}
                  <div className="flex items-center gap-1 text-slate-500">
                    <Heart className="h-3 w-3" />
                    <span className="text-xs">{product.favoriteCount}</span>
                  </div>
                  
                  {/* 价格 */}
                  <div className="flex items-center gap-1">
                    <span className="text-slate-900">
                      RMB {formatPrice(product.price)}
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}