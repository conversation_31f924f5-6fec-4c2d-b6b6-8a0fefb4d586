"use client";

import { useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { Package } from "lucide-react";
import { NavigationV80 } from "@/components/NavigationV80";
import { NavigationV78 } from "@/components/NavigationV78";
import { CategoryNavigation } from "@/components/CategoryNavigation";
import { LandingPage } from "@/components/LandingPage";
import { ServicesPage } from "@/components/ServicesPage";
import { OrderCard } from "@/components/OrderCard";
import { OrderStatusTabs } from "@/components/OrderStatusTabs";
import { OrderFilters } from "@/components/OrderFilters";
import { ChatWindow } from "@/components/ChatWindow";
import { ChatFloatingButton } from "@/components/ChatFloatingButton";
import { mockOrders } from "@/data/mockOrders";
import { getStatusCounts, filterOrders } from "@/utils/orderUtils";

// 模拟商品数据
const mockProducts = [
  {
    id: "1",
    name: "✨绝美品牌视觉设计，让你的LOGO成为爆款记忆点！",
    description: "姐妹们！这个设计师真的太会了😍 从LOGO到整套VI，各种风格都能拿捏住，原创度100%，商用无忧虑～绝对是品牌升级的必备神器！",
    price: 999,
    image: "https://images.unsplash.com/photo-1558655146-d09347e92766?w=400&h=400&fit=crop",
    rating: 5.0,
    reviews: 1234,
    category: "设计",
    tags: ["热门", "原创"],
    isBestseller: true,
    isNew: false,
    creator: {
      name: "设计师小王",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",
      verified: true,
      location: "北京",
      isOnline: true,
      averageResponseTime: "3分钟",
      activeOrdersCount: 5
    },
    favoriteCount: 892,
    specialBadge: "hot" as const
  }
];

export default function App() {
  const [currentPage, setCurrentPage] = useState<"landing" | "services" | "orders" | "favorites" | "support" | "gallery" | "login" | "register" | "product">("landing");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [dateFilter, setDateFilter] = useState("all");
  const [selectedOrderId, setSelectedOrderId] = useState<string | null>(null);
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);
  const [activeCategory, setActiveCategory] = useState("popular");
  const [activeSubCategory, setActiveSubCategory] = useState<string | null>(null);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [notificationCount] = useState(3); // 模拟通知数量

  const handleNavigate = (page: string) => {
    setCurrentPage(page as any);
    setSelectedOrderId(null);
    setSelectedProductId(null);
    // 如果导航到首页，重置到Landing页面
    if (page === "home") {
      setCurrentPage("landing");
      setActiveCategory("popular");
      setActiveSubCategory(null);
    }
  };

  const handleNavigateToServices = () => {
    setCurrentPage("services");
    setActiveCategory("popular");
    setActiveSubCategory(null);
  };

  const handleSubCategoryClick = (category: string, subCategory: string) => {
    setActiveCategory(category);
    setActiveSubCategory(subCategory);

    // 如果当前在首页，点击子分类后跳转到服务页面
    if (currentPage === "landing") {
      setCurrentPage("services");
    }

    console.log(`已选择子分类: ${subCategory} 在分类: ${category}`);
  };

  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    setActiveSubCategory(null);
    console.log(`已切换到分类: ${category}`);
  };

  const handleLoginClick = () => {
    setCurrentPage("login");
  };

  const handleOpenChat = () => {
    setIsChatOpen(true);
  };

  const handleCloseChat = () => {
    setIsChatOpen(false);
  };

  const handleMinimizeChat = () => {
    setIsChatOpen(false);
  };

  const handleViewProductDetail = (productId: string) => {
    setSelectedProductId(productId);
    setCurrentPage("product");
  };

  // 筛选订单
  const filteredOrders = useMemo(() => {
    return filterOrders(mockOrders, searchTerm, statusFilter, dateFilter);
  }, [searchTerm, statusFilter, dateFilter]);

  const statusCounts = getStatusCounts(mockOrders);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30">
      {/* 根据页面选择导航栏版本 */}
      {currentPage === "landing" ? (
        <NavigationV80
          onNavigate={handleNavigate}
          onCategoryChange={handleCategoryChange}
          onLoginClick={handleLoginClick}
        />
      ) : (
        <NavigationV78
          currentPage={currentPage === "services" ? "services" : currentPage as any}
          onNavigate={handleNavigate}
          onOpenChat={handleOpenChat}
        />
      )}

      {/* 分类导航栏 - 在首页、服务页面和作品广场显示 */}
      {(currentPage === "landing" || currentPage === "services" || currentPage === "gallery") && (
        <CategoryNavigation
          activeCategory={activeCategory}
          onCategoryChange={handleCategoryChange}
          onSubCategoryClick={handleSubCategoryClick}
        />
      )}

      {/* 根据当前页面渲染不同内容 */}
      {currentPage === "landing" && (
        <LandingPage onNavigateToServices={handleNavigateToServices} />
      )}

        {currentPage === "services" && (
          <ServicesPage
            activeCategory={activeCategory}
            activeSubCategory={activeSubCategory}
            onCategoryChange={handleCategoryChange}
            onSubCategoryChange={setActiveSubCategory}
            onViewProductDetail={handleViewProductDetail}
          />
        )}

        {currentPage === "orders" && (
          <>
            {/* 订单页面头部 */}
            <div className="bg-white/70 backdrop-blur-xl border-b border-slate-200/60">
              <div className="max-w-7xl mx-auto px-4 py-6">
                {/* 页面标题 */}
                <div className="flex items-center gap-3 mb-8">
                  <div className="relative">
                    <div className="absolute inset-0 bg-blue-500/10 rounded-xl blur-sm"></div>
                    <div className="relative p-3 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-xl border border-blue-200/30">
                      <Package className="h-6 w-6 text-blue-600" />
                    </div>
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-slate-900">我的订单</h1>
                    <p className="text-slate-600">管理您的所有订单</p>
                  </div>
                </div>

                {/* 状态标签 */}
                <div className="mb-8">
                  <OrderStatusTabs
                    activeStatus={statusFilter}
                    onStatusChange={setStatusFilter}
                    statusCounts={statusCounts}
                  />
                </div>

                {/* 筛选器 */}
                <OrderFilters
                  searchTerm={searchTerm}
                  onSearchChange={setSearchTerm}
                  statusFilter={statusFilter}
                  onStatusFilterChange={setStatusFilter}
                  dateFilter={dateFilter}
                  onDateFilterChange={setDateFilter}
                />
              </div>
            </div>

            {/* 订单列表 */}
            <div className="max-w-7xl mx-auto px-4 py-6">
              {filteredOrders.length > 0 ? (
                <div className="space-y-4">
                  {filteredOrders.map((order) => (
                    <OrderCard
                      key={order.id}
                      order={order}
                      onViewDetail={() => setSelectedOrderId(order.id)}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <div className="relative inline-block mb-4">
                    <div className="absolute inset-0 bg-slate-200/50 rounded-full blur-xl"></div>
                    <Package className="relative h-12 w-12 text-slate-400 mx-auto" />
                  </div>
                  <h3 className="text-lg font-medium text-slate-900 mb-2">暂无订单</h3>
                  <p className="text-slate-600">
                    {searchTerm || statusFilter !== "all" || dateFilter !== "all"
                      ? "没有找到符合条件的订单，请尝试调整筛选条件"
                      : "您还没有任何订单，快去购物吧！"
                    }
                  </p>
                </div>
              )}
            </div>
          </>
        )}

        {currentPage === "favorites" && (
          <div className="max-w-7xl mx-auto px-4 py-8">
            <h2 className="text-2xl font-bold text-slate-900 mb-6">我的收藏</h2>
            <div className="text-center py-20">
              <p className="text-slate-600">暂无收藏内容</p>
            </div>
          </div>
        )}

        {/* 其他页面的占位符 */}
        {(currentPage === "support" || currentPage === "gallery") && (
          <div className="max-w-7xl mx-auto px-4 py-8">
            <div className="text-center py-20">
              <h2 className="text-2xl font-bold text-slate-900 mb-4">
                {currentPage === "support" ? "定制服务" : "作品广场"}
              </h2>
              <p className="text-slate-600">功能开发中，敬请期待...</p>
            </div>
          </div>
        )}

        {(currentPage === "login" || currentPage === "register") && (
          <div className="max-w-md mx-auto px-4 py-20">
            <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-8">
              <h2 className="text-2xl font-bold text-slate-900 mb-6 text-center">
                {currentPage === "login" ? "登录" : "注册"}
              </h2>
              <p className="text-slate-600 text-center">功能开发中，敬请期待...</p>
            </div>
          </div>
        )}

        {/* 聊天窗口 */}
        <ChatWindow isOpen={isChatOpen} onClose={handleCloseChat} onMinimize={handleMinimizeChat} />

        {/* 聊天悬浮球 - 当聊天窗口关闭时显示 */}
        <ChatFloatingButton
          onClick={handleOpenChat}
          notificationCount={notificationCount}
          isVisible={!isChatOpen}
        />
    </div>
  );
}
