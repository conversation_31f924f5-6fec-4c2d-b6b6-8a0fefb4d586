"use client";

import { useState } from "react";
import { NavigationV80 } from "@/components/NavigationV80";
import { CategoryNavigation } from "@/components/CategoryNavigation";
import { LandingPage } from "@/components/LandingPage";
import { ServicesPageContent } from "@/components/ServicesPage";
import { ChatWindow } from "@/components/ChatWindow";
import { ChatFloatingButton } from "@/components/ChatFloatingButton";
import { useRouter } from "next/navigation";

export default function Home() {
  const router = useRouter();
  const [activeCategory, setActiveCategory] = useState("popular");
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [notificationCount] = useState(3);

  const handleNavigate = (page: string) => {
    switch (page) {
      case "home":
      case "landing":
        router.push("/");
        break;
      case "services":
        router.push("/services");
        break;
      case "orders":
        router.push("/orders");
        break;
      case "favorites":
        router.push("/favorites");
        break;
      case "support":
        router.push("/support");
        break;
      case "gallery":
        router.push("/gallery");
        break;
      case "login":
        router.push("/login");
        break;
      case "register":
        router.push("/register");
        break;
      default:
        break;
    }
  };

  const handleNavigateToServices = () => {
    router.push("/services");
  };

  const handleSubCategoryClick = (category: string, subCategory: string) => {
    setActiveCategory(category);
    // 点击子分类后跳转到服务页面
    router.push(`/services?category=${category}&sub=${subCategory}`);
  };

  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    console.log(`已切换到分类: ${category}`);
  };

  const handleLoginClick = () => {
    router.push("/login");
  };

  const handleOpenChat = () => {
    setIsChatOpen(true);
  };

  const handleCloseChat = () => {
    setIsChatOpen(false);
  };

  const handleMinimizeChat = () => {
    setIsChatOpen(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30">
      <NavigationV80
        onNavigate={handleNavigate}
        onCategoryChange={handleCategoryChange}
        onLoginClick={handleLoginClick}
      />

      <CategoryNavigation
        activeCategory={activeCategory}
        onCategoryChange={handleCategoryChange}
        onSubCategoryClick={handleSubCategoryClick}
      />

      <LandingPage onNavigateToServices={handleNavigateToServices} />

      {/* 服务内容区域 */}
      <ServicesPageContent
        activeCategory={activeCategory}
        activeSubCategory={null}
        onCategoryChange={handleCategoryChange}
        onSubCategoryChange={() => {}}
      />

      {/* 聊天窗口 */}
      <ChatWindow isOpen={isChatOpen} onClose={handleCloseChat} onMinimize={handleMinimizeChat} />

      {/* 聊天悬浮球 */}
      <ChatFloatingButton
        onClick={handleOpenChat}
        notificationCount={notificationCount}
        isVisible={!isChatOpen}
      />
    </div>
  );
}
