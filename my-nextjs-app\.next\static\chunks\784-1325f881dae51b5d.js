"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[784],{5784:(e,s,t)=>{t.d(s,{Q:()=>j});var a=t(8260),l=t(7688),r=t(3657);function i(e){let[s,t]=(0,l.useState)(!1),{src:r,alt:i,style:n,className:o,...c}=e;return s?(0,a.jsx)("div",{className:"inline-block bg-gray-100 text-center align-middle ".concat(null!=o?o:""),style:n,children:(0,a.jsx)("div",{className:"flex items-center justify-center w-full h-full",children:(0,a.jsx)("img",{src:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODgiIGhlaWdodD0iODgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgc3Ryb2tlPSIjMDAwIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBvcGFjaXR5PSIuMyIgZmlsbD0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIzLjciPjxyZWN0IHg9IjE2IiB5PSIxNiIgd2lkdGg9IjU2IiBoZWlnaHQ9IjU2IiByeD0iNiIvPjxwYXRoIGQ9Im0xNiA1OCAxNi0xOCAzMiAzMiIvPjxjaXJjbGUgY3g9IjUzIiBjeT0iMzUiIHI9IjciLz48L3N2Zz4KCg==",alt:"Error loading image",...c,"data-original-url":r})})}):(0,a.jsx)("img",{src:r,alt:i,className:o,style:n,...c,onError:()=>{t(!0)}})}var n=t(7271),o=t(6384),c=t(1581),d=t(735),x=t(6281),m=t(278),h=t(2256),u=t(7951),g=t(9858),b=t(120),v=t(3172);function j(e){let{currentPage:s="services",onNavigate:t,onOpenChat:j}=e,[p,f]=(0,l.useState)(!1),[N,w]=(0,l.useState)(""),[y]=(0,l.useState)(3),I=(0,l.useRef)(null),z=e=>{null==t||t(e),f(!1)};return(0,l.useEffect)(()=>{let e=e=>{I.current&&!I.current.contains(e.target)&&f(!1)};return p&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[p]),(0,a.jsx)("nav",{className:"bg-white/90 backdrop-blur-sm border-b border-slate-200/60 sticky top-0 z-[70]",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsx)("div",{className:"flex items-center gap-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-3 cursor-pointer",onClick:()=>z("home"),children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-blue-500/10 rounded-xl blur-sm"}),(0,a.jsx)("div",{className:"relative p-2 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-xl border border-blue-200/30",children:(0,a.jsx)(n.A,{className:"h-6 w-6 text-blue-600"})})]}),(0,a.jsx)("span",{className:"text-slate-900 text-lg font-bold tracking-tight",children:"U-bund"})]})}),(0,a.jsxs)("div",{className:"flex-1 max-w-4xl mx-8 hidden md:flex items-center gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(r.$,{variant:"ghost",size:"sm",onClick:()=>z("support"),className:"flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ".concat("support"===s?"bg-blue-50/80 text-blue-700 shadow-sm":"text-slate-600 hover:text-slate-900 hover:bg-slate-100/80"),children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),"定制服务*"]}),(0,a.jsxs)(r.$,{variant:"ghost",size:"sm",onClick:()=>z("services"),className:"flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ".concat("services"===s?"bg-blue-50/80 text-blue-700 shadow-sm":"text-slate-600 hover:text-slate-900 hover:bg-slate-100/80"),children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),"找服务"]}),(0,a.jsxs)(r.$,{variant:"ghost",size:"sm",onClick:()=>z("gallery"),className:"flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ".concat("gallery"===s?"bg-blue-50/80 text-blue-700 shadow-sm":"text-slate-600 hover:text-slate-900 hover:bg-slate-100/80"),children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),"作品广场*"]})]}),(0,a.jsxs)("div",{className:"flex-1 max-w-md relative",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,a.jsx)("input",{type:"text",placeholder:"搜索服务、商家...",value:N,onChange:e=>w(e.target.value),className:"w-full pl-10 pr-4 py-2 bg-slate-50/80 border border-slate-200/60 rounded-lg text-sm placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-300 transition-all duration-200"})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(r.$,{variant:"ghost",size:"sm",onClick:()=>z("orders"),className:"hidden sm:flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ".concat("orders"===s?"bg-blue-50/80 text-blue-700 shadow-sm":"text-slate-600 hover:text-slate-900 hover:bg-slate-100/80"),children:[(0,a.jsx)(x.A,{className:"h-4 w-4"}),"我的订单"]}),(0,a.jsxs)(r.$,{variant:"ghost",size:"sm",onClick:()=>z("favorites"),className:"hidden sm:flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ".concat("favorites"===s?"bg-blue-50/80 text-blue-700 shadow-sm":"text-slate-600 hover:text-slate-900 hover:bg-slate-100/80"),children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),"我的收藏"]}),(0,a.jsx)("div",{className:"relative",children:(0,a.jsxs)(r.$,{variant:"ghost",size:"sm",onClick:()=>{null==j||j()},className:"relative p-2 rounded-lg text-slate-600 hover:text-slate-900 hover:bg-slate-100/80 transition-all duration-200",children:[(0,a.jsx)(h.A,{className:"h-5 w-5"}),y>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium",children:y})]})}),(0,a.jsxs)("div",{className:"relative",ref:I,children:[(0,a.jsxs)(r.$,{variant:"ghost",size:"sm",onClick:()=>f(!p),className:"flex items-center gap-2 p-1 pr-2 hover:bg-slate-100/80 transition-colors duration-200",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i,{src:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",alt:"用户头像",className:"w-8 h-8 rounded-full object-cover border border-slate-200/60"}),(0,a.jsx)("div",{className:"absolute -top-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white"}),y>0&&(0,a.jsx)("div",{className:"absolute -top-2 -right-2 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-white",children:y>9?"9+":y})]}),(0,a.jsx)("span",{className:"hidden sm:block text-slate-700 text-sm",children:"张小明"}),(0,a.jsx)(u.A,{className:"h-4 w-4 text-slate-400 transition-transform duration-200 ".concat(p?"rotate-180":"")})]}),p&&(0,a.jsxs)("div",{className:"absolute right-0 top-full mt-2 w-48 bg-white/95 backdrop-blur-sm border border-slate-200/60 rounded-xl shadow-lg py-2 z-[200]",children:[(0,a.jsxs)("div",{className:"px-4 py-3 border-b border-slate-200/60",children:[(0,a.jsx)("p",{className:"text-slate-900 text-sm",children:"张小明"}),(0,a.jsx)("p",{className:"text-slate-500 text-xs",children:"<EMAIL>"})]}),(0,a.jsxs)("div",{className:"py-1",children:[(0,a.jsxs)(r.$,{variant:"ghost",size:"sm",onClick:()=>{null==j||j(),f(!1)},className:"w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80 relative",children:[(0,a.jsx)(h.A,{className:"h-4 w-4 mr-3"}),"通知消息",y>0&&(0,a.jsx)("span",{className:"ml-auto bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] text-center",children:y>9?"9+":y})]}),(0,a.jsxs)(r.$,{variant:"ghost",size:"sm",onClick:()=>z("profile"),className:"w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-3"}),"个人资料*"]}),(0,a.jsxs)(r.$,{variant:"ghost",size:"sm",className:"w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80",children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-3"}),"认证专区*"]}),(0,a.jsxs)(r.$,{variant:"ghost",size:"sm",className:"w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-3"}),"账户设置*"]})]}),(0,a.jsx)("div",{className:"border-t border-slate-200/60 pt-1",children:(0,a.jsx)(r.$,{variant:"ghost",size:"sm",className:"w-full justify-start px-4 py-2 text-red-600 hover:text-red-700 hover:bg-red-50/80",children:"退出登录"})})]})]})]})]})})})}}}]);