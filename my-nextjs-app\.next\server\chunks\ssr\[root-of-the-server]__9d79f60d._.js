module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/lib/api-client.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// API客户端配置和类型定义
__turbopack_context__.s({
    "ApiClient": ()=>ApiClient,
    "ApiClientError": ()=>ApiClientError,
    "apiClient": ()=>apiClient,
    "default": ()=>__TURBOPACK__default__export__
});
class ApiClientError extends Error {
    code;
    status;
    errors;
    constructor(message, code, status, errors){
        super(message);
        this.name = 'ApiClientError';
        this.code = code;
        this.status = status;
        this.errors = errors;
    }
}
class ApiClient {
    baseURL;
    timeout;
    defaultHeaders;
    onError;
    onSuccess;
    constructor(config = {}){
        this.baseURL = config.baseURL || '/api';
        this.timeout = config.timeout || 10000;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            ...config.headers
        };
        this.onError = config.onError;
        this.onSuccess = config.onSuccess;
    }
    // 设置认证token
    setAuthToken(token) {
        this.defaultHeaders['Authorization'] = `Bearer ${token}`;
    }
    // 移除认证token
    removeAuthToken() {
        delete this.defaultHeaders['Authorization'];
    }
    // 通用请求方法
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        // 创建AbortController用于超时控制
        const controller = new AbortController();
        const timeoutId = setTimeout(()=>controller.abort(), this.timeout);
        try {
            const response = await fetch(url, {
                ...options,
                headers: {
                    ...this.defaultHeaders,
                    ...options.headers
                },
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            // 解析响应
            let responseData;
            try {
                responseData = await response.json();
            } catch  {
                // 如果响应不是JSON，创建默认响应结构
                responseData = {
                    success: response.ok,
                    message: response.ok ? 'Success' : 'Request failed'
                };
            }
            // 检查HTTP状态码
            if (!response.ok) {
                const error = new ApiClientError(responseData.message || `HTTP ${response.status}: ${response.statusText}`, responseData.code, response.status, responseData.errors);
                this.onError?.(error);
                throw error;
            }
            // 检查业务状态码
            if (!responseData.success) {
                const error = new ApiClientError(responseData.message || 'Request failed', responseData.code, response.status, responseData.errors);
                this.onError?.(error);
                throw error;
            }
            this.onSuccess?.(responseData);
            return responseData;
        } catch (error) {
            clearTimeout(timeoutId);
            if (error instanceof ApiClientError) {
                throw error;
            }
            // 处理网络错误、超时等
            if (error instanceof Error) {
                if (error.name === 'AbortError') {
                    const timeoutError = new ApiClientError('Request timeout', 'TIMEOUT', 408);
                    this.onError?.(timeoutError);
                    throw timeoutError;
                }
                const networkError = new ApiClientError(error.message || 'Network error', 'NETWORK_ERROR', 0);
                this.onError?.(networkError);
                throw networkError;
            }
            // 未知错误
            const unknownError = new ApiClientError('Unknown error', 'UNKNOWN_ERROR', 0);
            this.onError?.(unknownError);
            throw unknownError;
        }
    }
    // GET请求
    async get(endpoint, params) {
        let url = endpoint;
        if (params) {
            const searchParams = new URLSearchParams();
            Object.entries(params).forEach(([key, value])=>{
                if (value !== undefined && value !== null) {
                    searchParams.append(key, String(value));
                }
            });
            if (searchParams.toString()) {
                url += `?${searchParams.toString()}`;
            }
        }
        return this.request(url, {
            method: 'GET'
        });
    }
    // POST请求
    async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: data ? JSON.stringify(data) : undefined
        });
    }
    // PUT请求
    async put(endpoint, data) {
        return this.request(endpoint, {
            method: 'PUT',
            body: data ? JSON.stringify(data) : undefined
        });
    }
    // PATCH请求
    async patch(endpoint, data) {
        return this.request(endpoint, {
            method: 'PATCH',
            body: data ? JSON.stringify(data) : undefined
        });
    }
    // DELETE请求
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }
    // 文件上传
    async upload(endpoint, formData) {
        const headers = {
            ...this.defaultHeaders
        };
        delete headers['Content-Type']; // 让浏览器自动设置multipart/form-data
        return this.request(endpoint, {
            method: 'POST',
            body: formData,
            headers
        });
    }
}
const apiClient = new ApiClient({
    baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',
    timeout: 10000
});
;
const __TURBOPACK__default__export__ = apiClient;
}),
"[project]/src/lib/auth-api.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AuthAPI": ()=>AuthAPI,
    "UserAPI": ()=>UserAPI,
    "authAPI": ()=>authAPI,
    "default": ()=>__TURBOPACK__default__export__,
    "userAPI": ()=>userAPI
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-ssr] (ecmascript)");
;
class AuthAPI {
    // 用户注册
    static async register(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post('/auth/register', data);
    }
    // 用户登录
    static async login(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post('/auth/login', data);
    }
    // 用户登出
    static async logout() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post('/auth/logout');
    }
    // 刷新Token
    static async refreshToken(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post('/auth/refresh', data);
    }
    // 获取当前用户信息
    static async getCurrentUser() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get('/auth/me');
    }
    // 修改密码
    static async changePassword(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post('/auth/change-password', data);
    }
    // 请求重置密码
    static async requestPasswordReset(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post('/auth/reset-password', data);
    }
    // 确认重置密码
    static async confirmPasswordReset(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post('/auth/reset-password/confirm', data);
    }
    // 验证Token是否有效
    static async validateToken() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get('/auth/validate');
    }
}
class UserAPI {
    // 更新用户资料
    static async updateProfile(data) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].patch('/user/profile', data);
    }
    // 上传头像
    static async uploadAvatar(file) {
        const formData = new FormData();
        formData.append('avatar', file);
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].upload('/user/avatar', formData);
    }
    // 删除账户
    static async deleteAccount(password) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].post('/user/delete', {
            password
        });
    }
    // 获取用户统计信息
    static async getUserStats() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get('/user/stats');
    }
}
const authAPI = AuthAPI;
const userAPI = UserAPI;
const __TURBOPACK__default__export__ = {
    auth: AuthAPI,
    user: UserAPI
};
}),
"[project]/src/lib/error-handler.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ErrorHandler": ()=>ErrorHandler,
    "default": ()=>__TURBOPACK__default__export__,
    "dismissLoading": ()=>dismissLoading,
    "handleApiError": ()=>handleApiError,
    "showInfo": ()=>showInfo,
    "showLoading": ()=>showLoading,
    "showSuccess": ()=>showSuccess,
    "showWarning": ()=>showWarning
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$hot$2d$toast$40$2$2e$5$2e$2_react_4ea650e5376fd58cd959e858ce6285c0$2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/react-hot-toast@2.5.2_react_4ea650e5376fd58cd959e858ce6285c0/node_modules/react-hot-toast/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-ssr] (ecmascript)");
;
;
class ErrorHandler {
    // 显示错误提示
    static showError(error) {
        let errorInfo;
        if (typeof error === 'string') {
            errorInfo = {
                message: error
            };
        } else if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiClientError"]) {
            errorInfo = {
                message: error.message,
                code: error.code,
                status: error.status,
                errors: error.errors
            };
        } else {
            errorInfo = {
                message: error.message || 'Unknown error'
            };
        }
        // 根据错误类型显示不同的提示
        this.displayErrorToast(errorInfo);
        // 记录错误日志
        this.logError(errorInfo);
    }
    // 显示成功提示
    static showSuccess(message) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$hot$2d$toast$40$2$2e$5$2e$2_react_4ea650e5376fd58cd959e858ce6285c0$2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].success(message, {
            duration: 3000,
            position: 'top-right'
        });
    }
    // 显示警告提示
    static showWarning(message) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$hot$2d$toast$40$2$2e$5$2e$2_react_4ea650e5376fd58cd959e858ce6285c0$2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(message, {
            duration: 4000,
            position: 'top-right',
            icon: '⚠️'
        });
    }
    // 显示信息提示
    static showInfo(message) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$hot$2d$toast$40$2$2e$5$2e$2_react_4ea650e5376fd58cd959e858ce6285c0$2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(message, {
            duration: 3000,
            position: 'top-right',
            icon: 'ℹ️'
        });
    }
    // 显示加载提示
    static showLoading(message = 'Loading...') {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$hot$2d$toast$40$2$2e$5$2e$2_react_4ea650e5376fd58cd959e858ce6285c0$2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].loading(message, {
            position: 'top-right'
        });
    }
    // 关闭加载提示
    static dismissLoading(toastId) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$hot$2d$toast$40$2$2e$5$2e$2_react_4ea650e5376fd58cd959e858ce6285c0$2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].dismiss(toastId);
    }
    // 显示错误Toast
    static displayErrorToast(errorInfo) {
        let message = errorInfo.message;
        // 如果有字段验证错误，显示第一个错误
        if (errorInfo.errors) {
            const firstError = Object.values(errorInfo.errors)[0];
            if (firstError && firstError.length > 0) {
                message = firstError[0];
            }
        }
        // 根据错误码或状态码自定义消息
        const customMessage = this.getCustomErrorMessage(errorInfo);
        if (customMessage) {
            message = customMessage;
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$react$2d$hot$2d$toast$40$2$2e$5$2e$2_react_4ea650e5376fd58cd959e858ce6285c0$2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].error(message, {
            duration: 5000,
            position: 'top-right'
        });
    }
    // 获取自定义错误消息
    static getCustomErrorMessage(errorInfo) {
        // 根据HTTP状态码
        switch(errorInfo.status){
            case 400:
                return '请求参数错误，请检查输入信息';
            case 401:
                return '登录已过期，请重新登录';
            case 403:
                return '没有权限执行此操作';
            case 404:
                return '请求的资源不存在';
            case 409:
                return '数据冲突，请刷新页面后重试';
            case 422:
                return '数据验证失败，请检查输入信息';
            case 429:
                return '请求过于频繁，请稍后再试';
            case 500:
                return '服务器内部错误，请稍后重试';
            case 502:
                return '服务暂时不可用，请稍后重试';
            case 503:
                return '服务维护中，请稍后重试';
            case 504:
                return '请求超时，请检查网络连接';
        }
        // 根据错误码
        switch(errorInfo.code){
            case 'TIMEOUT':
                return '请求超时，请检查网络连接';
            case 'NETWORK_ERROR':
                return '网络连接失败，请检查网络设置';
            case 'VALIDATION_ERROR':
                return '输入信息有误，请检查后重试';
            case 'AUTH_FAILED':
                return '认证失败，请重新登录';
            case 'PERMISSION_DENIED':
                return '权限不足，无法执行此操作';
            case 'RESOURCE_NOT_FOUND':
                return '请求的资源不存在';
            case 'DUPLICATE_RESOURCE':
                return '资源已存在，请勿重复操作';
            case 'RATE_LIMIT_EXCEEDED':
                return '操作过于频繁，请稍后再试';
            default:
                return null;
        }
    }
    // 记录错误日志
    static logError(errorInfo) {
        // 在开发环境下打印详细错误信息
        if ("TURBOPACK compile-time truthy", 1) {
            console.error('API Error:', errorInfo);
        }
        // 在生产环境下可以发送到错误监控服务
        if (("TURBOPACK compile-time value", "development") === 'production') {
        // 这里可以集成Sentry、LogRocket等错误监控服务
        // 例如：Sentry.captureException(errorInfo);
        }
    }
    // 处理表单验证错误
    static handleValidationErrors(errors) {
        const formErrors = {};
        Object.entries(errors).forEach(([field, messages])=>{
            if (messages && messages.length > 0) {
                formErrors[field] = messages[0];
            }
        });
        return formErrors;
    }
    // 检查是否为认证错误
    static isAuthError(error) {
        return error.status === 401 || error.code === 'AUTH_FAILED';
    }
    // 检查是否为权限错误
    static isPermissionError(error) {
        return error.status === 403 || error.code === 'PERMISSION_DENIED';
    }
    // 检查是否为网络错误
    static isNetworkError(error) {
        return error.code === 'NETWORK_ERROR' || error.code === 'TIMEOUT';
    }
    // 检查是否为验证错误
    static isValidationError(error) {
        return error.status === 422 || error.code === 'VALIDATION_ERROR' || !!error.errors;
    }
}
const handleApiError = (error)=>{
    ErrorHandler.showError(error);
};
const showSuccess = (message)=>{
    ErrorHandler.showSuccess(message);
};
const showWarning = (message)=>{
    ErrorHandler.showWarning(message);
};
const showInfo = (message)=>{
    ErrorHandler.showInfo(message);
};
const showLoading = (message)=>{
    return ErrorHandler.showLoading(message);
};
const dismissLoading = (toastId)=>{
    ErrorHandler.dismissLoading(toastId);
};
const __TURBOPACK__default__export__ = ErrorHandler;
}),
"[project]/src/contexts/AuthContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "AuthProvider": ()=>AuthProvider,
    "default": ()=>__TURBOPACK__default__export__,
    "useAuth": ()=>useAuth,
    "useRequireAuth": ()=>useRequireAuth
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/auth-api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api-client.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/error-handler.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
// 创建认证上下文
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// 本地存储键名
const TOKEN_KEY = 'auth_token';
const REFRESH_TOKEN_KEY = 'refresh_token';
const USER_KEY = 'auth_user';
function AuthProvider({ children }) {
    const [authState, setAuthState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        user: null,
        token: null,
        isLoading: true,
        isAuthenticated: false
    });
    // 初始化认证状态
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        initializeAuth();
    }, []);
    // 初始化认证
    const initializeAuth = async ()=>{
        try {
            const token = localStorage.getItem(TOKEN_KEY);
            const userStr = localStorage.getItem(USER_KEY);
            if (token && userStr) {
                const user = JSON.parse(userStr);
                // 设置API客户端的认证token
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].setAuthToken(token);
                // 验证token是否仍然有效
                try {
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthAPI"].validateToken();
                    setAuthState({
                        user,
                        token,
                        isLoading: false,
                        isAuthenticated: true
                    });
                } catch (error) {
                    // Token无效，尝试刷新
                    await tryRefreshToken();
                }
            } else {
                setAuthState((prev)=>({
                        ...prev,
                        isLoading: false
                    }));
            }
        } catch (error) {
            console.error('Initialize auth error:', error);
            clearAuthData();
            setAuthState((prev)=>({
                    ...prev,
                    isLoading: false
                }));
        }
    };
    // 尝试刷新token
    const tryRefreshToken = async ()=>{
        try {
            const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);
            if (refreshToken) {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthAPI"].refreshToken({
                    refreshToken
                });
                if (response.success && response.data) {
                    saveAuthData(response.data);
                    setAuthState({
                        user: response.data.user,
                        token: response.data.token,
                        isLoading: false,
                        isAuthenticated: true
                    });
                    return;
                }
            }
            // 刷新失败，清除认证数据
            clearAuthData();
            setAuthState((prev)=>({
                    ...prev,
                    isLoading: false
                }));
        } catch (error) {
            clearAuthData();
            setAuthState((prev)=>({
                    ...prev,
                    isLoading: false
                }));
        }
    };
    // 保存认证数据
    const saveAuthData = (authData)=>{
        localStorage.setItem(TOKEN_KEY, authData.token);
        localStorage.setItem(USER_KEY, JSON.stringify(authData.user));
        if (authData.refreshToken) {
            localStorage.setItem(REFRESH_TOKEN_KEY, authData.refreshToken);
        }
        // 设置API客户端的认证token
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].setAuthToken(authData.token);
    };
    // 清除认证数据
    const clearAuthData = ()=>{
        localStorage.removeItem(TOKEN_KEY);
        localStorage.removeItem(REFRESH_TOKEN_KEY);
        localStorage.removeItem(USER_KEY);
        // 移除API客户端的认证token
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].removeAuthToken();
    };
    // 登录
    const login = async (data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthAPI"].login(data);
            if (response.success && response.data) {
                saveAuthData(response.data);
                setAuthState({
                    user: response.data.user,
                    token: response.data.token,
                    isLoading: false,
                    isAuthenticated: true
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ErrorHandler"].showSuccess('登录成功！');
                return true;
            }
            return false;
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiClientError"]) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ErrorHandler"].showError(error);
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ErrorHandler"].showError('登录失败，请重试');
            }
            return false;
        }
    };
    // 注册
    const register = async (data)=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthAPI"].register(data);
            if (response.success && response.data) {
                saveAuthData(response.data);
                setAuthState({
                    user: response.data.user,
                    token: response.data.token,
                    isLoading: false,
                    isAuthenticated: true
                });
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ErrorHandler"].showSuccess('注册成功！');
                return true;
            }
            return false;
        } catch (error) {
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiClientError"]) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ErrorHandler"].showError(error);
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ErrorHandler"].showError('注册失败，请重试');
            }
            return false;
        }
    };
    // 登出
    const logout = async ()=>{
        try {
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthAPI"].logout();
        } catch (error) {
            // 即使登出API失败，也要清除本地数据
            console.error('Logout API error:', error);
        } finally{
            clearAuthData();
            setAuthState({
                user: null,
                token: null,
                isLoading: false,
                isAuthenticated: false
            });
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ErrorHandler"].showSuccess('已退出登录');
        }
    };
    // 刷新认证状态
    const refreshAuth = async ()=>{
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$auth$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AuthAPI"].getCurrentUser();
            if (response.success && response.data) {
                const updatedUser = response.data;
                localStorage.setItem(USER_KEY, JSON.stringify(updatedUser));
                setAuthState((prev)=>({
                        ...prev,
                        user: updatedUser
                    }));
            }
        } catch (error) {
            console.error('Refresh auth error:', error);
            // 如果获取用户信息失败，可能token已过期
            if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2d$client$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ApiClientError"] && __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ErrorHandler"].isAuthError(error)) {
                await logout();
            }
        }
    };
    // 更新用户信息
    const updateUser = (userData)=>{
        setAuthState((prev)=>{
            if (prev.user) {
                const updatedUser = {
                    ...prev.user,
                    ...userData
                };
                localStorage.setItem(USER_KEY, JSON.stringify(updatedUser));
                return {
                    ...prev,
                    user: updatedUser
                };
            }
            return prev;
        });
    };
    const contextValue = {
        ...authState,
        login,
        register,
        logout,
        refreshAuth,
        updateUser
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/AuthContext.tsx",
        lineNumber: 282,
        columnNumber: 5
    }, this);
}
function useAuth() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
}
function useRequireAuth() {
    const { isAuthenticated, isLoading } = useAuth();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isLoading && !isAuthenticated) {
            // 可以在这里重定向到登录页面
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ErrorHandler"].showWarning('请先登录');
        }
    }, [
        isAuthenticated,
        isLoading
    ]);
    return {
        isAuthenticated,
        isLoading
    };
}
const __TURBOPACK__default__export__ = AuthContext;
}),
"[project]/node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
;
else {
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    else {
        if ("TURBOPACK compile-time truthy", 1) {
            if ("TURBOPACK compile-time truthy", 1) {
                module.exports = __turbopack_context__.r("[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)");
            } else //TURBOPACK unreachable
            ;
        } else //TURBOPACK unreachable
        ;
    }
} //# sourceMappingURL=module.compiled.js.map
}}),
"[project]/node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].React; //# sourceMappingURL=react.js.map
}}),
"[project]/node_modules/.pnpm/goober@2.1.16_csstype@3.1.3/node_modules/goober/dist/goober.modern.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "css": ()=>u,
    "extractCss": ()=>r,
    "glob": ()=>b,
    "keyframes": ()=>h,
    "setup": ()=>m,
    "styled": ()=>j
});
let e = {
    data: ""
}, t = (t)=>("TURBOPACK compile-time falsy", 0) ? "TURBOPACK unreachable" : t || e, r = (e)=>{
    let r = t(e), l = r.data;
    return r.data = "", l;
}, l = /(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g, a = /\/\*[^]*?\*\/|  +/g, n = /\n+/g, o = (e, t)=>{
    let r = "", l = "", a = "";
    for(let n in e){
        let c = e[n];
        "@" == n[0] ? "i" == n[1] ? r = n + " " + c + ";" : l += "f" == n[1] ? o(c, n) : n + "{" + o(c, "k" == n[1] ? "" : t) + "}" : "object" == typeof c ? l += o(c, t ? t.replace(/([^,])+/g, (e)=>n.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g, (t)=>/&/.test(t) ? t.replace(/&/g, e) : e ? e + " " + t : t)) : n) : null != c && (n = /^--/.test(n) ? n : n.replace(/[A-Z]/g, "-$&").toLowerCase(), a += o.p ? o.p(n, c) : n + ":" + c + ";");
    }
    return r + (t && a ? t + "{" + a + "}" : a) + l;
}, c = {}, s = (e)=>{
    if ("object" == typeof e) {
        let t = "";
        for(let r in e)t += r + s(e[r]);
        return t;
    }
    return e;
}, i = (e, t, r, i, p)=>{
    let u = s(e), d = c[u] || (c[u] = ((e)=>{
        let t = 0, r = 11;
        for(; t < e.length;)r = 101 * r + e.charCodeAt(t++) >>> 0;
        return "go" + r;
    })(u));
    if (!c[d]) {
        let t = u !== e ? e : ((e)=>{
            let t, r, o = [
                {}
            ];
            for(; t = l.exec(e.replace(a, ""));)t[4] ? o.shift() : t[3] ? (r = t[3].replace(n, " ").trim(), o.unshift(o[0][r] = o[0][r] || {})) : o[0][t[1]] = t[2].replace(n, " ").trim();
            return o[0];
        })(e);
        c[d] = o(p ? {
            ["@keyframes " + d]: t
        } : t, r ? "" : "." + d);
    }
    let f = r && c.g ? c.g : null;
    return r && (c.g = c[d]), ((e, t, r, l)=>{
        l ? t.data = t.data.replace(l, e) : -1 === t.data.indexOf(e) && (t.data = r ? e + t.data : t.data + e);
    })(c[d], t, i, f), d;
}, p = (e, t, r)=>e.reduce((e, l, a)=>{
        let n = t[a];
        if (n && n.call) {
            let e = n(r), t = e && e.props && e.props.className || /^go/.test(e) && e;
            n = t ? "." + t : e && "object" == typeof e ? e.props ? "" : o(e, "") : !1 === e ? "" : e;
        }
        return e + l + (null == n ? "" : n);
    }, "");
function u(e) {
    let r = this || {}, l = e.call ? e(r.p) : e;
    return i(l.unshift ? l.raw ? p(l, [].slice.call(arguments, 1), r.p) : l.reduce((e, t)=>Object.assign(e, t && t.call ? t(r.p) : t), {}) : l, t(r.target), r.g, r.o, r.k);
}
let d, f, g, b = u.bind({
    g: 1
}), h = u.bind({
    k: 1
});
function m(e, t, r, l) {
    o.p = t, d = e, f = r, g = l;
}
function j(e, t) {
    let r = this || {};
    return function() {
        let l = arguments;
        function a(n, o) {
            let c = Object.assign({}, n), s = c.className || a.className;
            r.p = Object.assign({
                theme: f && f()
            }, c), r.o = / *go\d+/.test(s), c.className = u.apply(r, l) + (s ? " " + s : ""), t && (c.ref = o);
            let i = e;
            return e[0] && (i = c.as || e, delete c.as), g && i[0] && g(c), d(i, c);
        }
        return t ? t(a) : a;
    };
}
;
}),
"[project]/node_modules/.pnpm/react-hot-toast@2.5.2_react_4ea650e5376fd58cd959e858ce6285c0/node_modules/react-hot-toast/dist/index.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "CheckmarkIcon": ()=>_,
    "ErrorIcon": ()=>k,
    "LoaderIcon": ()=>V,
    "ToastBar": ()=>C,
    "ToastIcon": ()=>M,
    "Toaster": ()=>Oe,
    "default": ()=>Vt,
    "resolveValue": ()=>f,
    "toast": ()=>c,
    "useToaster": ()=>O,
    "useToasterStore": ()=>D
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/goober@2.1.16_csstype@3.1.3/node_modules/goober/dist/goober.modern.js [app-ssr] (ecmascript)");
"use client";
var W = (e)=>typeof e == "function", f = (e, t)=>W(e) ? e(t) : e;
var F = (()=>{
    let e = 0;
    return ()=>(++e).toString();
})(), A = (()=>{
    let e;
    return ()=>{
        if (e === void 0 && "undefined" < "u") {
            let t = matchMedia("(prefers-reduced-motion: reduce)");
            e = !t || t.matches;
        }
        return e;
    };
})();
;
var Y = 20;
var U = (e, t)=>{
    switch(t.type){
        case 0:
            return {
                ...e,
                toasts: [
                    t.toast,
                    ...e.toasts
                ].slice(0, Y)
            };
        case 1:
            return {
                ...e,
                toasts: e.toasts.map((o)=>o.id === t.toast.id ? {
                        ...o,
                        ...t.toast
                    } : o)
            };
        case 2:
            let { toast: r } = t;
            return U(e, {
                type: e.toasts.find((o)=>o.id === r.id) ? 1 : 0,
                toast: r
            });
        case 3:
            let { toastId: s } = t;
            return {
                ...e,
                toasts: e.toasts.map((o)=>o.id === s || s === void 0 ? {
                        ...o,
                        dismissed: !0,
                        visible: !1
                    } : o)
            };
        case 4:
            return t.toastId === void 0 ? {
                ...e,
                toasts: []
            } : {
                ...e,
                toasts: e.toasts.filter((o)=>o.id !== t.toastId)
            };
        case 5:
            return {
                ...e,
                pausedAt: t.time
            };
        case 6:
            let a = t.time - (e.pausedAt || 0);
            return {
                ...e,
                pausedAt: void 0,
                toasts: e.toasts.map((o)=>({
                        ...o,
                        pauseDuration: o.pauseDuration + a
                    }))
            };
    }
}, P = [], y = {
    toasts: [],
    pausedAt: void 0
}, u = (e)=>{
    y = U(y, e), P.forEach((t)=>{
        t(y);
    });
}, q = {
    blank: 4e3,
    error: 4e3,
    success: 2e3,
    loading: 1 / 0,
    custom: 4e3
}, D = (e = {})=>{
    let [t, r] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(y), s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(y);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>(s.current !== y && r(y), P.push(r), ()=>{
            let o = P.indexOf(r);
            o > -1 && P.splice(o, 1);
        }), []);
    let a = t.toasts.map((o)=>{
        var n, i, p;
        return {
            ...e,
            ...e[o.type],
            ...o,
            removeDelay: o.removeDelay || ((n = e[o.type]) == null ? void 0 : n.removeDelay) || (e == null ? void 0 : e.removeDelay),
            duration: o.duration || ((i = e[o.type]) == null ? void 0 : i.duration) || (e == null ? void 0 : e.duration) || q[o.type],
            style: {
                ...e.style,
                ...(p = e[o.type]) == null ? void 0 : p.style,
                ...o.style
            }
        };
    });
    return {
        ...t,
        toasts: a
    };
};
var J = (e, t = "blank", r)=>({
        createdAt: Date.now(),
        visible: !0,
        dismissed: !1,
        type: t,
        ariaProps: {
            role: "status",
            "aria-live": "polite"
        },
        message: e,
        pauseDuration: 0,
        ...r,
        id: (r == null ? void 0 : r.id) || F()
    }), x = (e)=>(t, r)=>{
        let s = J(t, e, r);
        return u({
            type: 2,
            toast: s
        }), s.id;
    }, c = (e, t)=>x("blank")(e, t);
c.error = x("error");
c.success = x("success");
c.loading = x("loading");
c.custom = x("custom");
c.dismiss = (e)=>{
    u({
        type: 3,
        toastId: e
    });
};
c.remove = (e)=>u({
        type: 4,
        toastId: e
    });
c.promise = (e, t, r)=>{
    let s = c.loading(t.loading, {
        ...r,
        ...r == null ? void 0 : r.loading
    });
    return typeof e == "function" && (e = e()), e.then((a)=>{
        let o = t.success ? f(t.success, a) : void 0;
        return o ? c.success(o, {
            id: s,
            ...r,
            ...r == null ? void 0 : r.success
        }) : c.dismiss(s), a;
    }).catch((a)=>{
        let o = t.error ? f(t.error, a) : void 0;
        o ? c.error(o, {
            id: s,
            ...r,
            ...r == null ? void 0 : r.error
        }) : c.dismiss(s);
    }), e;
};
;
var K = (e, t)=>{
    u({
        type: 1,
        toast: {
            id: e,
            height: t
        }
    });
}, X = ()=>{
    u({
        type: 5,
        time: Date.now()
    });
}, b = new Map, Z = 1e3, ee = (e, t = Z)=>{
    if (b.has(e)) return;
    let r = setTimeout(()=>{
        b.delete(e), u({
            type: 4,
            toastId: e
        });
    }, t);
    b.set(e, r);
}, O = (e)=>{
    let { toasts: t, pausedAt: r } = D(e);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (r) return;
        let o = Date.now(), n = t.map((i)=>{
            if (i.duration === 1 / 0) return;
            let p = (i.duration || 0) + i.pauseDuration - (o - i.createdAt);
            if (p < 0) {
                i.visible && c.dismiss(i.id);
                return;
            }
            return setTimeout(()=>c.dismiss(i.id), p);
        });
        return ()=>{
            n.forEach((i)=>i && clearTimeout(i));
        };
    }, [
        t,
        r
    ]);
    let s = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        r && u({
            type: 6,
            time: Date.now()
        });
    }, [
        r
    ]), a = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((o, n)=>{
        let { reverseOrder: i = !1, gutter: p = 8, defaultPosition: d } = n || {}, h = t.filter((m)=>(m.position || d) === (o.position || d) && m.height), v = h.findIndex((m)=>m.id === o.id), S = h.filter((m, E)=>E < v && m.visible).length;
        return h.filter((m)=>m.visible).slice(...i ? [
            S + 1
        ] : [
            0,
            S
        ]).reduce((m, E)=>m + (E.height || 0) + p, 0);
    }, [
        t
    ]);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        t.forEach((o)=>{
            if (o.dismissed) ee(o.id, o.removeDelay);
            else {
                let n = b.get(o.id);
                n && (clearTimeout(n), b.delete(o.id));
            }
        });
    }, [
        t
    ]), {
        toasts: t,
        handlers: {
            updateHeight: K,
            startPause: X,
            endPause: s,
            calculateOffset: a
        }
    };
};
;
;
;
;
;
var oe = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["keyframes"]`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`, re = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["keyframes"]`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`, se = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["keyframes"]`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`, k = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["styled"])("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${(e)=>e.primary || "#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${oe} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${re} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${(e)=>e.secondary || "#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${se} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`;
;
var ne = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["keyframes"]`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`, V = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["styled"])("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${(e)=>e.secondary || "#e0e0e0"};
  border-right-color: ${(e)=>e.primary || "#616161"};
  animation: ${ne} 1s linear infinite;
`;
;
var pe = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["keyframes"]`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`, de = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["keyframes"]`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`, _ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["styled"])("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${(e)=>e.primary || "#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${pe} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${de} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${(e)=>e.secondary || "#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`;
var ue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["styled"])("div")`
  position: absolute;
`, le = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["styled"])("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`, fe = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["keyframes"]`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`, Te = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["styled"])("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${fe} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`, M = ({ toast: e })=>{
    let { icon: t, type: r, iconTheme: s } = e;
    return t !== void 0 ? typeof t == "string" ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"](Te, null, t) : t : r === "blank" ? null : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"](le, null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"](V, {
        ...s
    }), r !== "loading" && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"](ue, null, r === "error" ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"](k, {
        ...s
    }) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"](_, {
        ...s
    })));
};
var ye = (e)=>`
0% {transform: translate3d(0,${e * -200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`, ge = (e)=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${e * -150}%,-1px) scale(.6); opacity:0;}
`, he = "0%{opacity:0;} 100%{opacity:1;}", xe = "0%{opacity:1;} 100%{opacity:0;}", be = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["styled"])("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`, Se = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["styled"])("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`, Ae = (e, t)=>{
    let s = e.includes("top") ? 1 : -1, [a, o] = A() ? [
        he,
        xe
    ] : [
        ye(s),
        ge(s)
    ];
    return {
        animation: t ? `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["keyframes"])(a)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards` : `${(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["keyframes"])(o)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`
    };
}, C = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"](({ toast: e, position: t, style: r, children: s })=>{
    let a = e.height ? Ae(e.position || t || "top-center", e.visible) : {
        opacity: 0
    }, o = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"](M, {
        toast: e
    }), n = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"](Se, {
        ...e.ariaProps
    }, f(e.message, e));
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"](be, {
        className: e.className,
        style: {
            ...a,
            ...r,
            ...e.style
        }
    }, typeof s == "function" ? s({
        icon: o,
        message: n
    }) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], null, o, n));
});
;
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setup"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"]);
var ve = ({ id: e, className: t, style: r, onHeightUpdate: s, children: a })=>{
    let o = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"]((n)=>{
        if (n) {
            let i = ()=>{
                let p = n.getBoundingClientRect().height;
                s(e, p);
            };
            i(), new MutationObserver(i).observe(n, {
                subtree: !0,
                childList: !0,
                characterData: !0
            });
        }
    }, [
        e,
        s
    ]);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"]("div", {
        ref: o,
        className: t,
        style: r
    }, a);
}, Ee = (e, t)=>{
    let r = e.includes("top"), s = r ? {
        top: 0
    } : {
        bottom: 0
    }, a = e.includes("center") ? {
        justifyContent: "center"
    } : e.includes("right") ? {
        justifyContent: "flex-end"
    } : {};
    return {
        left: 0,
        right: 0,
        display: "flex",
        position: "absolute",
        transition: A() ? void 0 : "all 230ms cubic-bezier(.21,1.02,.73,1)",
        transform: `translateY(${t * (r ? 1 : -1)}px)`,
        ...s,
        ...a
    };
}, De = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$goober$40$2$2e$1$2e$16_csstype$40$3$2e$1$2e$3$2f$node_modules$2f$goober$2f$dist$2f$goober$2e$modern$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["css"]`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`, R = 16, Oe = ({ reverseOrder: e, position: t = "top-center", toastOptions: r, gutter: s, children: a, containerStyle: o, containerClassName: n })=>{
    let { toasts: i, handlers: p } = O(r);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"]("div", {
        id: "_rht_toaster",
        style: {
            position: "fixed",
            zIndex: 9999,
            top: R,
            left: R,
            right: R,
            bottom: R,
            pointerEvents: "none",
            ...o
        },
        className: n,
        onMouseEnter: p.startPause,
        onMouseLeave: p.endPause
    }, i.map((d)=>{
        let h = d.position || t, v = p.calculateOffset(d, {
            reverseOrder: e,
            gutter: s,
            defaultPosition: t
        }), S = Ee(h, v);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"](ve, {
            id: d.id,
            key: d.id,
            onHeightUpdate: p.updateHeight,
            className: d.visible ? De : "",
            style: S
        }, d.type === "custom" ? f(d.message, d) : a ? a(d) : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$4$2e$5_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"](C, {
            toast: d,
            position: h
        }));
    }));
};
var Vt = c;
;
 //# sourceMappingURL=index.mjs.map
}),
"[project]/node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { m: module, e: exports } = __turbopack_context__;
{
module.exports = __turbopack_context__.r("[project]/node_modules/.pnpm/next@15.4.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-ssr] (ecmascript)").vendored['react-ssr'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__9d79f60d._.js.map