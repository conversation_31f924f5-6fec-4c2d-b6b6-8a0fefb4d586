import { MessageCircle } from "lucide-react";
import { But<PERSON> } from "./ui/button";

interface ChatFloatingButtonProps {
  onClick: () => void;
  notificationCount?: number;
  isVisible?: boolean;
}

export function ChatFloatingButton({ 
  onClick, 
  notificationCount = 0, 
  isVisible = true 
}: ChatFloatingButtonProps) {
  if (!isVisible) return null;

  return (
    <div className="fixed bottom-6 right-6 z-[100]">
      <Button
        onClick={onClick}
        className="relative w-14 h-14 rounded-full bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105"
        size="icon"
      >
        <MessageCircle className="h-6 w-6" />
        
        {/* 通知数量角标 */}
        {notificationCount > 0 && (
          <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-white shadow-sm">
            {notificationCount > 9 ? '9+' : notificationCount}
          </div>
        )}
        
        {/* 呼吸动画效果 */}
        {notificationCount > 0 && (
          <div className="absolute inset-0 rounded-full bg-blue-600 animate-ping opacity-20"></div>
        )}
      </Button>
    </div>
  );
}