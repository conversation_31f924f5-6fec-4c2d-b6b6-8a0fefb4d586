import { useState } from "react";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Checkbox } from "./ui/checkbox";
import { Separator } from "./ui/separator";
import { ArrowLeft, ArrowRight, Mail } from "lucide-react";

interface LoginPageProps {
  onBack: () => void;
  onRegisterClick: () => void;
}

export function LoginPage({ onBack, onRegisterClick }: LoginPageProps) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleEmailLogin();
    }
  };

  const handleEmailLogin = async () => {
    if (!email.trim() || !password.trim() || !agreeToTerms) return;
    
    setIsLoading(true);
    // 模拟登录过程
    setTimeout(() => {
      setIsLoading(false);
      console.log("邮箱登录:", email, "密码:", password);
      // 这里可以添加实际的登录逻辑
    }, 1500);
  };

  const handleThirdPartyLogin = (provider: string) => {
    console.log(`使用 ${provider} 登录`);
    // 这里可以添加第三方登录逻辑
  };

  // 支付宝Logo SVG
  const AlipayLogo = () => (
    <svg width="20" height="20" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M1024 561.18c-64.4-24.32-147.2-56.96-251.84-89.28C835.52 357.76 876.8 245.44 876.8 245.44s-334.4 100.48-580.8 319.04c-65.28 57.92-110.08 122.88-110.08 178.24 0 174.72 349.44 245.12 349.44 245.12s-25.6-76.16-25.6-174.08c0-32 7.68-63.36 21.76-92.16 119.04-14.08 257.28-35.84 413.44-65.28 67.84 25.6 130.56 49.92 185.6 71.04C1024 666.24 1024 561.18 1024 561.18z" fill="#1677FF"/>
      <path d="M512 0C229.12 0 0 229.12 0 512s229.12 512 512 512 512-229.12 512-512S794.88 0 512 0zm375.68 683.52c-55.04-21.12-117.76-45.44-185.6-71.04-156.16 29.44-294.4 51.2-413.44 65.28-14.08 28.8-21.76 60.16-21.76 92.16 0 97.92 25.6 174.08 25.6 174.08S140.8 873.6 140.8 698.88c0-55.36 44.8-120.32 110.08-178.24 246.4-218.56 580.8-319.04 580.8-319.04S790.4 313.92 726.08 427.36c104.64 32.32 187.44 64.96 251.84 89.28 0 0 0 105.06-90.24 166.88z" fill="#1677FF"/>
    </svg>
  );

  // 微信Logo SVG
  const WechatLogo = () => (
    <svg width="20" height="20" viewBox="0 0 1024 1024" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M331.52 263.68c21.76 0 36.48 14.72 36.48 36.48s-14.72 36.48-36.48 36.48-43.52-14.72-43.52-36.48 21.76-36.48 43.52-36.48zm168.96 0c21.76 0 36.48 14.72 36.48 36.48s-14.72 36.48-36.48 36.48-43.52-14.72-43.52-36.48 21.76-36.48 43.52-36.48z" fill="#07C160"/>
      <path d="M693.76 428.8c-14.72 0-29.44 14.72-29.44 29.44s14.72 29.44 29.44 29.44 29.44-14.72 29.44-29.44-14.72-29.44-29.44-29.44zm131.84 0c-14.72 0-29.44 14.72-29.44 29.44s14.72 29.44 29.44 29.44 29.44-14.72 29.44-29.44-14.72-29.44-29.44-29.44z" fill="#07C160"/>
      <path d="M512 0C229.12 0 0 229.12 0 512s229.12 512 512 512 512-229.12 512-512S794.88 0 512 0zm-95.36 599.04c-29.44 0-51.2-7.04-80.64-14.72l-80.64 43.52 21.76-73.6c-58.24-43.52-94.72-102.4-94.72-168.96 0-124.16 116.48-219.52 256-219.52 124.16 0 234.24 80.64 256 189.44-7.04-0.64-14.72-0.64-21.76-0.64-124.16 0-219.52 94.72-219.52 212.48 0 21.76 3.84 43.52 10.88 64-15.36 0-30.72-0.64-46.08-0.64zm387.84 124.16l14.72 58.24-58.24-36.48c-21.76 7.04-43.52 14.72-65.28 14.72-124.16 0-219.52-87.68-219.52-197.76s95.36-197.76 219.52-197.76c116.48 0 219.52 87.68 219.52 197.76 0 58.24-29.44 109.44-73.6 146.56z" fill="#07C160"/>
    </svg>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* 返回按钮 */}
        <Button
          onClick={onBack}
          variant="ghost"
          className="mb-8 p-2 hover:bg-slate-100/60 rounded-full transition-colors"
        >
          <ArrowLeft className="h-5 w-5 text-slate-600" />
        </Button>

        {/* 主要内容区域 */}
        <div className="p-8">
          {/* 标题区域 */}
          <div className="text-center mb-8">
            <h1 className="text-slate-900 mb-2">现在登录U-bund</h1>
          </div>

          {/* 邮箱登录表单 */}
          <div className="space-y-6">
            {/* 邮箱输入框 */}
            <div>
              <Input
                type="email"
                placeholder="电子邮件或电话号码"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                onKeyPress={handleKeyPress}
                className="w-full h-12 pl-4 bg-slate-50/80 border-slate-200/60 rounded-xl focus:bg-white focus:border-blue-300/60 transition-all duration-200"
              />
            </div>

            {/* 密码输入框 */}
            <div className="relative">
              <Input
                type="password"
                placeholder="密码"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                onKeyPress={handleKeyPress}
                className="w-full h-12 pl-4 pr-12 bg-slate-50/80 border-slate-200/60 rounded-xl focus:bg-white focus:border-blue-300/60 transition-all duration-200"
              />
              <Button
                onClick={handleEmailLogin}
                disabled={!email.trim() || !password.trim() || !agreeToTerms || isLoading}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 bg-slate-600 hover:bg-slate-700 disabled:bg-slate-300 rounded-full transition-colors"
              >
                {isLoading ? (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                ) : (
                  <ArrowRight className="h-4 w-4 text-white" />
                )}
              </Button>
            </div>

            {/* 分隔线 */}
            <div className="relative">
              <Separator className="bg-slate-200/60" />
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="bg-white px-4 text-sm text-slate-500">或者</span>
              </div>
            </div>

            {/* 第三方登录选项 */}
            <div className="space-y-3">
              <Button
                onClick={() => handleThirdPartyLogin("支付宝")}
                className="w-full h-12 bg-[#1677FF] hover:bg-[#1677FF]/90 border-0 transition-all duration-200 rounded-xl text-white"
              >
                <div className="flex items-center justify-center space-x-3">
                  <AlipayLogo />
                  <span>使用支付宝登录</span>
                </div>
              </Button>

              <Button
                onClick={() => handleThirdPartyLogin("微信")}
                className="w-full h-12 bg-[#07C160] hover:bg-[#07C160]/90 border-0 transition-all duration-200 rounded-xl text-white"
              >
                <div className="flex items-center justify-center space-x-3">
                  <WechatLogo />
                  <span>使用微信登录</span>
                </div>
              </Button>
            </div>

            {/* 注册链接 */}
            <div className="text-center mt-8 pt-6 border-t border-slate-200/60">
              <p className="text-sm text-slate-600">
                没有 U-bund 账户？{" "}
                <Button
                  onClick={onRegisterClick}
                  variant="link"
                  className="text-blue-600 hover:text-blue-700 p-0 h-auto text-sm"
                >
                  创建你的 U-bund 账户 →
                </Button>
              </p>
            </div>

            {/* 忘记密码 */}
            <div className="text-center mt-3">
              <Button
                variant="link"
                className="text-slate-500 hover:text-slate-700 p-0 h-auto text-sm"
              >
                忘记密码？
              </Button>
            </div>

            {/* 勾选区域 - 记住账户和用户协议 */}
            <div className="space-y-3 mt-6 pt-4 border-t border-slate-200/60">
              {/* 记住我的账户 */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="remember"
                  checked={rememberMe}
                  onCheckedChange={(checked) => setRememberMe(checked as boolean)}
                  className="border-slate-300"
                />
                <label
                  htmlFor="remember"
                  className="text-sm text-slate-600 cursor-pointer"
                >
                  记住我的账户
                </label>
              </div>

              {/* 用户协议和隐私协议 */}
              <div className="flex items-start space-x-2">
                <Checkbox
                  id="terms"
                  checked={agreeToTerms}
                  onCheckedChange={(checked) => setAgreeToTerms(checked as boolean)}
                  className="border-slate-300 mt-0.5"
                />
                <label
                  htmlFor="terms"
                  className="text-sm text-slate-600 cursor-pointer leading-relaxed"
                >
                  我已阅读并同意{" "}
                  <Button
                    variant="link"
                    className="text-blue-600 hover:text-blue-700 p-0 h-auto text-sm underline"
                  >
                    用户协议
                  </Button>
                  {" "}和{" "}
                  <Button
                    variant="link"
                    className="text-blue-600 hover:text-blue-700 p-0 h-auto text-sm underline"
                  >
                    隐私政策
                  </Button>
                </label>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}