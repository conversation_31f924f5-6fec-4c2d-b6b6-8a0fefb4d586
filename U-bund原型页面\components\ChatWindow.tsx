import { useState, useRef } from "react";
import { ImageWithFallback } from "./figma/ImageWithFallback";
import { Button } from "./ui/button";
import { 
  X, 
  Minus, 
  Send, 
  Paperclip,
  MoreHorizontal,
  Bell,
  User,
  Circle,
  Image,
  FileText,
  Download,
  Smile
} from "lucide-react";

interface ChatWindowProps {
  isOpen: boolean;
  onClose: () => void;
  onMinimize: () => void;
}

interface Contact {
  id: string;
  name: string;
  avatar?: string;
  type: "official" | "customer";
  lastMessage: string;
  time: string;
  unreadCount: number;
  isOnline?: boolean;
}

interface Message {
  id: string;
  content: string;
  sender: "me" | "other";
  time: string;
  type: "text" | "notification" | "file" | "image" | "emoji";
  fileName?: string;
  fileSize?: string;
  imageUrl?: string;
}

export function ChatWindow({ isOpen, onClose, onMinimize }: ChatWindowProps) {
  const [activeContact, setActiveContact] = useState<string>("official");
  const [messageInput, setMessageInput] = useState("");
  const [showEmojiPanel, setShowEmojiPanel] = useState(false);
  const [messages, setMessages] = useState<Record<string, Message[]>>({
    official: [
      {
        id: "1",
        content: "您的Logo设计订单已完成，请查看并确认验收。",
        sender: "other",
        time: "14:30",
        type: "notification"
      },
      {
        id: "2", 
        content: "您的网站开发项目费用支付成功，项目即将开始。",
        sender: "other",
        time: "13:15",
        type: "notification"
      },
      {
        id: "3",
        content: "恭喜您获得「忠实用户」徽章！",
        sender: "other", 
        time: "昨天",
        type: "notification"
      }
    ],
    customer1: [
      {
        id: "1",
        content: "您好，关于Logo设计的需求我已经收到了",
        sender: "other",
        time: "10:30",
        type: "text"
      },
      {
        id: "2",
        content: "请问您比较偏向什么风格？",
        sender: "other", 
        time: "10:31",
        type: "text"
      },
      {
        id: "3",
        content: "我希望是简约现代的风格，主要用于科技公司",
        sender: "me",
        time: "10:35",
        type: "text"
      },
      {
        id: "4",
        content: "好的，我明天给您发初稿",
        sender: "other",
        time: "10:40", 
        type: "text"
      },
      {
        id: "5",
        content: "👍",
        sender: "me",
        time: "10:41", 
        type: "emoji"
      }
    ],
    customer2: [
      {
        id: "1",
        content: "网站开发进度如何？",
        sender: "other",
        time: "11:20",
        type: "text"
      },
      {
        id: "2",
        content: "目前前端页面已经完成80%，预计明天可以给您看demo",
        sender: "me",
        time: "11:25",
        type: "text"
      }
    ]
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  // 表情包数据
  const emojiCategories = [
    {
      name: "笑脸",
      emojis: ["😀", "😃", "😄", "😁", "😆", "😅", "😂", "🤣", "😊", "😇", "🙂", "🙃", "😉", "😌", "😍", "🥰", "😘", "😗", "😙", "😚", "😋", "😛", "😝", "😜", "🤪", "🤨", "🧐", "🤓", "😎", "🤩", "🥳", "😏", "😒", "😞", "😔", "😟", "😕", "🙁", "☹️", "😣", "😖", "😫", "😩", "🥺", "😢", "😭", "😤", "😠", "😡", "🤬", "🤯", "😳", "🥵", "🥶", "😱", "😨", "😰", "😥", "😓", "🤗", "🤔", "🤭", "🤫", "🤥", "😶", "😐", "😑", "😬", "🙄", "😯", "😦", "😧", "😮", "😲", "🥱", "😴", "🤤", "😪", "😵", "🤐", "🥴", "🤢", "🤮", "🤧", "😷", "🤒", "🤕"]
    },
    {
      name: "手势",
      emojis: ["👋", "🤚", "🖐️", "✋", "🖖", "👌", "🤏", "✌️", "🤞", "🤟", "🤘", "🤙", "👈", "👉", "👆", "🖕", "👇", "☝️", "👍", "👎", "✊", "👊", "🤛", "🤜", "👏", "🙌", "👐", "🤝", "🙏", "✍️", "💅", "🤳", "💪", "🦾", "🦵", "🦿"]
    },
    {
      name: "心情",
      emojis: ["❤️", "🧡", "💛", "💚", "💙", "💜", "🖤", "🤍", "🤎", "💔", "❣️", "💕", "💞", "💓", "💗", "💖", "💘", "💝", "💟", "💌", "💯", "💢", "💥", "💫", "💦", "💨", "🕳️", "💣", "💬", "👁️‍🗨️", "🗨️", "🗯️", "💭", "💤"]
    },
    {
      name: "物品",
      emojis: ["📱", "📲", "💻", "⌨️", "🖥️", "🖨️", "🖱️", "🖲️", "💽", "💾", "💿", "📀", "📼", "📷", "📸", "📹", "🎥", "📽️", "🎞️", "📞", "☎️", "📟", "📠", "📺", "📻", "🎙️", "🎚️", "🎛️", "🧭", "⏱️", "⏲️", "⏰", "🕰️", "⌛", "⏳", "📡", "🔋", "🔌", "💡", "🔦", "🕯️", "🪔", "🧯", "🛢️", "💸", "💵", "💴", "💶", "💷", "💰", "💳", "💎", "⚖️"]
    }
  ];

  const contacts: Contact[] = [
    {
      id: "official",
      name: "官方通知",
      type: "official",
      lastMessage: "您的Logo设计订单已完成",
      time: "2分钟前",
      unreadCount: 3
    },
    {
      id: "customer1",
      name: "张设计师",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",
      type: "customer",
      lastMessage: messages.customer1?.[messages.customer1.length - 1]?.content || "👍",
      time: "1小时前",
      unreadCount: 1,
      isOnline: true
    },
    {
      id: "customer2", 
      name: "李工程师",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",
      type: "customer",
      lastMessage: messages.customer2?.[messages.customer2.length - 1]?.content || "网站开发进度如何？",
      time: "3小时前",
      unreadCount: 0,
      isOnline: false
    }
  ];

  const generateMessageId = () => {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  };

  const getCurrentTime = () => {
    const now = new Date();
    return now.getHours().toString().padStart(2, '0') + ':' + 
           now.getMinutes().toString().padStart(2, '0');
  };

  const handleSendMessage = () => {
    if (messageInput.trim() && activeContact !== "official") {
      const newMessage: Message = {
        id: generateMessageId(),
        content: messageInput.trim(),
        sender: "me",
        time: getCurrentTime(),
        type: "text"
      };

      setMessages(prev => ({
        ...prev,
        [activeContact]: [...(prev[activeContact] || []), newMessage]
      }));

      setMessageInput("");

      // 模拟对方回复（延迟1-3秒）
      setTimeout(() => {
        const replies = [
          "好的，我知道了",
          "没问题，稍等一下",
          "收到，我马上处理",
          "了解，谢谢您的反馈",
          "好的，我会尽快回复您"
        ];
        const randomReply = replies[Math.floor(Math.random() * replies.length)];
        
        const replyMessage: Message = {
          id: generateMessageId(),
          content: randomReply,
          sender: "other",
          time: getCurrentTime(),
          type: "text"
        };

        setMessages(prev => ({
          ...prev,
          [activeContact]: [...(prev[activeContact] || []), replyMessage]
        }));
      }, Math.random() * 2000 + 1000);
    }
  };

  const handleEmojiSelect = (emoji: string) => {
    if (activeContact !== "official") {
      const newMessage: Message = {
        id: generateMessageId(),
        content: emoji,
        sender: "me",
        time: getCurrentTime(),
        type: "emoji"
      };

      setMessages(prev => ({
        ...prev,
        [activeContact]: [...(prev[activeContact] || []), newMessage]
      }));

      setShowEmojiPanel(false);

      // 模拟对方回复表情
      setTimeout(() => {
        const emojiReplies = ["😊", "👍", "😄", "💖", "🙌", "😘"];
        const randomEmoji = emojiReplies[Math.floor(Math.random() * emojiReplies.length)];
        
        const replyMessage: Message = {
          id: generateMessageId(),
          content: randomEmoji,
          sender: "other",
          time: getCurrentTime(),
          type: "emoji"
        };

        setMessages(prev => ({
          ...prev,
          [activeContact]: [...(prev[activeContact] || []), replyMessage]
        }));
      }, Math.random() * 1000 + 500);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && activeContact !== "official") {
      const isImage = file.type.startsWith('image/');
      const fileSize = file.size < 1024 * 1024 
        ? `${Math.round(file.size / 1024)}KB` 
        : `${(file.size / (1024 * 1024)).toFixed(1)}MB`;

      const newMessage: Message = {
        id: generateMessageId(),
        content: isImage ? "发送了一张图片" : "发送了一个文件",
        sender: "me",
        time: getCurrentTime(),
        type: isImage ? "image" : "file",
        fileName: file.name,
        fileSize: fileSize,
        imageUrl: isImage ? URL.createObjectURL(file) : undefined
      };

      setMessages(prev => ({
        ...prev,
        [activeContact]: [...(prev[activeContact] || []), newMessage]
      }));

      // 清空文件输入
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      // 模拟对方回复
      setTimeout(() => {
        const reply = isImage ? "图片很棒！" : "文件收到了，谢谢";
        const replyMessage: Message = {
          id: generateMessageId(),
          content: reply,
          sender: "other",
          time: getCurrentTime(),
          type: "text"
        };

        setMessages(prev => ({
          ...prev,
          [activeContact]: [...(prev[activeContact] || []), replyMessage]
        }));
      }, 1500);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const renderMessage = (message: Message) => {
    const isMe = message.sender === "me";
    
    if (message.type === "emoji") {
      return (
        <div className={`flex ${isMe ? "justify-end" : "justify-start"}`}>
          <div className={`px-3 py-2 rounded-lg ${
            isMe ? "bg-blue-500" : "bg-slate-100"
          }`}>
            <div className="text-2xl">{message.content}</div>
            <p className={`text-xs mt-1 ${
              isMe ? "text-blue-100" : "text-slate-500"
            }`}>
              {message.time}
            </p>
          </div>
        </div>
      );
    }
    
    if (message.type === "file") {
      return (
        <div className={`flex ${isMe ? "justify-end" : "justify-start"}`}>
          <div className={`max-w-[80%] p-3 rounded-lg ${
            isMe ? "bg-blue-500 text-white" : "bg-slate-100 text-slate-800"
          }`}>
            <div className="flex items-center gap-2 mb-1">
              <FileText className="h-4 w-4" />
              <span className="text-sm font-medium">{message.fileName}</span>
            </div>
            <div className="flex items-center justify-between gap-2">
              <span className="text-xs opacity-70">{message.fileSize}</span>
              <Button
                variant="ghost"
                size="sm"
                className={`h-6 w-6 p-0 ${isMe ? "hover:bg-blue-600" : "hover:bg-slate-200"}`}
              >
                <Download className="h-3 w-3" />
              </Button>
            </div>
            <p className={`text-xs mt-1 ${
              isMe ? "text-blue-100" : "text-slate-500"
            }`}>
              {message.time}
            </p>
          </div>
        </div>
      );
    }

    if (message.type === "image") {
      return (
        <div className={`flex ${isMe ? "justify-end" : "justify-start"}`}>
          <div className={`max-w-[80%] p-2 rounded-lg ${
            isMe ? "bg-blue-500 text-white" : "bg-slate-100 text-slate-800"
          }`}>
            {message.imageUrl && (
              <img 
                src={message.imageUrl}
                alt={message.fileName}
                className="max-w-full h-auto rounded mb-1 max-h-48 object-cover"
              />
            )}
            <div className="flex items-center justify-between gap-2">
              <span className="text-xs opacity-70">{message.fileName}</span>
              <span className="text-xs opacity-70">{message.fileSize}</span>
            </div>
            <p className={`text-xs mt-1 ${
              isMe ? "text-blue-100" : "text-slate-500"
            }`}>
              {message.time}
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className={`flex ${isMe ? "justify-end" : "justify-start"}`}>
        <div className={`max-w-[80%] px-3 py-2 rounded-lg text-sm ${
          isMe
            ? "bg-blue-500 text-white"
            : message.type === "notification"
            ? "bg-orange-50 text-orange-800 border border-orange-200"
            : "bg-slate-100 text-slate-800"
        }`}>
          <p className="leading-relaxed">{message.content}</p>
          <p className={`text-xs mt-1 ${
            isMe 
              ? "text-blue-100" 
              : message.type === "notification"
              ? "text-orange-600"
              : "text-slate-500"
          }`}>
            {message.time}
          </p>
        </div>
      </div>
    );
  };

  if (!isOpen) return null;

  const currentContact = contacts.find(c => c.id === activeContact);
  const currentMessages = messages[activeContact] || [];

  return (
    <div className="fixed bottom-4 right-4 w-[640px] h-[768px] bg-white border border-slate-200 rounded-lg shadow-2xl z-[100] flex flex-col overflow-hidden">
      {/* 顶部标题栏 */}
      <div className="bg-slate-50/80 border-b border-slate-200 px-6 py-4 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Bell className="h-5 w-5 text-slate-600" />
          <span className="text-slate-900 font-medium">消息通知</span>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onMinimize}
            className="h-8 w-8 p-0 hover:bg-slate-200/60"
          >
            <Minus className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0 hover:bg-slate-200/60"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="flex flex-1 overflow-hidden">
        {/* 左侧联系人列表 */}
        <div className="w-48 bg-slate-50/50 border-r border-slate-200 flex flex-col">
          {contacts.map((contact) => (
            <div
              key={contact.id}
              onClick={() => setActiveContact(contact.id)}
              className={`p-4 cursor-pointer transition-colors duration-200 border-b border-slate-200/60 ${
                activeContact === contact.id
                  ? 'bg-blue-50 border-r-2 border-blue-500'
                  : 'hover:bg-slate-100/60'
              }`}
            >
              <div className="flex items-center gap-3">
                {contact.type === "official" ? (
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <Bell className="h-5 w-5 text-blue-600" />
                  </div>
                ) : (
                  <div className="relative">
                    <ImageWithFallback
                      src={contact.avatar || ""}
                      alt={contact.name}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                    {contact.isOnline && (
                      <div className="absolute -top-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                    )}
                  </div>
                )}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-medium text-slate-900 truncate">
                      {contact.name}
                    </span>
                    {contact.unreadCount > 0 && (
                      <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs font-medium">
                          {contact.unreadCount > 9 ? '9+' : contact.unreadCount}
                        </span>
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-slate-500 truncate">
                    {contact.lastMessage}
                  </p>
                  <p className="text-xs text-slate-400 mt-0.5">
                    {contact.time}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 右侧对话区域 */}
        <div className="flex-1 flex flex-col relative">
          {/* 对话标题 */}
          <div className="px-6 py-4 border-b border-slate-200 bg-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {currentContact?.type === "official" ? (
                  <Bell className="h-5 w-5 text-blue-600" />
                ) : (
                  <User className="h-5 w-5 text-slate-600" />
                )}
                <span className="font-medium text-slate-900">
                  {currentContact?.name}
                </span>
                {currentContact?.isOnline && (
                  <div className="flex items-center gap-1">
                    <Circle className="h-2 w-2 text-green-500 fill-current" />
                    <span className="text-xs text-green-600">在线</span>
                  </div>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 hover:bg-slate-100"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* 消息列表 */}
          <div className="flex-1 p-6 overflow-y-auto space-y-4">
            {currentMessages.map((message) => (
              <div key={message.id}>
                {renderMessage(message)}
              </div>
            ))}
          </div>

          {/* 表情面板 */}
          {showEmojiPanel && (
            <div className="absolute bottom-20 left-4 right-4 bg-white border border-slate-200 rounded-lg shadow-lg p-4 max-h-64 overflow-y-auto z-10">
              <div className="space-y-4">
                {emojiCategories.map((category, categoryIndex) => (
                  <div key={categoryIndex}>
                    <h4 className="text-xs font-medium text-slate-600 mb-2">{category.name}</h4>
                    <div className="grid grid-cols-8 gap-2">
                      {category.emojis.map((emoji, emojiIndex) => (
                        <Button
                          key={emojiIndex}
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEmojiSelect(emoji)}
                          className="h-8 w-8 p-0 text-lg hover:bg-slate-100 rounded"
                        >
                          {emoji}
                        </Button>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 输入框 */}
          {currentContact?.type !== "official" && (
            <div className="border-t border-slate-200 p-4">
              <div className="flex items-end gap-3">
                <input
                  ref={fileInputRef}
                  type="file"
                  onChange={handleFileUpload}
                  className="hidden"
                  accept="image/*,.pdf,.doc,.docx,.txt"
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  className="h-10 w-10 p-0 hover:bg-slate-100 flex-shrink-0"
                >
                  <Paperclip className="h-5 w-5 text-slate-500" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowEmojiPanel(!showEmojiPanel)}
                  className={`h-10 w-10 p-0 flex-shrink-0 ${
                    showEmojiPanel ? 'bg-blue-50 text-blue-600' : 'hover:bg-slate-100 text-slate-500'
                  }`}
                >
                  <Smile className="h-5 w-5" />
                </Button>
                <div className="flex-1">
                  <textarea
                    value={messageInput}
                    onChange={(e) => setMessageInput(e.target.value)}
                    onKeyPress={handleKeyPress}
                    placeholder="输入消息... (按Enter发送)"
                    className="w-full px-4 py-3 text-sm border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none min-h-[44px] max-h-32"
                    rows={1}
                  />
                </div>
                <Button
                  size="sm"
                  onClick={handleSendMessage}
                  disabled={!messageInput.trim()}
                  className="h-10 w-10 p-0 bg-blue-500 hover:bg-blue-600 disabled:opacity-50 flex-shrink-0"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}