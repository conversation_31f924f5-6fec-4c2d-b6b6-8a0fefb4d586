import { Clock } from "lucide-react";

interface ProgressStep {
  id: string;
  label: string;
  description: string;
  status: "completed" | "current" | "pending";
  completedAt?: string;
}

interface WorkProgressBarProps {
  currentStep: number;
  steps: ProgressStep[];
}

export function WorkProgressBar({ currentStep, steps }: WorkProgressBarProps) {
  const currentStepData = steps.find(step => step.status === "current");
  const completedSteps = steps.filter(step => step.status === "completed");
  const allCompleted = steps.every(step => step.status === "completed");

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* 统一的标题设计 */}
      <h3 className="text-slate-900 flex items-center gap-2">
        <Clock className="h-4 w-4" />
        工作进度
      </h3>

      {/* Apple风格进度条保持原样 */}
      <div className="relative">
        {/* 步骤容器 */}
        <div className="flex justify-between items-start relative">
          {/* 背景连接线 - 带断开效果 */}
          <div className="absolute top-0 left-0 right-0 h-1 flex">
            {steps.map((_, index) => {
              if (index === steps.length - 1) return null;
              
              const isCompleted = currentStep > index;
              const segmentWidth = `${100 / (steps.length - 1)}%`;
              
              return (
                <div key={`segment-${index}`} className="flex-1 flex items-center" style={{ width: segmentWidth }}>
                  <div 
                    className={`h-1 flex-1 transition-all duration-500 ${
                      isCompleted 
                        ? 'bg-gradient-to-r from-blue-500 to-blue-600' 
                        : 'bg-slate-200'
                    }`}
                  />
                  {/* 分段间隔 */}
                  <div className="w-1 h-1 bg-white" />
                </div>
              );
            })}
          </div>

          {/* 步骤节点 */}
          {steps.map((step, index) => {
            const isCompleted = step.status === "completed";
            const isCurrent = step.status === "current";
            const isPending = step.status === "pending";
            
            return (
              <div key={step.id} className="flex flex-col items-center relative z-10">
                {/* 节点圆圈 */}
                <div 
                  className={`w-6 h-6 rounded-full border-2 transition-all duration-500 flex items-center justify-center ${
                    isCompleted 
                      ? 'bg-blue-500 border-blue-500' 
                      : isCurrent 
                        ? 'bg-white border-blue-500 ring-4 ring-blue-100' 
                        : 'bg-white border-slate-300'
                  }`}
                >
                  {isCompleted && (
                    <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                  {isCurrent && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                  )}
                </div>
                
                {/* 步骤标签 */}
                <div className="mt-3 text-center max-w-20">
                  <div 
                    className={`text-sm font-medium transition-colors duration-300 ${
                      isCompleted || isCurrent 
                        ? 'text-slate-900' 
                        : 'text-slate-500'
                    }`}
                  >
                    {step.label}
                  </div>
                  {step.completedAt && (
                    <div className="text-xs text-slate-500 mt-1">
                      {formatDate(step.completedAt)}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* 当前步骤详情 */}
      {currentStepData && (
        <div className="bg-blue-50/50 border border-blue-200/60 rounded-xl p-4">
          <div className="flex items-start gap-3">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 animate-pulse" />
            <div className="flex-1">
              <h4 className="text-slate-900 font-medium mb-1">
                当前阶段：{currentStepData.label}
              </h4>
              <p className="text-slate-600 text-sm">
                {currentStepData.description}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 完成状态 */}
      {allCompleted && (
        <div className="bg-green-50/50 border border-green-200/60 rounded-xl p-4">
          <div className="flex items-center gap-3">
            <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
              <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <div>
              <h4 className="text-green-900 font-medium">项目已完成</h4>
              <p className="text-green-700 text-sm">所有工作阶段已顺利完成</p>
            </div>
          </div>
        </div>
      )}

      {/* 进度统计 */}
      <div className="flex items-center justify-between text-sm text-slate-600 pt-2 border-t border-slate-200/60">
        <span>已完成 {completedSteps.length} / {steps.length} 个阶段</span>
        <span>{Math.round((completedSteps.length / steps.length) * 100)}% 完成</span>
      </div>
    </div>
  );
}
