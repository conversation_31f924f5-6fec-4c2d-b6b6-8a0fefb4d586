(()=>{var a={};a.id=778,a.ids=[778],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3293:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(1209).A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3535:(a,b,c)=>{"use strict";c.d(b,{p:()=>f});var d=c(568);c(5561);var e=c(2384);function f({className:a,type:b,...c}){return(0,d.jsx)("input",{type:b,"data-slot":"input",className:(0,e.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}},3873:a=>{"use strict";a.exports=require("path")},4683:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(4962),e=c(3253),f=c(6443),g=c(9500),h=c(9606),i=c(6888),j=c(6327),k=c(2650),l=c(7024),m=c(4618),n=c(8421),o=c(5365),p=c(9310),q=c(261),r=c(6429),s=c(3282),t=c(6713),u=c(6196),v=c(7621),w=c(2873),x=c(4894),y=c(4690),z=c(4294),A=c(6439),B=c(3866),C=c.n(B),D=c(3362),E=c(3435),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["orders",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,6294)),"E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\orders\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,2399))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,8275)),"E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,3866,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,3116,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,7189,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,168,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,2399))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\orders\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/orders/page",pathname:"/orders",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/orders/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},5684:(a,b,c)=>{Promise.resolve().then(c.bind(c,6294))},6294:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(4538).registerClientReference)(function(){throw Error("Attempted to call the default export of \"E:\\\\7mouthMission\\\\1500\\\\my-nextjs-app\\\\src\\\\app\\\\orders\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"E:\\7mouthMission\\1500\\my-nextjs-app\\src\\app\\orders\\page.tsx","default")},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6533:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>B});var d=c(568),e=c(5561),f=c(8674),g=c(9409),h=c(8535),i=c(8558),j=c(820),k=c(5207);let l=(0,c(1209).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);var m=c(3293),n=c(6845),o=c(7624),p=c(9069);function q({order:a,onViewDetail:b}){let c=a=>new Date(a).toLocaleDateString("zh-CN",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),e=a.workProgress.steps[a.workProgress.currentStep]||a.workProgress.steps[0];return(0,d.jsx)(g.Zp,{className:"group bg-white/80 backdrop-blur-sm border-slate-200/60 hover:shadow-lg hover:shadow-slate-200/40 transition-all duration-300 cursor-pointer",children:(0,d.jsxs)(g.Wu,{className:"p-6",onClick:b,children:[(0,d.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-2",children:[(0,d.jsxs)("h3",{className:"font-semibold text-slate-900",children:["订单号: ",a.orderNumber]}),(0,d.jsx)(i.E,{className:`text-xs px-2 py-1 ${(a=>{switch(a){case"delivered":return"bg-green-100 text-green-800 border-green-200";case"shipped":return"bg-blue-100 text-blue-800 border-blue-200";case"processing":return"bg-orange-100 text-orange-800 border-orange-200";case"pending":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"cancelled":return"bg-red-100 text-red-800 border-red-200";case"aftersale":return"bg-purple-100 text-purple-800 border-purple-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(a.status)}`,children:(a=>{switch(a){case"delivered":return"已完成";case"shipped":return"待交付";case"processing":return"进行中";case"pending":return"待处理";case"cancelled":return"已取消";case"aftersale":return"售后中";default:return"未知状态"}})(a.status)})]}),(0,d.jsxs)("div",{className:"flex items-center gap-4 text-sm text-slate-600",children:[(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(k.A,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:c(a.date)})]}),a.estimatedCompletionTime&&(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(l,{className:"h-4 w-4"}),(0,d.jsxs)("span",{children:["预计 ",c(a.estimatedCompletionTime)]})]})]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("div",{className:"text-2xl font-bold text-slate-900 mb-1",children:["\xa5",a.total.toFixed(2)]}),(0,d.jsx)(m.A,{className:"h-5 w-5 text-slate-400 group-hover:text-slate-600 transition-colors ml-auto"})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-4 p-3 bg-slate-50/80 rounded-lg",children:[(0,d.jsxs)(j.eu,{className:"h-10 w-10",children:[(0,d.jsx)(j.BK,{src:a.merchant.avatar}),(0,d.jsx)(j.q5,{children:a.merchant.name[0]})]}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,d.jsx)("span",{className:"font-medium text-slate-900",children:a.merchant.name}),(0,d.jsx)("div",{className:`w-2 h-2 rounded-full ${a.merchant.isOnline?"bg-green-500":"bg-slate-400"}`})]}),(0,d.jsxs)("div",{className:"flex items-center gap-3 text-sm text-slate-600",children:[(0,d.jsxs)("div",{className:"flex items-center gap-1",children:[(0,d.jsx)(n.A,{className:"h-3 w-3 text-yellow-500 fill-current"}),(0,d.jsx)("span",{children:a.merchant.rating})]}),(0,d.jsx)("span",{children:a.merchant.phone})]})]}),(0,d.jsxs)(h.$,{variant:"outline",size:"sm",className:"flex items-center gap-1",onClick:a=>{a.stopPropagation(),console.log("联系商家")},children:[(0,d.jsx)(o.A,{className:"h-4 w-4"}),"联系"]})]}),(0,d.jsx)("div",{className:"space-y-3 mb-4",children:a.items.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("img",{src:a.image,alt:a.name,className:"w-16 h-16 object-cover rounded-lg bg-slate-100 border border-slate-200/60"}),a.quantity>1&&(0,d.jsx)(i.E,{className:"absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-blue-600",children:a.quantity})]}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsx)("h4",{className:"font-medium text-slate-900 line-clamp-1 mb-1",children:a.name}),a.specifications&&a.specifications.length>0&&(0,d.jsxs)("div",{className:"flex flex-wrap gap-1",children:[a.specifications.slice(0,2).map((a,b)=>(0,d.jsx)(i.E,{variant:"secondary",className:"text-xs px-2 py-0.5",children:a},b)),a.specifications.length>2&&(0,d.jsxs)(i.E,{variant:"secondary",className:"text-xs px-2 py-0.5",children:["+",a.specifications.length-2]})]})]}),(0,d.jsx)("div",{className:"text-right",children:(0,d.jsxs)("div",{className:"font-semibold text-slate-900",children:["\xa5",a.price.toFixed(2)]})})]},a.id))}),"processing"===a.status&&e&&(0,d.jsxs)("div",{className:"bg-blue-50/80 rounded-lg p-3",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 text-blue-600"}),(0,d.jsx)("span",{className:"text-sm font-medium text-blue-900",children:"当前进度"})]}),(0,d.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,d.jsx)("span",{className:"font-medium",children:e.label}),(0,d.jsx)("span",{className:"text-blue-600 ml-2",children:e.description})]}),(0,d.jsxs)("div",{className:"mt-3",children:[(0,d.jsxs)("div",{className:"flex justify-between text-xs text-blue-600 mb-1",children:[(0,d.jsx)("span",{children:"进度"}),(0,d.jsxs)("span",{children:[Math.round((a.workProgress.currentStep+1)/a.workProgress.steps.length*100),"%"]})]}),(0,d.jsx)("div",{className:"w-full bg-blue-200/50 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${(a.workProgress.currentStep+1)/a.workProgress.steps.length*100}%`}})})]})]}),"delivered"===a.status&&a.completedAt&&(0,d.jsx)("div",{className:"bg-green-50/80 rounded-lg p-3",children:(0,d.jsxs)("div",{className:"flex items-center gap-2 text-sm text-green-800",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 text-green-600"}),(0,d.jsxs)("span",{children:["已于 ",c(a.completedAt)," 完成交付"]})]})}),"shipped"===a.status&&a.trackingNumber&&(0,d.jsx)("div",{className:"bg-blue-50/80 rounded-lg p-3",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2 text-sm text-blue-800",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 text-blue-600"}),(0,d.jsx)("span",{children:"等待交付确认"})]}),(0,d.jsx)(i.E,{variant:"outline",className:"text-xs",children:a.trackingNumber})]})})]})})}function r({activeStatus:a,onStatusChange:b,statusCounts:c}){let e=[{id:"all",label:"全部",count:c.total},{id:"pending",label:"待处理",count:c.pending},{id:"processing",label:"进行中",count:c.processing},{id:"shipped",label:"待交付",count:c.shipped},{id:"delivered",label:"已完成",count:c.delivered},{id:"cancelled",label:"已取消",count:c.cancelled},{id:"aftersale",label:"售后",count:c.aftersale}];return(0,d.jsx)("div",{className:"flex items-center gap-2 overflow-x-auto pb-2",children:e.map(c=>(0,d.jsxs)(h.$,{variant:a===c.id?"default":"ghost",size:"sm",onClick:()=>b(c.id),className:`flex items-center gap-2 whitespace-nowrap ${a===c.id?"bg-blue-600 text-white shadow-sm":"text-slate-600 hover:text-slate-900 hover:bg-slate-100"}`,children:[(0,d.jsx)("span",{children:c.label}),c.count>0&&(0,d.jsx)(i.E,{variant:"secondary",className:`text-xs px-1.5 py-0.5 ${a===c.id?"bg-white/20 text-white border-white/20":"bg-slate-200 text-slate-700"}`,children:c.count})]},c.id))})}var s=c(3535),t=c(9459),u=c(1469),v=c(9425);function w({searchTerm:a,onSearchChange:b,statusFilter:c,onStatusFilterChange:e,dateFilter:f,onDateFilterChange:g}){let i=a||"all"!==c||"all"!==f;return(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center",children:[(0,d.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,d.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,d.jsx)(s.p,{type:"text",placeholder:"搜索订单号、商品名称或商家...",value:a,onChange:a=>b(a.target.value),className:"pl-10 pr-4 bg-white/80 border-slate-200/60 focus:border-blue-300 focus:ring-blue-500/20"}),a&&(0,d.jsx)(h.$,{variant:"ghost",size:"sm",onClick:()=>b(""),className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-slate-100",children:(0,d.jsx)(v.A,{className:"h-4 w-4"})})]}),(0,d.jsxs)(t.l6,{value:c,onValueChange:e,children:[(0,d.jsx)(t.bq,{className:"w-full sm:w-[140px] bg-white/80 border-slate-200/60",children:(0,d.jsx)(t.yv,{placeholder:"订单状态"})}),(0,d.jsxs)(t.gC,{children:[(0,d.jsx)(t.eb,{value:"all",children:"全部状态"}),(0,d.jsx)(t.eb,{value:"pending",children:"待处理"}),(0,d.jsx)(t.eb,{value:"processing",children:"进行中"}),(0,d.jsx)(t.eb,{value:"shipped",children:"待交付"}),(0,d.jsx)(t.eb,{value:"delivered",children:"已完成"}),(0,d.jsx)(t.eb,{value:"cancelled",children:"已取消"}),(0,d.jsx)(t.eb,{value:"aftersale",children:"售后中"})]})]}),(0,d.jsxs)(t.l6,{value:f,onValueChange:g,children:[(0,d.jsx)(t.bq,{className:"w-full sm:w-[140px] bg-white/80 border-slate-200/60",children:(0,d.jsx)(t.yv,{placeholder:"时间范围"})}),(0,d.jsxs)(t.gC,{children:[(0,d.jsx)(t.eb,{value:"all",children:"全部时间"}),(0,d.jsx)(t.eb,{value:"recent",children:"最近30天"}),(0,d.jsx)(t.eb,{value:"quarter",children:"最近3个月"}),(0,d.jsx)(t.eb,{value:"year",children:"最近一年"})]})]}),i&&(0,d.jsxs)(h.$,{variant:"outline",size:"sm",onClick:()=>{b(""),e("all"),g("all")},className:"flex items-center gap-2 whitespace-nowrap",children:[(0,d.jsx)(v.A,{className:"h-4 w-4"}),"清除筛选"]})]})}var x=c(2890),y=c(6636);let z=[{id:"1",orderNumber:"ORD202501290001",date:"2025-01-28",status:"delivered",total:8999,shippingAddress:"",completedAt:"2025-01-29",estimatedCompletionTime:void 0,paymentMethod:"支付宝",shippingFee:0,discountAmount:0,workProgress:{currentStep:3,steps:[{id:"1",label:"需求对接",description:"确认品牌设计需求和视觉方向",status:"completed",completedAt:"2025-01-28T10:30:00"},{id:"2",label:"设计制作",description:"Logo创意设计和方案制作",status:"completed",completedAt:"2025-01-28T16:45:00"},{id:"3",label:"修改确认",description:"客户反馈修改和最终确认",status:"completed",completedAt:"2025-01-29T09:15:00"},{id:"4",label:"项目交付",description:"设计文件输出和素材打包",status:"completed",completedAt:"2025-01-29T11:00:00"}]},merchant:{id:"merchant_1",name:"墨刻品牌设计工作室",avatar:"https://images.unsplash.com/photo-1472214103451-9374bd1c798e?w=100&h=100&fit=crop&crop=faces",isOnline:!0,phone:"************",rating:4.9},items:[{id:"1",name:"企业品牌Logo设计 + VI视觉识别系统",image:"https://images.unsplash.com/photo-1634942537034-2531766767d1?w=400&h=400&fit=crop",quantity:1,price:8999,specifications:["原创设计","多方案对比","全套VI手册","商用授权"]}]},{id:"2",orderNumber:"ORD202501280002",date:"2025-01-27",status:"shipped",total:15999,shippingAddress:"",completedAt:void 0,estimatedCompletionTime:"2025-01-31",paymentMethod:"微信支付",shippingFee:0,discountAmount:1e3,trackingNumber:"SF1234567890123",workProgress:{currentStep:2,steps:[{id:"1",label:"需求分析",description:"分析网站功能需求和技术架构",status:"completed",completedAt:"2025-01-27T14:20:00"},{id:"2",label:"开发制作",description:"前后端开发和功能实现",status:"completed",completedAt:"2025-01-28T20:30:00"},{id:"3",label:"测试优化",description:"功能测试和性能优化",status:"current"},{id:"4",label:"上线部署",description:"域名配置和服务器部署",status:"pending"}]},merchant:{id:"merchant_2",name:"码猿科技开发团队",avatar:"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",isOnline:!1,phone:"021-12345678",rating:4.7},items:[{id:"2",name:"企业官网定制开发（响应式）",image:"https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=400&fit=crop",quantity:1,price:12999,specifications:["React技术栈","响应式设计","SEO优化","后台管理系统"]},{id:"3",name:"域名注册和SSL证书配置",image:"https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=400&h=400&fit=crop",quantity:1,price:3e3,specifications:[".com域名","SSL证书","DNS配置","一年服务"]}]}];var A=c(9270);function B(){let a=(0,A.useRouter)(),[b,c]=(0,e.useState)(""),[g,h]=(0,e.useState)("all"),[i,j]=(0,e.useState)("all"),[k,l]=(0,e.useState)(!1),[m]=(0,e.useState)(3),n=()=>{l(!0)},o=(0,e.useMemo)(()=>{let a,c,d;return((a,b)=>{if("all"===b)return a;let c=new Date;return a.filter(a=>{let d=new Date(a.date),e=Math.ceil((c.getTime()-d.getTime())/864e5);switch(b){case"recent":return e<=30;case"quarter":return e<=90;case"year":return e<=365;default:return!0}})})((c=((a,b)=>{if(!b)return a;let c=b.toLowerCase();return a.filter(a=>a.orderNumber.toLowerCase().includes(c)||a.items.some(a=>a.name.toLowerCase().includes(c))||a.merchant.name.toLowerCase().includes(c))})(z,b),a="all"===(d=g)?c:c.filter(a=>a.status===d)),i)},[b,g,i]),s={total:z.length,pending:z.filter(a=>"pending"===a.status).length,processing:z.filter(a=>"processing"===a.status).length,shipped:z.filter(a=>"shipped"===a.status).length,delivered:z.filter(a=>"delivered"===a.status).length,cancelled:z.filter(a=>"cancelled"===a.status).length,aftersale:z.filter(a=>"aftersale"===a.status).length};return(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30",children:[(0,d.jsx)(f.Q,{currentPage:"orders",onNavigate:b=>{switch(b){case"home":case"landing":a.push("/");break;case"services":a.push("/services");break;case"orders":a.push("/orders");break;case"favorites":a.push("/favorites");break;case"support":a.push("/support");break;case"gallery":a.push("/gallery");break;case"login":a.push("/login");break;case"register":a.push("/register")}},onOpenChat:n}),(0,d.jsx)("div",{className:"bg-white/70 backdrop-blur-xl border-b border-slate-200/60",children:(0,d.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-6",children:[(0,d.jsxs)("div",{className:"flex items-center gap-3 mb-8",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-blue-500/10 rounded-xl blur-sm"}),(0,d.jsx)("div",{className:"relative p-3 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-xl border border-blue-200/30",children:(0,d.jsx)(p.A,{className:"h-6 w-6 text-blue-600"})})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-2xl font-bold text-slate-900",children:"我的订单"}),(0,d.jsx)("p",{className:"text-slate-600",children:"管理您的所有订单"})]})]}),(0,d.jsx)("div",{className:"mb-8",children:(0,d.jsx)(r,{activeStatus:g,onStatusChange:h,statusCounts:s})}),(0,d.jsx)(w,{searchTerm:b,onSearchChange:c,statusFilter:g,onStatusFilterChange:h,dateFilter:i,onDateFilterChange:j})]})}),(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-6",children:o.length>0?(0,d.jsx)("div",{className:"space-y-4",children:o.map(b=>(0,d.jsx)(q,{order:b,onViewDetail:()=>{var c;return c=b.id,void a.push(`/order/${c}`)}},b.id))}):(0,d.jsxs)("div",{className:"text-center py-12",children:[(0,d.jsxs)("div",{className:"relative inline-block mb-4",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-slate-200/50 rounded-full blur-xl"}),(0,d.jsx)(p.A,{className:"relative h-12 w-12 text-slate-400 mx-auto"})]}),(0,d.jsx)("h3",{className:"text-lg font-medium text-slate-900 mb-2",children:"暂无订单"}),(0,d.jsx)("p",{className:"text-slate-600",children:b||"all"!==g||"all"!==i?"没有找到符合条件的订单，请尝试调整筛选条件":"您还没有任何订单，快去购物吧！"})]})}),(0,d.jsx)(x.l,{isOpen:k,onClose:()=>{l(!1)},onMinimize:()=>{l(!1)}}),(0,d.jsx)(y.H,{onClick:n,notificationCount:m,isVisible:!k})]})}},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},8354:a=>{"use strict";a.exports=require("util")},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9188:(a,b,c)=>{Promise.resolve().then(c.bind(c,6533))},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9409:(a,b,c)=>{"use strict";c.d(b,{Wu:()=>g,Zp:()=>f});var d=c(568);c(5561);var e=c(2384);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},9459:(a,b,c)=>{"use strict";c.d(b,{bq:()=>l,eb:()=>n,gC:()=>m,l6:()=>j,yv:()=>k});var d=c(568);c(5561);var e=c(5587),f=c(3607),g=c(2125),h=c(9802),i=c(2384);function j({...a}){return(0,d.jsx)(e.bL,{"data-slot":"select",...a})}function k({...a}){return(0,d.jsx)(e.WT,{"data-slot":"select-value",...a})}function l({className:a,size:b="default",children:c,...g}){return(0,d.jsxs)(e.l9,{"data-slot":"select-trigger","data-size":b,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...g,children:[c,(0,d.jsx)(e.In,{asChild:!0,children:(0,d.jsx)(f.A,{className:"size-4 opacity-50"})})]})}function m({className:a,children:b,position:c="popper",...f}){return(0,d.jsx)(e.ZL,{children:(0,d.jsxs)(e.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...f,children:[(0,d.jsx)(o,{}),(0,d.jsx)(e.LM,{className:(0,i.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:b}),(0,d.jsx)(p,{})]})})}function n({className:a,children:b,...c}){return(0,d.jsxs)(e.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...c,children:[(0,d.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,d.jsx)(e.VF,{children:(0,d.jsx)(g.A,{className:"size-4"})})}),(0,d.jsx)(e.p4,{children:b})]})}function o({className:a,...b}){return(0,d.jsx)(e.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(h.A,{className:"size-4"})})}function p({className:a,...b}){return(0,d.jsx)(e.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(f.A,{className:"size-4"})})}}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[343,387,984,344,467,674],()=>b(b.s=4683));module.exports=c})();