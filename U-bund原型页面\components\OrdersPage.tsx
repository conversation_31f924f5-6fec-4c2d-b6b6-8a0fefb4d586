import { Package } from "lucide-react";
import { Order, StatusCounts } from "../types/order";
import { OrderCard } from "./OrderCard";
import { OrderFilters } from "./OrderFilters";
import { OrderStatusTabs } from "./OrderStatusTabs";

interface OrdersPageProps {
  filteredOrders: Order[];
  searchTerm: string;
  onSearchChange: (term: string) => void;
  statusFilter: string;
  onStatusFilterChange: (status: string) => void;
  dateFilter: string;
  onDateFilterChange: (date: string) => void;
  statusCounts: StatusCounts;
  onStatusChange: (status: string) => void;
  onViewOrderDetail: (orderId: string) => void;
}

export function OrdersPage({
  filteredOrders,
  searchTerm,
  onSearchChange,
  statusFilter,
  onStatusFilterChange,
  dateFilter,
  onDateFilterChange,
  statusCounts,
  onStatusChange,
  onViewOrderDetail
}: OrdersPageProps) {
  return (
    <>
      {/* 订单页面内容 */}
      <div className="bg-white/70 backdrop-blur-xl border-b border-slate-200/60">
        <div className="max-w-7xl mx-auto px-4 py-6">
          {/* 页面标题 */}
          <div className="flex items-center gap-3 mb-8">
            <div className="relative">
              <div className="absolute inset-0 bg-blue-500/10 rounded-xl blur-sm"></div>
              <div className="relative p-3 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-xl border border-blue-200/30">
                <Package className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div>
              <h1 className="text-slate-900">我的订单</h1>
              <p className="text-slate-600">管理您的所有订单</p>
            </div>
          </div>
          
          {/* 现代化状态标签 */}
          <div className="mb-8">
            <OrderStatusTabs
              activeStatus={statusFilter}
              onStatusChange={onStatusChange}
              statusCounts={statusCounts}
            />
          </div>

          {/* 筛选器 */}
          <OrderFilters
            searchTerm={searchTerm}
            onSearchChange={onSearchChange}
            statusFilter={statusFilter}
            onStatusFilterChange={onStatusFilterChange}
            dateFilter={dateFilter}
            onDateFilterChange={onDateFilterChange}
          />
        </div>
      </div>

      {/* 订单列表 */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        {filteredOrders.length > 0 ? (
          <div className="space-y-4">
            {filteredOrders.map((order) => (
              <OrderCard 
                key={order.id} 
                order={order} 
                onViewDetail={() => onViewOrderDetail(order.id)}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <div className="relative inline-block mb-4">
              <div className="absolute inset-0 bg-slate-200/50 rounded-full blur-xl"></div>
              <Package className="relative h-12 w-12 text-slate-400 mx-auto" />
            </div>
            <h3 className="text-slate-900 mb-2">暂无订单</h3>
            <p className="text-slate-600">
              {searchTerm || statusFilter !== "all" || dateFilter !== "all" 
                ? "没有找到符合条件的订单，请尝试调整筛选条件"
                : "您还没有任何订单，快去购物吧！"
              }
            </p>
          </div>
        )}
      </div>
    </>
  );
}