export type OrderStatus = "pending" | "processing" | "shipped" | "delivered" | "cancelled" | "aftersale";

export type WorkProgressStatus = "pending" | "current" | "completed";

export interface WorkProgressStep {
  id: string;
  label: string;
  description: string;
  status: WorkProgressStatus;
  completedAt?: string;
}

export interface WorkProgress {
  currentStep: number;
  steps: WorkProgressStep[];
}

export interface Merchant {
  id: string;
  name: string;
  avatar: string;
  isOnline: boolean;
  phone: string;
  rating: number;
}

export interface OrderItem {
  id: string;
  name: string;
  image: string;
  quantity: number;
  price: number;
  specifications: string[];
}

export interface Order {
  id: string;
  orderNumber: string;
  date: string;
  status: OrderStatus;
  total: number;
  shippingAddress: string;
  completedAt?: string;
  estimatedCompletionTime?: string;
  paymentMethod: string;
  shippingFee: number;
  discountAmount: number;
  trackingNumber?: string;
  workProgress: WorkProgress;
  merchant: Merchant;
  items: OrderItem[];
}

export interface StatusCounts {
  total: number;
  pending: number;
  processing: number;
  shipped: number;
  delivered: number;
  cancelled: number;
  aftersale: number;
}