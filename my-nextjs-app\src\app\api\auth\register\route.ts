import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import mysql from 'mysql2/promise';
import { z } from 'zod';

// 数据库连接配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '123456',
  database: process.env.DB_NAME || 'ubund_db',
  port: parseInt(process.env.DB_PORT || '3306'),
};

// JWT密钥
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';

// 注册请求验证schema
const registerSchema = z.object({
  username: z.string()
    .min(3, '用户名至少需要3个字符')
    .max(20, '用户名不能超过20个字符')
    .regex(/^[a-zA-Z0-9_]+$/, '用户名只能包含字母、数字和下划线'),
  password: z.string()
    .min(6, '密码至少需要6个字符')
    .max(50, '密码不能超过50个字符'),
  confirmPassword: z.string().optional(),
}).refine((data) => {
  if (data.confirmPassword && data.password !== data.confirmPassword) {
    return false;
  }
  return true;
}, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
});

// 统一响应格式
function createResponse(success: boolean, data?: any, message?: string, code?: string, status: number = 200) {
  return NextResponse.json({
    success,
    data,
    message,
    code,
  }, { status });
}

// 错误响应
function createErrorResponse(message: string, code?: string, status: number = 400, errors?: Record<string, string[]>) {
  return NextResponse.json({
    success: false,
    message,
    code,
    errors,
  }, { status });
}

export async function POST(request: NextRequest) {
  let connection: mysql.Connection | null = null;

  try {
    // 解析请求体
    const body = await request.json();

    // 验证请求数据
    const validationResult = registerSchema.safeParse(body);
    if (!validationResult.success) {
      const errors: Record<string, string[]> = {};
      validationResult.error.errors.forEach((error) => {
        const field = error.path[0] as string;
        if (!errors[field]) {
          errors[field] = [];
        }
        errors[field].push(error.message);
      });

      return createErrorResponse(
        '输入信息有误，请检查后重试',
        'VALIDATION_ERROR',
        422,
        errors
      );
    }

    const { username, password } = validationResult.data;

    // 连接数据库
    connection = await mysql.createConnection(dbConfig);

    // 检查用户名是否已存在
    const [existingUsers] = await connection.execute(
      'SELECT id FROM users WHERE username = ?',
      [username]
    );

    if (Array.isArray(existingUsers) && existingUsers.length > 0) {
      return createErrorResponse(
        '用户名已存在，请选择其他用户名',
        'DUPLICATE_USERNAME',
        409
      );
    }

    // 加密密码
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 创建用户
    const [result] = await connection.execute(
      `INSERT INTO users (username, password, created_at, updated_at) 
       VALUES (?, ?, NOW(), NOW())`,
      [username, hashedPassword]
    ) as mysql.ResultSetHeader[];

    const userId = result.insertId;

    // 生成JWT token
    const token = jwt.sign(
      { 
        userId, 
        username,
        type: 'access'
      },
      JWT_SECRET,
      { expiresIn: JWT_EXPIRES_IN }
    );

    // 生成refresh token
    const refreshToken = jwt.sign(
      { 
        userId, 
        username,
        type: 'refresh'
      },
      JWT_SECRET,
      { expiresIn: '30d' }
    );

    // 获取用户信息
    const [userRows] = await connection.execute(
      'SELECT id, username, created_at FROM users WHERE id = ?',
      [userId]
    );

    const user = Array.isArray(userRows) ? userRows[0] as any : null;

    if (!user) {
      return createErrorResponse(
        '用户创建失败，请重试',
        'USER_CREATION_FAILED',
        500
      );
    }

    // 返回成功响应
    return createResponse(true, {
      user: {
        id: user.id.toString(),
        username: user.username,
        createdAt: user.created_at,
      },
      token,
      refreshToken,
      expiresIn: 7 * 24 * 60 * 60, // 7天，单位：秒
    }, '注册成功', undefined, 201);

  } catch (error) {
    console.error('Register error:', error);

    // 数据库连接错误
    if (error instanceof Error && error.message.includes('ECONNREFUSED')) {
      return createErrorResponse(
        '数据库连接失败，请稍后重试',
        'DATABASE_CONNECTION_ERROR',
        503
      );
    }

    // 其他错误
    return createErrorResponse(
      '注册失败，请稍后重试',
      'INTERNAL_SERVER_ERROR',
      500
    );
  } finally {
    // 关闭数据库连接
    if (connection) {
      await connection.end();
    }
  }
}

// 处理不支持的HTTP方法
export async function GET() {
  return createErrorResponse(
    '不支持的请求方法',
    'METHOD_NOT_ALLOWED',
    405
  );
}

export async function PUT() {
  return createErrorResponse(
    '不支持的请求方法',
    'METHOD_NOT_ALLOWED',
    405
  );
}

export async function DELETE() {
  return createErrorResponse(
    '不支持的请求方法',
    'METHOD_NOT_ALLOWED',
    405
  );
}
