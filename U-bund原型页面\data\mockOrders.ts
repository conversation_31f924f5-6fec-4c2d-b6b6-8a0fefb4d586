import { Order } from "../types/order";

export const mockOrders: Order[] = [
  {
    id: "1",
    orderNumber: "ORD202501290001",
    date: "2025-01-28",
    status: "delivered",
    total: 8999.00,
    shippingAddress: "",
    completedAt: "2025-01-29",
    estimatedCompletionTime: undefined,
    paymentMethod: "支付宝",
    shippingFee: 0,
    discountAmount: 0,
    workProgress: {
      currentStep: 3,
      steps: [
        { id: "1", label: "需求对接", description: "确认品牌设计需求和视觉方向", status: "completed", completedAt: "2025-01-28T10:30:00" },
        { id: "2", label: "设计制作", description: "Logo创意设计和方案制作", status: "completed", completedAt: "2025-01-28T16:45:00" },
        { id: "3", label: "修改确认", description: "客户反馈修改和最终确认", status: "completed", completedAt: "2025-01-29T09:15:00" },
        { id: "4", label: "项目交付", description: "设计文件输出和素材打包", status: "completed", completedAt: "2025-01-29T11:00:00" }
      ]
    },
    merchant: {
      id: "merchant_1",
      name: "墨刻品牌设计工作室",
      avatar: "https://images.unsplash.com/photo-1472214103451-9374bd1c798e?w=100&h=100&fit=crop&crop=faces",
      isOnline: true,
      phone: "************",
      rating: 4.9
    },
    items: [
      {
        id: "1",
        name: "企业品牌Logo设计 + VI视觉识别系统",
        image: "https://images.unsplash.com/photo-1634942537034-2531766767d1?w=400&h=400&fit=crop",
        quantity: 1,
        price: 8999.00,
        specifications: ["原创设计", "多方案对比", "全套VI手册", "商用授权"]
      }
    ]
  },
  {
    id: "2",
    orderNumber: "ORD202501280002",
    date: "2025-01-27",
    status: "shipped",
    total: 15999.00,
    shippingAddress: "",
    completedAt: undefined,
    estimatedCompletionTime: "2025-01-31",
    paymentMethod: "微信支付",
    shippingFee: 0,
    discountAmount: 1000,
    trackingNumber: "SF1234567890123",
    workProgress: {
      currentStep: 2,
      steps: [
        { id: "1", label: "需求分析", description: "分析网站功能需求和技术架构", status: "completed", completedAt: "2025-01-27T14:20:00" },
        { id: "2", label: "开发制作", description: "前后端开发和功能实现", status: "completed", completedAt: "2025-01-28T20:30:00" },
        { id: "3", label: "测试优化", description: "功能测试和性能优化", status: "current" },
        { id: "4", label: "上线部署", description: "域名配置和服务器部署", status: "pending" }
      ]
    },
    merchant: {
      id: "merchant_2",
      name: "码猿科技开发团队",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",
      isOnline: false,
      phone: "021-12345678",
      rating: 4.7
    },
    items: [
      {
        id: "2",
        name: "企业官网定制开发（响应式）",
        image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=400&fit=crop",
        quantity: 1,
        price: 12999.00,
        specifications: ["React技术栈", "响应式设计", "SEO优化", "后台管理系统"]
      },
      {
        id: "3",
        name: "域名注册和SSL证书配置",
        image: "https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=400&h=400&fit=crop",
        quantity: 1,
        price: 3000.00,
        specifications: [".com域名", "SSL证书", "DNS配置", "一年服务"]
      }
    ]
  },
  {
    id: "3",
    orderNumber: "ORD202501260003",
    date: "2025-01-26",
    status: "processing",
    total: 5699.00,
    shippingAddress: "",
    completedAt: undefined,
    estimatedCompletionTime: "2025-02-02",
    paymentMethod: "信用卡",
    shippingFee: 0,
    discountAmount: 300,
    workProgress: {
      currentStep: 1,
      steps: [
        { id: "1", label: "需求调研", description: "用户调研和功能需求分析", status: "completed", completedAt: "2025-01-26T15:45:00" },
        { id: "2", label: "设计开发", description: "UI设计和原生应用开发", status: "current" },
        { id: "3", label: "测试发布", description: "应用测试和应用商店上架", status: "pending" },
        { id: "4", label: "项目交付", description: "源代码交付和使用培训", status: "pending" }
      ]
    },
    merchant: {
      id: "merchant_3",
      name: "星云移动应用工作室",
      avatar: "https://images.unsplash.com/photo-1494790108755-2616c989c32c?w=100&h=100&fit=crop&crop=faces",
      isOnline: true,
      phone: "020-88888888",
      rating: 4.8
    },
    items: [
      {
        id: "4",
        name: "iOS原生应用开发",
        image: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=400&h=400&fit=crop",
        quantity: 1,
        price: 3999.00,
        specifications: ["Swift开发", "适配iPhone/iPad", "App Store上架", "一年维护"]
      },
      {
        id: "5",
        name: "Android原生应用开发",
        image: "https://images.unsplash.com/photo-1607252650355-f7fd0460ccdb?w=400&h=400&fit=crop",
        quantity: 1,
        price: 1700.00,
        specifications: ["Kotlin开发", "Material Design", "Google Play上架"]
      }
    ]
  },
  {
    id: "4",
    orderNumber: "ORD202501250004",
    date: "2025-01-25",
    status: "pending",
    total: 2899.00,
    shippingAddress: "",
    completedAt: undefined,
    estimatedCompletionTime: "2025-02-05",
    paymentMethod: "待支付",
    shippingFee: 0,
    discountAmount: 0,
    workProgress: {
      currentStep: 0,
      steps: [
        { id: "1", label: "待支付", description: "等待付款确认，准备启动项目", status: "current" },
        { id: "2", label: "内容策划", description: "视频脚本策划和分镜设计", status: "pending" },
        { id: "3", label: "拍摄制作", description: "视频拍摄和后期制作", status: "pending" },
        { id: "4", label: "成品交付", description: "最终视频输出和素材交付", status: "pending" }
      ]
    },
    merchant: {
      id: "merchant_4",
      name: "光影视觉制作工作室",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",
      isOnline: true,
      phone: "0755-12345678",
      rating: 4.6
    },
    items: [
      {
        id: "6",
        name: "企业宣传片拍摄制作（3分钟）",
        image: "https://images.unsplash.com/photo-1485846234645-a62644f84728?w=400&h=400&fit=crop",
        quantity: 1,
        price: 2899.00,
        specifications: ["4K拍摄", "专业团队", "后期调色", "配音配乐", "多格式输出"]
      }
    ]
  },
  {
    id: "5",
    orderNumber: "ORD202501240005",
    date: "2025-01-24",
    status: "cancelled",
    total: 1599.00,
    shippingAddress: "",
    completedAt: undefined,
    estimatedCompletionTime: undefined,
    paymentMethod: "支付宝",
    shippingFee: 0,
    discountAmount: 0,
    workProgress: {
      currentStep: 0,
      steps: [
        { id: "1", label: "需求沟通", description: "订单已取消", status: "pending" },
        { id: "2", label: "策略制定", description: "订单已取消", status: "pending" },
        { id: "3", label: "执行推广", description: "订单已取消", status: "pending" },
        { id: "4", label: "效果交付", description: "订单已取消", status: "pending" }
      ]
    },
    merchant: {
      id: "merchant_5",
      name: "数字营销增长专家",
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=faces",
      isOnline: false,
      phone: "0571-87654321",
      rating: 4.5
    },
    items: [
      {
        id: "7",
        name: "社交媒体营销推广方案（30天）",
        image: "https://images.unsplash.com/photo-1432888622747-4eb9a8efeb07?w=400&h=400&fit=crop",
        quantity: 1,
        price: 1599.00,
        specifications: ["小红书推广", "抖音内容营销", "微博话题运营", "数据报告"]
      }
    ]
  },
  {
    id: "6",
    orderNumber: "ORD202501230006",
    date: "2025-01-23",
    status: "delivered",
    total: 3299.00,
    shippingAddress: "",
    completedAt: "2025-01-29",
    estimatedCompletionTime: undefined,
    paymentMethod: "微信支付",
    shippingFee: 0,
    discountAmount: 200,
    workProgress: {
      currentStep: 3,
      steps: [
        { id: "1", label: "需求分析", description: "深入了解用户体验需求", status: "completed", completedAt: "2025-01-23T09:00:00" },
        { id: "2", label: "设计制作", description: "UI界面设计和交互原型", status: "completed", completedAt: "2025-01-27T18:30:00" },
        { id: "3", label: "修改完善", description: "根据反馈优化设计方案", status: "completed", completedAt: "2025-01-29T14:20:00" },
        { id: "4", label: "设计交付", description: "输出设计规范和切图素材", status: "completed", completedAt: "2025-01-29T16:00:00" }
      ]
    },
    merchant: {
      id: "merchant_6",
      name: "像素完美设计工作室",
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=100&h=100&fit=crop&crop=faces",
      isOnline: true,
      phone: "010-66668888",
      rating: 4.9
    },
    items: [
      {
        id: "8",
        name: "移动端APP UI/UX设计",
        image: "https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=400&h=400&fit=crop",
        quantity: 1,
        price: 3299.00,
        specifications: ["原创设计", "交互原型", "设计规范", "切图标注", "多尺寸适配"]
      }
    ]
  },
  {
    id: "7",
    orderNumber: "ORD202501220007",
    date: "2025-01-22",
    status: "processing",
    total: 899.00,
    shippingAddress: "",
    completedAt: undefined,
    estimatedCompletionTime: "2025-01-30",
    paymentMethod: "支付宝",
    shippingFee: 0,
    discountAmount: 0,
    workProgress: {
      currentStep: 1,
      steps: [
        { id: "1", label: "素材收集", description: "品牌资料和内容素材整理", status: "completed", completedAt: "2025-01-22T11:00:00" },
        { id: "2", label: "文案创作", description: "品牌文案策划和内容撰写", status: "current" },
        { id: "3", label: "内容优化", description: "文案修改和SEO优化", status: "pending" },
        { id: "4", label: "成果交付", description: "最终文案交付和使用指导", status: "pending" }
      ]
    },
    merchant: {
      id: "merchant_7",
      name: "文字匠人创意工作室",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",
      isOnline: true,
      phone: "021-55667788",
      rating: 4.7
    },
    items: [
      {
        id: "9",
        name: "品牌文案策划服务",
        image: "https://images.unsplash.com/photo-1455390582262-044cdead277a?w=400&h=400&fit=crop",
        quantity: 1,
        price: 899.00,
        specifications: ["品牌slogan", "产品文案", "官网内容", "SEO优化", "多语言版本"]
      }
    ]
  },
  {
    id: "8",
    orderNumber: "ORD202501210008",
    date: "2025-01-21",
    status: "shipped",
    total: 1999.00,
    shippingAddress: "",
    completedAt: undefined,
    estimatedCompletionTime: "2025-01-28",
    paymentMethod: "信用卡",
    shippingFee: 0,
    discountAmount: 100,
    trackingNumber: "YZ9876543210",
    workProgress: {
      currentStep: 2,
      steps: [
        { id: "1", label: "拍摄策划", description: "商品拍摄方案和场景设计", status: "completed", completedAt: "2025-01-21T10:30:00" },
        { id: "2", label: "现场拍摄", description: "专业摄影和多角度拍摄", status: "completed", completedAt: "2025-01-25T17:00:00" },
        { id: "3", label: "后期处理", description: "图片精修和色彩调整", status: "current" },
        { id: "4", label: "成品交付", description: "高清图片打包和使用授权", status: "pending" }
      ]
    },
    merchant: {
      id: "merchant_8",
      name: "光影商业摄影工作室",
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",
      isOnline: false,
      phone: "0755-99887766",
      rating: 4.8
    },
    items: [
      {
        id: "10",
        name: "商品摄影服务（20张精修）",
        image: "https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=400&h=400&fit=crop",
        quantity: 1,
        price: 1999.00,
        specifications: ["专业棚拍", "多角度拍摄", "精修20张", "商用授权", "多格式输出"]
      }
    ]
  },
  {
    id: "9",
    orderNumber: "ORD202501200009",
    date: "2025-01-20",
    status: "aftersale",
    total: 2599.00,
    shippingAddress: "",
    completedAt: undefined,
    estimatedCompletionTime: "2025-02-01",
    paymentMethod: "支付宝",
    shippingFee: 0,
    discountAmount: 0,
    workProgress: {
      currentStep: 3,
      steps: [
        { id: "1", label: "需求确认", description: "确认Logo设计修改需求", status: "completed", completedAt: "2025-01-20T10:00:00" },
        { id: "2", label: "设计调整", description: "根据反馈调整设计方案", status: "completed", completedAt: "2025-01-25T15:30:00" },
        { id: "3", label: "售后处理", description: "处理客户售后服务请求", status: "current" },
        { id: "4", label: "问题解决", description: "完成售后问题处理", status: "pending" }
      ]
    },
    merchant: {
      id: "merchant_9",
      name: "墨刻品牌设计工作室",
      avatar: "https://images.unsplash.com/photo-1472214103451-9374bd1c798e?w=100&h=100&fit=crop&crop=faces",
      isOnline: true,
      phone: "************",
      rating: 4.9
    },
    items: [
      {
        id: "11",
        name: "企业Logo设计修改服务",
        image: "https://images.unsplash.com/photo-1634942537034-2531766767d1?w=400&h=400&fit=crop",
        quantity: 1,
        price: 2599.00,
        specifications: ["设计修改", "颜色调整", "格式输出", "售后支持"]
      }
    ]
  },
  {
    id: "10",
    orderNumber: "ORD202501190010",
    date: "2025-01-19",
    status: "aftersale",
    total: 8999.00,
    shippingAddress: "",
    completedAt: undefined,
    estimatedCompletionTime: "2025-01-30",
    paymentMethod: "微信支付",
    shippingFee: 0,
    discountAmount: 0,
    workProgress: {
      currentStep: 3,
      steps: [
        { id: "1", label: "问题诊断", description: "分析网站功能异常问题", status: "completed", completedAt: "2025-01-19T11:30:00" },
        { id: "2", label: "技术修复", description: "修复网站Bug和性能问题", status: "completed", completedAt: "2025-01-22T16:00:00" },
        { id: "3", label: "售后跟踪", description: "持续跟踪修复效果", status: "current" },
        { id: "4", label: "验收确认", description: "客户验收确认修复完成", status: "pending" }
      ]
    },
    merchant: {
      id: "merchant_10",
      name: "码猿科技开发团队",
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",
      isOnline: true,
      phone: "021-12345678",
      rating: 4.7
    },
    items: [
      {
        id: "12",
        name: "网站功能修复和优化",
        image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=400&fit=crop",
        quantity: 1,
        price: 8999.00,
        specifications: ["Bug修复", "性能优化", "安全加固", "30天保修"]
      }
    ]
  }
];