import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ImageWithFallback } from "@/components/figma/ImageWithFallback";
import { 
  Bell, 
  Settings, 
  ChevronDown, 
  Package,
  User,
  Star,
  FileText,
  Search,
  Palette,
  Compass,
  Shield,
  MessageCircle
} from "lucide-react";

interface NavigationV78Props {
  currentPage?: "services" | "orders" | "favorites" | "support" | "gallery";
  onNavigate?: (page: string) => void;
  onOpenChat?: () => void;
}

export function NavigationV78({ currentPage = "services", onNavigate, onOpenChat }: NavigationV78Props) {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [notificationCount] = useState(3); // 模拟通知数量
  const menuRef = useRef<HTMLDivElement>(null);

  const handleNavigate = (page: string) => {
    onNavigate?.(page);
    setShowUserMenu(false);
  };

  const handleNotificationClick = () => {
    onOpenChat?.();
  };

  // 点击外部关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };

    if (showUserMenu) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showUserMenu]);

  return (
    <nav className="bg-white/90 backdrop-blur-sm border-b border-slate-200/60 sticky top-0 z-[70]">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* 左侧 - Logo */}
          <div className="flex items-center gap-8">
            <div className="flex items-center gap-3 cursor-pointer" onClick={() => handleNavigate("home")}>
              <div className="relative">
                <div className="absolute inset-0 bg-blue-500/10 rounded-xl blur-sm"></div>
                <div className="relative p-2 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-xl border border-blue-200/30">
                  <Package className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <span className="text-slate-900 text-lg font-bold tracking-tight">U-bund</span>
            </div>
          </div>

          {/* 中间 - 导航按钮和搜索框 */}
          <div className="flex-1 max-w-4xl mx-8 hidden md:flex items-center gap-4">
            {/* 导航按钮组 */}
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleNavigate("support")}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                  currentPage === "support"
                    ? 'bg-blue-50/80 text-blue-700 shadow-sm' 
                    : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'
                }`}
              >
                <Palette className="h-4 w-4" />
                定制服务*
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleNavigate("services")}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                  currentPage === "services"
                    ? 'bg-blue-50/80 text-blue-700 shadow-sm' 
                    : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'
                }`}
              >
                <Search className="h-4 w-4" />
                找服务
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleNavigate("gallery")}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                  currentPage === "gallery"
                    ? 'bg-blue-50/80 text-blue-700 shadow-sm' 
                    : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'
                }`}
              >
                <Compass className="h-4 w-4" />
                作品广场*
              </Button>
            </div>

            {/* 搜索框 */}
            <div className="flex-1 max-w-md relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <input
                type="text"
                placeholder="搜索服务、商家..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-slate-50/80 border border-slate-200/60 rounded-lg text-sm placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-300 transition-all duration-200"
              />
            </div>
          </div>

          {/* 右侧 - 用户操作区 */}
          <div className="flex items-center gap-3">
            {/* 我的订单 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleNavigate("orders")}
              className={`hidden sm:flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                currentPage === "orders"
                  ? 'bg-blue-50/80 text-blue-700 shadow-sm' 
                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'
              }`}
            >
              <FileText className="h-4 w-4" />
              我的订单
            </Button>

            {/* 我的收藏 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleNavigate("favorites")}
              className={`hidden sm:flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                currentPage === "favorites"
                  ? 'bg-blue-50/80 text-blue-700 shadow-sm' 
                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'
              }`}
            >
              <Star className="h-4 w-4" />
              我的收藏
            </Button>

            {/* 通知铃铛 */}
            <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleNotificationClick}
                className="relative p-2 rounded-lg text-slate-600 hover:text-slate-900 hover:bg-slate-100/80 transition-all duration-200"
              >
                <Bell className="h-5 w-5" />
                {notificationCount > 0 && (
                  <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium">
                    {notificationCount}
                  </span>
                )}
              </Button>
            </div>

            {/* 用户菜单 */}
            <div className="relative" ref={menuRef}>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center gap-2 p-1 pr-2 hover:bg-slate-100/80 transition-colors duration-200"
              >
                <div className="relative">
                  <ImageWithFallback
                    src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces"
                    alt="用户头像"
                    className="w-8 h-8 rounded-full object-cover border border-slate-200/60"
                  />
                  {/* 在线状态指示器 */}
                  <div className="absolute -top-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                  {/* 通知数字角标 */}
                  {notificationCount > 0 && (
                    <div className="absolute -top-2 -right-2 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-white">
                      {notificationCount > 9 ? '9+' : notificationCount}
                    </div>
                  )}
                </div>
                <span className="hidden sm:block text-slate-700 text-sm">张小明</span>
                <ChevronDown className={`h-4 w-4 text-slate-400 transition-transform duration-200 ${
                  showUserMenu ? 'rotate-180' : ''
                }`} />
              </Button>

              {/* 用户下拉菜单 */}
              {showUserMenu && (
                <div className="absolute right-0 top-full mt-2 w-48 bg-white/95 backdrop-blur-sm border border-slate-200/60 rounded-xl shadow-lg py-2 z-[200]">
                  <div className="px-4 py-3 border-b border-slate-200/60">
                    <p className="text-slate-900 text-sm">张小明</p>
                    <p className="text-slate-500 text-xs"><EMAIL></p>
                  </div>
                  <div className="py-1">
                    {/* 通知菜单项 */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        onOpenChat?.();
                        setShowUserMenu(false);
                      }}
                      className="w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80 relative"
                    >
                      <Bell className="h-4 w-4 mr-3" />
                      通知消息
                      {notificationCount > 0 && (
                        <span className="ml-auto bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] text-center">
                          {notificationCount > 9 ? '9+' : notificationCount}
                        </span>
                      )}
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleNavigate("profile")}
                      className="w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80"
                    >
                      <User className="h-4 w-4 mr-3" />
                      个人资料*
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80"
                    >
                      <Shield className="h-4 w-4 mr-3" />
                      认证专区*
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80"
                    >
                      <Settings className="h-4 w-4 mr-3" />
                      账户设置*
                    </Button>
                  </div>
                  <div className="border-t border-slate-200/60 pt-1">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start px-4 py-2 text-red-600 hover:text-red-700 hover:bg-red-50/80"
                    >
                      退出登录
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
