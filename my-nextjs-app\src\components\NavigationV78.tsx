import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Bell, 
  Settings, 
  ChevronDown, 
  Package,
  User,
  Star,
  FileText,
  Search,
  Palette,
  Compass,
  Shield,
  MessageCircle
} from "lucide-react";

interface NavigationV78Props {
  currentPage?: "services" | "orders" | "favorites" | "support" | "gallery";
  onNavigate?: (page: string) => void;
  onOpenChat?: () => void;
}

export function NavigationV78({ currentPage = "services", onNavigate, onOpenChat }: NavigationV78Props) {
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [notificationCount] = useState(3); // 模拟通知数量

  const handleNavigate = (page: string) => {
    onNavigate?.(page);
    setShowUserMenu(false);
  };

  const handleNotificationClick = () => {
    onOpenChat?.();
  };

  return (
    <nav className="bg-white/90 backdrop-blur-sm border-b border-slate-200/60 sticky top-0 z-[70]">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* 左侧 - Logo */}
          <div className="flex items-center gap-8">
            <div className="flex items-center gap-3 cursor-pointer" onClick={() => handleNavigate("home")}>
              <div className="relative">
                <div className="absolute inset-0 bg-blue-500/10 rounded-xl blur-sm"></div>
                <div className="relative p-2 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-xl border border-blue-200/30">
                  <Package className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <span className="text-slate-900 text-lg font-bold tracking-tight">U-bund</span>
            </div>
          </div>

          {/* 中间 - 导航按钮和搜索框 */}
          <div className="flex-1 max-w-4xl mx-8 hidden md:flex items-center gap-4">
            {/* 导航按钮组 */}
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleNavigate("support")}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                  currentPage === "support"
                    ? 'bg-blue-50/80 text-blue-700 shadow-sm' 
                    : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'
                }`}
              >
                <Palette className="h-4 w-4" />
                定制服务*
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleNavigate("services")}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                  currentPage === "services"
                    ? 'bg-blue-50/80 text-blue-700 shadow-sm' 
                    : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'
                }`}
              >
                <Search className="h-4 w-4" />
                找服务
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleNavigate("gallery")}
                className={`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                  currentPage === "gallery"
                    ? 'bg-blue-50/80 text-blue-700 shadow-sm' 
                    : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'
                }`}
              >
                <Compass className="h-4 w-4" />
                作品广场*
              </Button>
            </div>

            {/* 搜索框 */}
            <div className="flex-1 max-w-md relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <input
                type="text"
                placeholder="搜索服务、商家..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-slate-50/80 border border-slate-200/60 rounded-lg text-sm placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-300 transition-all duration-200"
              />
            </div>
          </div>

          {/* 右侧 - 用户操作区 */}
          <div className="flex items-center gap-3">
            {/* 我的订单 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleNavigate("orders")}
              className={`hidden sm:flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                currentPage === "orders"
                  ? 'bg-blue-50/80 text-blue-700 shadow-sm' 
                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'
              }`}
            >
              <FileText className="h-4 w-4" />
              我的订单
            </Button>

            {/* 我的收藏 */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleNavigate("favorites")}
              className={`hidden sm:flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                currentPage === "favorites"
                  ? 'bg-blue-50/80 text-blue-700 shadow-sm' 
                  : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100/80'
              }`}
            >
              <Star className="h-4 w-4" />
              我的收藏
            </Button>

            {/* 通知铃铛 */}
            <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleNotificationClick}
                className="relative p-2 rounded-lg text-slate-600 hover:text-slate-900 hover:bg-slate-100/80 transition-all duration-200"
              >
                <Bell className="h-5 w-5" />
                {notificationCount > 0 && (
                  <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium">
                    {notificationCount}
                  </span>
                )}
              </Button>
            </div>

            {/* 用户头像和菜单 */}
            <div className="relative">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center gap-2 p-1 rounded-lg hover:bg-slate-100/80 transition-all duration-200"
              >
                <Avatar className="h-8 w-8">
                  <AvatarImage src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces" />
                  <AvatarFallback>用户</AvatarFallback>
                </Avatar>
                <ChevronDown className="h-4 w-4 text-slate-600" />
              </Button>

              {/* 用户菜单下拉 */}
              {showUserMenu && (
                <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-slate-200/60 py-2 z-50">
                  <button
                    onClick={() => handleNavigate("profile")}
                    className="w-full flex items-center gap-3 px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 transition-colors"
                  >
                    <User className="h-4 w-4" />
                    个人中心
                  </button>
                  <button
                    onClick={() => handleNavigate("settings")}
                    className="w-full flex items-center gap-3 px-4 py-2 text-sm text-slate-700 hover:bg-slate-50 transition-colors"
                  >
                    <Settings className="h-4 w-4" />
                    设置
                  </button>
                  <div className="border-t border-slate-200/60 my-2"></div>
                  <button
                    onClick={() => handleNavigate("logout")}
                    className="w-full flex items-center gap-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                  >
                    退出登录
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </nav>
  );
}
