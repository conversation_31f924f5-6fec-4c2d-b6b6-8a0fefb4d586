import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Search,
  Play,
  Palette,
  Code,
  Video,
  Megaphone,
  FileText,
  Music,
  TrendingUp,
  Star,
  Users,
  Award,
  ArrowRight,
  CheckCircle
} from "lucide-react";

interface LandingPageProps {
  onNavigateToServices?: () => void;
}

export function LandingPage({ onNavigateToServices }: LandingPageProps) {
  const [searchQuery, setSearchQuery] = useState("");

  // 热门商品数据
  const featuredProducts = [
    {
      id: "1",
      name: "品牌视觉设计",
      price: 999,
      image: "https://images.unsplash.com/photo-1558655146-d09347e92766?w=300&h=200&fit=crop",
      rating: 5.0,
      reviews: 1234
    },
    {
      id: "2",
      name: "网站开发定制",
      price: 4999,
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=300&h=200&fit=crop",
      rating: 4.9,
      reviews: 856
    },
    {
      id: "3",
      name: "短视频制作",
      price: 1999,
      image: "https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=300&h=200&fit=crop",
      rating: 4.8,
      reviews: 2341
    },
    {
      id: "4",
      name: "文案策划服务",
      price: 699,
      image: "https://images.unsplash.com/photo-1455390582262-044cdead277a?w=300&h=200&fit=crop",
      rating: 4.7,
      reviews: 567
    }
  ];

  // 热门分类数据
  const categories = [
    {
      id: "design",
      name: "设计",
      description: "Logo设计、UI设计、平面设计",
      icon: Palette,
      color: "from-purple-500 to-pink-500",
      bgColor: "bg-purple-50",
      count: "12,000+"
    },
    {
      id: "tech",
      name: "技术开发",
      description: "网站开发、APP开发、小程序",
      icon: Code,
      color: "from-blue-500 to-cyan-500",
      bgColor: "bg-blue-50",
      count: "8,500+"
    },
    {
      id: "video",
      name: "视频制作",
      description: "短视频、宣传片、动画制作",
      icon: Video,
      color: "from-red-500 to-orange-500",
      bgColor: "bg-red-50",
      count: "6,200+"
    },
    {
      id: "marketing",
      name: "营销推广",
      description: "文案策划、社媒运营、广告投放",
      icon: Megaphone,
      color: "from-green-500 to-emerald-500",
      bgColor: "bg-green-50",
      count: "4,800+"
    },
    {
      id: "writing",
      name: "文案写作",
      description: "软文写作、新闻稿、产品文案",
      icon: FileText,
      color: "from-yellow-500 to-amber-500",
      bgColor: "bg-yellow-50",
      count: "3,600+"
    },
    {
      id: "music",
      name: "音乐音效",
      description: "原创音乐、配音、音效制作",
      icon: Music,
      color: "from-indigo-500 to-purple-500",
      bgColor: "bg-indigo-50",
      count: "2,100+"
    }
  ];

  const handleSearch = () => {
    if (searchQuery.trim()) {
      onNavigateToServices?.();
    }
  };

  const handleCategoryClick = (categoryId: string) => {
    onNavigateToServices?.();
  };

  // 模拟热门服务数据
  const featuredServices = [
    {
      id: "1",
      title: "✨绝美品牌视觉设计，让你的LOGO成为爆款记忆点！",
      description: "姐妹们！这个设计师真的太会了😍 从LOGO到整套VI，各种风格都能拿捏住",
      price: 999,
      originalPrice: 1999,
      image: "https://images.unsplash.com/photo-1558655146-d09347e92766?w=400&h=400&fit=crop",
      rating: 5.0,
      reviews: 1234,
      creator: {
        name: "设计师小王",
        avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=faces",
        verified: true
      },
      tags: ["热门", "原创", "好评Top"]
    },
    {
      id: "2", 
      title: "🚀全栈网站开发YYDS！React+Node.js技术栈",
      description: "宝贝们看过来！这位程序员小哥哥技术真的绝了🔥 响应式设计+全栈开发",
      price: 4999,
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=400&fit=crop",
      rating: 4.9,
      reviews: 856,
      creator: {
        name: "程序员老李",
        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",
        verified: true
      },
      tags: ["专业", "全栈", "技能之星"]
    }
  ];

  const stats = [
    { label: "注册用户", value: "100万+", icon: Users },
    { label: "服务订单", value: "50万+", icon: TrendingUp },
    { label: "优质服务商", value: "10万+", icon: Award },
    { label: "好评率", value: "99.8%", icon: Star }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30">
      {/* Hero区域 - 视频介绍 */}
      <section className="relative overflow-hidden py-28">
        {/* 视频背景 */}
        <video
          autoPlay
          muted
          loop
          playsInline
          className="absolute inset-0 w-full h-full object-cover z-0"
        >
          <source src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" type="video/mp4" />
          <source src="https://www.w3schools.com/html/mov_bbb.mp4" type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        {/* 半透明覆盖层 */}
        <div className="absolute inset-0 bg-black/20 z-10"></div>

        <div className="relative z-20 max-w-7xl mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* 左侧文字内容 */}
            <div>
              <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
                发现专业服务
                <br />
                <span className="text-blue-400">成就无限可能</span>
              </h1>
              <p className="text-white/90 mb-8 text-lg leading-relaxed">
                U-bund连接全球优秀创作者，为您提供设计、技术、营销等
                <br />
                各领域的专业服务。从创意到实现，我们助您实现每一个想法。
              </p>

              {/* 搜索框 */}
              <div className="relative mb-6">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-slate-400" />
                <input
                  type="text"
                  placeholder="搜索您需要的服务..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  className="w-full pl-12 pr-4 py-4 text-slate-700 placeholder-slate-400 focus:outline-none bg-white rounded-full shadow-lg border border-slate-200/60"
                />
              </div>

              {/* 热门商品快捷按钮 */}
              <div className="flex flex-wrap gap-3">
                {featuredProducts.map((product) => (
                  <Button
                    key={product.id}
                    variant="outline"
                    size="sm"
                    onClick={onNavigateToServices}
                    className="bg-white/80 border-slate-200/60 text-slate-600 hover:text-slate-900 hover:bg-white"
                  >
                    {product.name}
                  </Button>
                ))}
              </div>
            </div>

            {/* 右侧视频/图片区域 */}
            <div className="relative">
              {/* 这里可以放置其他内容 */}
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-3xl mx-4">
        <div className="max-w-6xl mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-xl mb-4">
                  <stat.icon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="text-3xl font-bold text-slate-900 mb-2">{stat.value}</div>
                <div className="text-slate-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Services */}
      <section className="py-16 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              热门精选服务
            </h2>
            <p className="text-xl text-slate-600">
              发现最受欢迎的优质服务，让专业的人做专业的事
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {featuredServices.map((service) => (
              <Card key={service.id} className="group hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm">
                <CardContent className="p-0">
                  <div className="relative overflow-hidden rounded-t-lg">
                    <img 
                      src={service.image} 
                      alt={service.title}
                      className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-4 left-4 flex gap-2">
                      {service.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="bg-white/90 text-slate-700">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div className="p-6">
                    <h3 className="font-bold text-lg text-slate-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
                      {service.title}
                    </h3>
                    
                    <p className="text-slate-600 text-sm mb-4 line-clamp-2">
                      {service.description}
                    </p>
                    
                    <div className="flex items-center gap-3 mb-4">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={service.creator.avatar} />
                        <AvatarFallback>{service.creator.name[0]}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-1">
                          <span className="text-sm font-medium text-slate-900">{service.creator.name}</span>
                          {service.creator.verified && (
                            <CheckCircle className="h-4 w-4 text-blue-500" />
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        <span className="text-sm font-medium">{service.rating}</span>
                        <span className="text-sm text-slate-500">({service.reviews})</span>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-2xl font-bold text-blue-600">¥{service.price}</span>
                        {service.originalPrice && (
                          <span className="text-sm text-slate-500 line-through">¥{service.originalPrice}</span>
                        )}
                      </div>
                      <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                        立即购买
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <Button 
              variant="outline" 
              size="lg"
              onClick={onNavigateToServices}
              className="px-8 py-3"
            >
              查看更多服务
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-3xl mx-4 text-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            准备开始您的项目了吗？
          </h2>
          <p className="text-xl mb-8 opacity-90">
            加入U-bund，与专业服务提供者合作，让您的想法变为现实
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              size="lg" 
              variant="secondary"
              onClick={onNavigateToServices}
              className="bg-white text-blue-600 hover:bg-slate-100 px-8 py-3"
            >
              立即开始
            </Button>
            <Button 
              size="lg" 
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-blue-600 px-8 py-3"
            >
              了解更多
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
