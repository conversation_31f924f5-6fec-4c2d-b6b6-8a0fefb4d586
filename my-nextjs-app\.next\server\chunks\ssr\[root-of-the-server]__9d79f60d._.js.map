{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/lib/api-client.ts"], "sourcesContent": ["// API客户端配置和类型定义\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  code?: string;\n  errors?: Record<string, string[]>;\n}\n\nexport interface ApiError {\n  message: string;\n  code?: string;\n  status?: number;\n  errors?: Record<string, string[]>;\n}\n\nexport class ApiClientError extends Error {\n  public code?: string;\n  public status?: number;\n  public errors?: Record<string, string[]>;\n\n  constructor(message: string, code?: string, status?: number, errors?: Record<string, string[]>) {\n    super(message);\n    this.name = 'ApiClientError';\n    this.code = code;\n    this.status = status;\n    this.errors = errors;\n  }\n}\n\n// API客户端配置\ninterface ApiClientConfig {\n  baseURL?: string;\n  timeout?: number;\n  headers?: Record<string, string>;\n  onError?: (error: ApiClientError) => void;\n  onSuccess?: (response: any) => void;\n}\n\nclass ApiClient {\n  private baseURL: string;\n  private timeout: number;\n  private defaultHeaders: Record<string, string>;\n  private onError?: (error: ApiClientError) => void;\n  private onSuccess?: (response: any) => void;\n\n  constructor(config: ApiClientConfig = {}) {\n    this.baseURL = config.baseURL || '/api';\n    this.timeout = config.timeout || 10000;\n    this.defaultHeaders = {\n      'Content-Type': 'application/json',\n      ...config.headers,\n    };\n    this.onError = config.onError;\n    this.onSuccess = config.onSuccess;\n  }\n\n  // 设置认证token\n  setAuthToken(token: string) {\n    this.defaultHeaders['Authorization'] = `Bearer ${token}`;\n  }\n\n  // 移除认证token\n  removeAuthToken() {\n    delete this.defaultHeaders['Authorization'];\n  }\n\n  // 通用请求方法\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    const url = `${this.baseURL}${endpoint}`;\n    \n    // 创建AbortController用于超时控制\n    const controller = new AbortController();\n    const timeoutId = setTimeout(() => controller.abort(), this.timeout);\n\n    try {\n      const response = await fetch(url, {\n        ...options,\n        headers: {\n          ...this.defaultHeaders,\n          ...options.headers,\n        },\n        signal: controller.signal,\n      });\n\n      clearTimeout(timeoutId);\n\n      // 解析响应\n      let responseData: ApiResponse<T>;\n      \n      try {\n        responseData = await response.json();\n      } catch {\n        // 如果响应不是JSON，创建默认响应结构\n        responseData = {\n          success: response.ok,\n          message: response.ok ? 'Success' : 'Request failed',\n        };\n      }\n\n      // 检查HTTP状态码\n      if (!response.ok) {\n        const error = new ApiClientError(\n          responseData.message || `HTTP ${response.status}: ${response.statusText}`,\n          responseData.code,\n          response.status,\n          responseData.errors\n        );\n        \n        this.onError?.(error);\n        throw error;\n      }\n\n      // 检查业务状态码\n      if (!responseData.success) {\n        const error = new ApiClientError(\n          responseData.message || 'Request failed',\n          responseData.code,\n          response.status,\n          responseData.errors\n        );\n        \n        this.onError?.(error);\n        throw error;\n      }\n\n      this.onSuccess?.(responseData);\n      return responseData;\n\n    } catch (error) {\n      clearTimeout(timeoutId);\n      \n      if (error instanceof ApiClientError) {\n        throw error;\n      }\n\n      // 处理网络错误、超时等\n      if (error instanceof Error) {\n        if (error.name === 'AbortError') {\n          const timeoutError = new ApiClientError('Request timeout', 'TIMEOUT', 408);\n          this.onError?.(timeoutError);\n          throw timeoutError;\n        }\n        \n        const networkError = new ApiClientError(\n          error.message || 'Network error',\n          'NETWORK_ERROR',\n          0\n        );\n        this.onError?.(networkError);\n        throw networkError;\n      }\n\n      // 未知错误\n      const unknownError = new ApiClientError('Unknown error', 'UNKNOWN_ERROR', 0);\n      this.onError?.(unknownError);\n      throw unknownError;\n    }\n  }\n\n  // GET请求\n  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {\n    let url = endpoint;\n    \n    if (params) {\n      const searchParams = new URLSearchParams();\n      Object.entries(params).forEach(([key, value]) => {\n        if (value !== undefined && value !== null) {\n          searchParams.append(key, String(value));\n        }\n      });\n      \n      if (searchParams.toString()) {\n        url += `?${searchParams.toString()}`;\n      }\n    }\n\n    return this.request<T>(url, {\n      method: 'GET',\n    });\n  }\n\n  // POST请求\n  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, {\n      method: 'POST',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  // PUT请求\n  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, {\n      method: 'PUT',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  // PATCH请求\n  async patch<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, {\n      method: 'PATCH',\n      body: data ? JSON.stringify(data) : undefined,\n    });\n  }\n\n  // DELETE请求\n  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {\n    return this.request<T>(endpoint, {\n      method: 'DELETE',\n    });\n  }\n\n  // 文件上传\n  async upload<T>(endpoint: string, formData: FormData): Promise<ApiResponse<T>> {\n    const headers = { ...this.defaultHeaders };\n    delete headers['Content-Type']; // 让浏览器自动设置multipart/form-data\n\n    return this.request<T>(endpoint, {\n      method: 'POST',\n      body: formData,\n      headers,\n    });\n  }\n}\n\n// 创建默认的API客户端实例\nexport const apiClient = new ApiClient({\n  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || '/api',\n  timeout: 10000,\n});\n\n// 导出类型和客户端\nexport { ApiClient };\nexport default apiClient;\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;;;AAgBT,MAAM,uBAAuB;IAC3B,KAAc;IACd,OAAgB;IAChB,OAAkC;IAEzC,YAAY,OAAe,EAAE,IAAa,EAAE,MAAe,EAAE,MAAiC,CAAE;QAC9F,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;IAChB;AACF;AAWA,MAAM;IACI,QAAgB;IAChB,QAAgB;IAChB,eAAuC;IACvC,QAA0C;IAC1C,UAAoC;IAE5C,YAAY,SAA0B,CAAC,CAAC,CAAE;QACxC,IAAI,CAAC,OAAO,GAAG,OAAO,OAAO,IAAI;QACjC,IAAI,CAAC,OAAO,GAAG,OAAO,OAAO,IAAI;QACjC,IAAI,CAAC,cAAc,GAAG;YACpB,gBAAgB;YAChB,GAAG,OAAO,OAAO;QACnB;QACA,IAAI,CAAC,OAAO,GAAG,OAAO,OAAO;QAC7B,IAAI,CAAC,SAAS,GAAG,OAAO,SAAS;IACnC;IAEA,YAAY;IACZ,aAAa,KAAa,EAAE;QAC1B,IAAI,CAAC,cAAc,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IAC1D;IAEA,YAAY;IACZ,kBAAkB;QAChB,OAAO,IAAI,CAAC,cAAc,CAAC,gBAAgB;IAC7C;IAEA,SAAS;IACT,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU;QAExC,0BAA0B;QAC1B,MAAM,aAAa,IAAI;QACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI,IAAI,CAAC,OAAO;QAEnE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,GAAG,OAAO;gBACV,SAAS;oBACP,GAAG,IAAI,CAAC,cAAc;oBACtB,GAAG,QAAQ,OAAO;gBACpB;gBACA,QAAQ,WAAW,MAAM;YAC3B;YAEA,aAAa;YAEb,OAAO;YACP,IAAI;YAEJ,IAAI;gBACF,eAAe,MAAM,SAAS,IAAI;YACpC,EAAE,OAAM;gBACN,sBAAsB;gBACtB,eAAe;oBACb,SAAS,SAAS,EAAE;oBACpB,SAAS,SAAS,EAAE,GAAG,YAAY;gBACrC;YACF;YAEA,YAAY;YACZ,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,QAAQ,IAAI,eAChB,aAAa,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE,EACzE,aAAa,IAAI,EACjB,SAAS,MAAM,EACf,aAAa,MAAM;gBAGrB,IAAI,CAAC,OAAO,GAAG;gBACf,MAAM;YACR;YAEA,UAAU;YACV,IAAI,CAAC,aAAa,OAAO,EAAE;gBACzB,MAAM,QAAQ,IAAI,eAChB,aAAa,OAAO,IAAI,kBACxB,aAAa,IAAI,EACjB,SAAS,MAAM,EACf,aAAa,MAAM;gBAGrB,IAAI,CAAC,OAAO,GAAG;gBACf,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,GAAG;YACjB,OAAO;QAET,EAAE,OAAO,OAAO;YACd,aAAa;YAEb,IAAI,iBAAiB,gBAAgB;gBACnC,MAAM;YACR;YAEA,aAAa;YACb,IAAI,iBAAiB,OAAO;gBAC1B,IAAI,MAAM,IAAI,KAAK,cAAc;oBAC/B,MAAM,eAAe,IAAI,eAAe,mBAAmB,WAAW;oBACtE,IAAI,CAAC,OAAO,GAAG;oBACf,MAAM;gBACR;gBAEA,MAAM,eAAe,IAAI,eACvB,MAAM,OAAO,IAAI,iBACjB,iBACA;gBAEF,IAAI,CAAC,OAAO,GAAG;gBACf,MAAM;YACR;YAEA,OAAO;YACP,MAAM,eAAe,IAAI,eAAe,iBAAiB,iBAAiB;YAC1E,IAAI,CAAC,OAAO,GAAG;YACf,MAAM;QACR;IACF;IAEA,QAAQ;IACR,MAAM,IAAO,QAAgB,EAAE,MAA4B,EAA2B;QACpF,IAAI,MAAM;QAEV,IAAI,QAAQ;YACV,MAAM,eAAe,IAAI;YACzB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAC1C,IAAI,UAAU,aAAa,UAAU,MAAM;oBACzC,aAAa,MAAM,CAAC,KAAK,OAAO;gBAClC;YACF;YAEA,IAAI,aAAa,QAAQ,IAAI;gBAC3B,OAAO,CAAC,CAAC,EAAE,aAAa,QAAQ,IAAI;YACtC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAAI,KAAK;YAC1B,QAAQ;QACV;IACF;IAEA,SAAS;IACT,MAAM,KAAQ,QAAgB,EAAE,IAAU,EAA2B;QACnE,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,QAAQ;IACR,MAAM,IAAO,QAAgB,EAAE,IAAU,EAA2B;QAClE,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,UAAU;IACV,MAAM,MAAS,QAAgB,EAAE,IAAU,EAA2B;QACpE,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,WAAW;IACX,MAAM,OAAU,QAAgB,EAA2B;QACzD,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;QACV;IACF;IAEA,OAAO;IACP,MAAM,OAAU,QAAgB,EAAE,QAAkB,EAA2B;QAC7E,MAAM,UAAU;YAAE,GAAG,IAAI,CAAC,cAAc;QAAC;QACzC,OAAO,OAAO,CAAC,eAAe,EAAE,8BAA8B;QAE9D,OAAO,IAAI,CAAC,OAAO,CAAI,UAAU;YAC/B,QAAQ;YACR,MAAM;YACN;QACF;IACF;AACF;AAGO,MAAM,YAAY,IAAI,UAAU;IACrC,SAAS,QAAQ,GAAG,CAAC,wBAAwB,IAAI;IACjD,SAAS;AACX;;uCAIe", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/lib/auth-api.ts"], "sourcesContent": ["import apiClient, { ApiResponse } from './api-client';\n\n// 认证相关的类型定义\nexport interface LoginRequest {\n  username: string;\n  password: string;\n}\n\nexport interface RegisterRequest {\n  username: string;\n  password: string;\n  confirmPassword?: string;\n}\n\nexport interface AuthResponse {\n  user: {\n    id: string;\n    username: string;\n    email?: string;\n    avatar?: string;\n    createdAt: string;\n  };\n  token: string;\n  refreshToken?: string;\n  expiresIn: number;\n}\n\nexport interface RefreshTokenRequest {\n  refreshToken: string;\n}\n\nexport interface ChangePasswordRequest {\n  currentPassword: string;\n  newPassword: string;\n  confirmPassword: string;\n}\n\nexport interface ResetPasswordRequest {\n  email: string;\n}\n\nexport interface ConfirmResetPasswordRequest {\n  token: string;\n  newPassword: string;\n  confirmPassword: string;\n}\n\n// 认证API类\nexport class AuthAPI {\n  // 用户注册\n  static async register(data: RegisterRequest): Promise<ApiResponse<AuthResponse>> {\n    return apiClient.post<AuthResponse>('/auth/register', data);\n  }\n\n  // 用户登录\n  static async login(data: LoginRequest): Promise<ApiResponse<AuthResponse>> {\n    return apiClient.post<AuthResponse>('/auth/login', data);\n  }\n\n  // 用户登出\n  static async logout(): Promise<ApiResponse<void>> {\n    return apiClient.post<void>('/auth/logout');\n  }\n\n  // 刷新Token\n  static async refreshToken(data: RefreshTokenRequest): Promise<ApiResponse<AuthResponse>> {\n    return apiClient.post<AuthResponse>('/auth/refresh', data);\n  }\n\n  // 获取当前用户信息\n  static async getCurrentUser(): Promise<ApiResponse<AuthResponse['user']>> {\n    return apiClient.get<AuthResponse['user']>('/auth/me');\n  }\n\n  // 修改密码\n  static async changePassword(data: ChangePasswordRequest): Promise<ApiResponse<void>> {\n    return apiClient.post<void>('/auth/change-password', data);\n  }\n\n  // 请求重置密码\n  static async requestPasswordReset(data: ResetPasswordRequest): Promise<ApiResponse<void>> {\n    return apiClient.post<void>('/auth/reset-password', data);\n  }\n\n  // 确认重置密码\n  static async confirmPasswordReset(data: ConfirmResetPasswordRequest): Promise<ApiResponse<void>> {\n    return apiClient.post<void>('/auth/reset-password/confirm', data);\n  }\n\n  // 验证Token是否有效\n  static async validateToken(): Promise<ApiResponse<{ valid: boolean }>> {\n    return apiClient.get<{ valid: boolean }>('/auth/validate');\n  }\n}\n\n// 用户相关API\nexport class UserAPI {\n  // 更新用户资料\n  static async updateProfile(data: {\n    username?: string;\n    email?: string;\n    avatar?: string;\n    bio?: string;\n  }): Promise<ApiResponse<AuthResponse['user']>> {\n    return apiClient.patch<AuthResponse['user']>('/user/profile', data);\n  }\n\n  // 上传头像\n  static async uploadAvatar(file: File): Promise<ApiResponse<{ avatar: string }>> {\n    const formData = new FormData();\n    formData.append('avatar', file);\n    return apiClient.upload<{ avatar: string }>('/user/avatar', formData);\n  }\n\n  // 删除账户\n  static async deleteAccount(password: string): Promise<ApiResponse<void>> {\n    return apiClient.post<void>('/user/delete', { password });\n  }\n\n  // 获取用户统计信息\n  static async getUserStats(): Promise<ApiResponse<{\n    ordersCount: number;\n    favoritesCount: number;\n    reviewsCount: number;\n    totalSpent: number;\n  }>> {\n    return apiClient.get('/user/stats');\n  }\n}\n\n// 导出所有API\nexport const authAPI = AuthAPI;\nexport const userAPI = UserAPI;\n\n// 默认导出\nexport default {\n  auth: AuthAPI,\n  user: UserAPI,\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAgDO,MAAM;IACX,OAAO;IACP,aAAa,SAAS,IAAqB,EAAsC;QAC/E,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAe,kBAAkB;IACxD;IAEA,OAAO;IACP,aAAa,MAAM,IAAkB,EAAsC;QACzE,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAe,eAAe;IACrD;IAEA,OAAO;IACP,aAAa,SAAqC;QAChD,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAO;IAC9B;IAEA,UAAU;IACV,aAAa,aAAa,IAAyB,EAAsC;QACvF,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAe,iBAAiB;IACvD;IAEA,WAAW;IACX,aAAa,iBAA6D;QACxE,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAuB;IAC7C;IAEA,OAAO;IACP,aAAa,eAAe,IAA2B,EAA8B;QACnF,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAO,yBAAyB;IACvD;IAEA,SAAS;IACT,aAAa,qBAAqB,IAA0B,EAA8B;QACxF,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAO,wBAAwB;IACtD;IAEA,SAAS;IACT,aAAa,qBAAqB,IAAiC,EAA8B;QAC/F,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAO,gCAAgC;IAC9D;IAEA,cAAc;IACd,aAAa,gBAA0D;QACrE,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAqB;IAC3C;AACF;AAGO,MAAM;IACX,SAAS;IACT,aAAa,cAAc,IAK1B,EAA8C;QAC7C,OAAO,2HAAA,CAAA,UAAS,CAAC,KAAK,CAAuB,iBAAiB;IAChE;IAEA,OAAO;IACP,aAAa,aAAa,IAAU,EAA4C;QAC9E,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,UAAU;QAC1B,OAAO,2HAAA,CAAA,UAAS,CAAC,MAAM,CAAqB,gBAAgB;IAC9D;IAEA,OAAO;IACP,aAAa,cAAc,QAAgB,EAA8B;QACvE,OAAO,2HAAA,CAAA,UAAS,CAAC,IAAI,CAAO,gBAAgB;YAAE;QAAS;IACzD;IAEA,WAAW;IACX,aAAa,eAKT;QACF,OAAO,2HAAA,CAAA,UAAS,CAAC,GAAG,CAAC;IACvB;AACF;AAGO,MAAM,UAAU;AAChB,MAAM,UAAU;uCAGR;IACb,MAAM;IACN,MAAM;AACR", "debugId": null}}, {"offset": {"line": 266, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/lib/error-handler.ts"], "sourcesContent": ["import toast from 'react-hot-toast';\nimport { ApiClientError } from './api-client';\n\n// 错误类型定义\nexport interface ErrorInfo {\n  message: string;\n  code?: string;\n  status?: number;\n  errors?: Record<string, string[]>;\n}\n\n// 错误处理器类\nexport class ErrorHandler {\n  // 显示错误提示\n  static showError(error: ApiClientError | Error | string) {\n    let errorInfo: ErrorInfo;\n\n    if (typeof error === 'string') {\n      errorInfo = { message: error };\n    } else if (error instanceof ApiClientError) {\n      errorInfo = {\n        message: error.message,\n        code: error.code,\n        status: error.status,\n        errors: error.errors,\n      };\n    } else {\n      errorInfo = { message: error.message || 'Unknown error' };\n    }\n\n    // 根据错误类型显示不同的提示\n    this.displayErrorToast(errorInfo);\n    \n    // 记录错误日志\n    this.logError(errorInfo);\n  }\n\n  // 显示成功提示\n  static showSuccess(message: string) {\n    toast.success(message, {\n      duration: 3000,\n      position: 'top-right',\n    });\n  }\n\n  // 显示警告提示\n  static showWarning(message: string) {\n    toast(message, {\n      duration: 4000,\n      position: 'top-right',\n      icon: '⚠️',\n    });\n  }\n\n  // 显示信息提示\n  static showInfo(message: string) {\n    toast(message, {\n      duration: 3000,\n      position: 'top-right',\n      icon: 'ℹ️',\n    });\n  }\n\n  // 显示加载提示\n  static showLoading(message: string = 'Loading...') {\n    return toast.loading(message, {\n      position: 'top-right',\n    });\n  }\n\n  // 关闭加载提示\n  static dismissLoading(toastId: string) {\n    toast.dismiss(toastId);\n  }\n\n  // 显示错误Toast\n  private static displayErrorToast(errorInfo: ErrorInfo) {\n    let message = errorInfo.message;\n\n    // 如果有字段验证错误，显示第一个错误\n    if (errorInfo.errors) {\n      const firstError = Object.values(errorInfo.errors)[0];\n      if (firstError && firstError.length > 0) {\n        message = firstError[0];\n      }\n    }\n\n    // 根据错误码或状态码自定义消息\n    const customMessage = this.getCustomErrorMessage(errorInfo);\n    if (customMessage) {\n      message = customMessage;\n    }\n\n    toast.error(message, {\n      duration: 5000,\n      position: 'top-right',\n    });\n  }\n\n  // 获取自定义错误消息\n  private static getCustomErrorMessage(errorInfo: ErrorInfo): string | null {\n    // 根据HTTP状态码\n    switch (errorInfo.status) {\n      case 400:\n        return '请求参数错误，请检查输入信息';\n      case 401:\n        return '登录已过期，请重新登录';\n      case 403:\n        return '没有权限执行此操作';\n      case 404:\n        return '请求的资源不存在';\n      case 409:\n        return '数据冲突，请刷新页面后重试';\n      case 422:\n        return '数据验证失败，请检查输入信息';\n      case 429:\n        return '请求过于频繁，请稍后再试';\n      case 500:\n        return '服务器内部错误，请稍后重试';\n      case 502:\n        return '服务暂时不可用，请稍后重试';\n      case 503:\n        return '服务维护中，请稍后重试';\n      case 504:\n        return '请求超时，请检查网络连接';\n    }\n\n    // 根据错误码\n    switch (errorInfo.code) {\n      case 'TIMEOUT':\n        return '请求超时，请检查网络连接';\n      case 'NETWORK_ERROR':\n        return '网络连接失败，请检查网络设置';\n      case 'VALIDATION_ERROR':\n        return '输入信息有误，请检查后重试';\n      case 'AUTH_FAILED':\n        return '认证失败，请重新登录';\n      case 'PERMISSION_DENIED':\n        return '权限不足，无法执行此操作';\n      case 'RESOURCE_NOT_FOUND':\n        return '请求的资源不存在';\n      case 'DUPLICATE_RESOURCE':\n        return '资源已存在，请勿重复操作';\n      case 'RATE_LIMIT_EXCEEDED':\n        return '操作过于频繁，请稍后再试';\n      default:\n        return null;\n    }\n  }\n\n  // 记录错误日志\n  private static logError(errorInfo: ErrorInfo) {\n    // 在开发环境下打印详细错误信息\n    if (process.env.NODE_ENV === 'development') {\n      console.error('API Error:', errorInfo);\n    }\n\n    // 在生产环境下可以发送到错误监控服务\n    if (process.env.NODE_ENV === 'production') {\n      // 这里可以集成Sentry、LogRocket等错误监控服务\n      // 例如：Sentry.captureException(errorInfo);\n    }\n  }\n\n  // 处理表单验证错误\n  static handleValidationErrors(errors: Record<string, string[]>): Record<string, string> {\n    const formErrors: Record<string, string> = {};\n    \n    Object.entries(errors).forEach(([field, messages]) => {\n      if (messages && messages.length > 0) {\n        formErrors[field] = messages[0];\n      }\n    });\n\n    return formErrors;\n  }\n\n  // 检查是否为认证错误\n  static isAuthError(error: ApiClientError): boolean {\n    return error.status === 401 || error.code === 'AUTH_FAILED';\n  }\n\n  // 检查是否为权限错误\n  static isPermissionError(error: ApiClientError): boolean {\n    return error.status === 403 || error.code === 'PERMISSION_DENIED';\n  }\n\n  // 检查是否为网络错误\n  static isNetworkError(error: ApiClientError): boolean {\n    return error.code === 'NETWORK_ERROR' || error.code === 'TIMEOUT';\n  }\n\n  // 检查是否为验证错误\n  static isValidationError(error: ApiClientError): boolean {\n    return error.status === 422 || error.code === 'VALIDATION_ERROR' || !!error.errors;\n  }\n}\n\n// 全局错误处理函数\nexport const handleApiError = (error: ApiClientError | Error | string) => {\n  ErrorHandler.showError(error);\n};\n\n// 成功提示函数\nexport const showSuccess = (message: string) => {\n  ErrorHandler.showSuccess(message);\n};\n\n// 警告提示函数\nexport const showWarning = (message: string) => {\n  ErrorHandler.showWarning(message);\n};\n\n// 信息提示函数\nexport const showInfo = (message: string) => {\n  ErrorHandler.showInfo(message);\n};\n\n// 加载提示函数\nexport const showLoading = (message?: string) => {\n  return ErrorHandler.showLoading(message);\n};\n\n// 关闭加载提示函数\nexport const dismissLoading = (toastId: string) => {\n  ErrorHandler.dismissLoading(toastId);\n};\n\nexport default ErrorHandler;\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAWO,MAAM;IACX,SAAS;IACT,OAAO,UAAU,KAAsC,EAAE;QACvD,IAAI;QAEJ,IAAI,OAAO,UAAU,UAAU;YAC7B,YAAY;gBAAE,SAAS;YAAM;QAC/B,OAAO,IAAI,iBAAiB,2HAAA,CAAA,iBAAc,EAAE;YAC1C,YAAY;gBACV,SAAS,MAAM,OAAO;gBACtB,MAAM,MAAM,IAAI;gBAChB,QAAQ,MAAM,MAAM;gBACpB,QAAQ,MAAM,MAAM;YACtB;QACF,OAAO;YACL,YAAY;gBAAE,SAAS,MAAM,OAAO,IAAI;YAAgB;QAC1D;QAEA,gBAAgB;QAChB,IAAI,CAAC,iBAAiB,CAAC;QAEvB,SAAS;QACT,IAAI,CAAC,QAAQ,CAAC;IAChB;IAEA,SAAS;IACT,OAAO,YAAY,OAAe,EAAE;QAClC,gQAAA,CAAA,UAAK,CAAC,OAAO,CAAC,SAAS;YACrB,UAAU;YACV,UAAU;QACZ;IACF;IAEA,SAAS;IACT,OAAO,YAAY,OAAe,EAAE;QAClC,CAAA,GAAA,gQAAA,CAAA,UAAK,AAAD,EAAE,SAAS;YACb,UAAU;YACV,UAAU;YACV,MAAM;QACR;IACF;IAEA,SAAS;IACT,OAAO,SAAS,OAAe,EAAE;QAC/B,CAAA,GAAA,gQAAA,CAAA,UAAK,AAAD,EAAE,SAAS;YACb,UAAU;YACV,UAAU;YACV,MAAM;QACR;IACF;IAEA,SAAS;IACT,OAAO,YAAY,UAAkB,YAAY,EAAE;QACjD,OAAO,gQAAA,CAAA,UAAK,CAAC,OAAO,CAAC,SAAS;YAC5B,UAAU;QACZ;IACF;IAEA,SAAS;IACT,OAAO,eAAe,OAAe,EAAE;QACrC,gQAAA,CAAA,UAAK,CAAC,OAAO,CAAC;IAChB;IAEA,YAAY;IACZ,OAAe,kBAAkB,SAAoB,EAAE;QACrD,IAAI,UAAU,UAAU,OAAO;QAE/B,oBAAoB;QACpB,IAAI,UAAU,MAAM,EAAE;YACpB,MAAM,aAAa,OAAO,MAAM,CAAC,UAAU,MAAM,CAAC,CAAC,EAAE;YACrD,IAAI,cAAc,WAAW,MAAM,GAAG,GAAG;gBACvC,UAAU,UAAU,CAAC,EAAE;YACzB;QACF;QAEA,iBAAiB;QACjB,MAAM,gBAAgB,IAAI,CAAC,qBAAqB,CAAC;QACjD,IAAI,eAAe;YACjB,UAAU;QACZ;QAEA,gQAAA,CAAA,UAAK,CAAC,KAAK,CAAC,SAAS;YACnB,UAAU;YACV,UAAU;QACZ;IACF;IAEA,YAAY;IACZ,OAAe,sBAAsB,SAAoB,EAAiB;QACxE,YAAY;QACZ,OAAQ,UAAU,MAAM;YACtB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QAEA,QAAQ;QACR,OAAQ,UAAU,IAAI;YACpB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,SAAS;IACT,OAAe,SAAS,SAAoB,EAAE;QAC5C,iBAAiB;QACjB,wCAA4C;YAC1C,QAAQ,KAAK,CAAC,cAAc;QAC9B;QAEA,oBAAoB;QACpB,IAAI,oDAAyB,cAAc;QACzC,gCAAgC;QAChC,yCAAyC;QAC3C;IACF;IAEA,WAAW;IACX,OAAO,uBAAuB,MAAgC,EAA0B;QACtF,MAAM,aAAqC,CAAC;QAE5C,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,OAAO,SAAS;YAC/C,IAAI,YAAY,SAAS,MAAM,GAAG,GAAG;gBACnC,UAAU,CAAC,MAAM,GAAG,QAAQ,CAAC,EAAE;YACjC;QACF;QAEA,OAAO;IACT;IAEA,YAAY;IACZ,OAAO,YAAY,KAAqB,EAAW;QACjD,OAAO,MAAM,MAAM,KAAK,OAAO,MAAM,IAAI,KAAK;IAChD;IAEA,YAAY;IACZ,OAAO,kBAAkB,KAAqB,EAAW;QACvD,OAAO,MAAM,MAAM,KAAK,OAAO,MAAM,IAAI,KAAK;IAChD;IAEA,YAAY;IACZ,OAAO,eAAe,KAAqB,EAAW;QACpD,OAAO,MAAM,IAAI,KAAK,mBAAmB,MAAM,IAAI,KAAK;IAC1D;IAEA,YAAY;IACZ,OAAO,kBAAkB,KAAqB,EAAW;QACvD,OAAO,MAAM,MAAM,KAAK,OAAO,MAAM,IAAI,KAAK,sBAAsB,CAAC,CAAC,MAAM,MAAM;IACpF;AACF;AAGO,MAAM,iBAAiB,CAAC;IAC7B,aAAa,SAAS,CAAC;AACzB;AAGO,MAAM,cAAc,CAAC;IAC1B,aAAa,WAAW,CAAC;AAC3B;AAGO,MAAM,cAAc,CAAC;IAC1B,aAAa,WAAW,CAAC;AAC3B;AAGO,MAAM,WAAW,CAAC;IACvB,aAAa,QAAQ,CAAC;AACxB;AAGO,MAAM,cAAc,CAAC;IAC1B,OAAO,aAAa,WAAW,CAAC;AAClC;AAGO,MAAM,iBAAiB,CAAC;IAC7B,aAAa,cAAc,CAAC;AAC9B;uCAEe", "debugId": null}}, {"offset": {"line": 469, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';\nimport { AuthAPI, AuthResponse, LoginRequest, RegisterRequest } from '@/lib/auth-api';\nimport { ApiClientError } from '@/lib/api-client';\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/error-handler';\nimport apiClient from '@/lib/api-client';\n\n// 认证状态类型\ninterface AuthState {\n  user: AuthResponse['user'] | null;\n  token: string | null;\n  isLoading: boolean;\n  isAuthenticated: boolean;\n}\n\n// 认证上下文类型\ninterface AuthContextType extends AuthState {\n  login: (data: LoginRequest) => Promise<boolean>;\n  register: (data: RegisterRequest) => Promise<boolean>;\n  logout: () => Promise<void>;\n  refreshAuth: () => Promise<void>;\n  updateUser: (user: Partial<AuthResponse['user']>) => void;\n}\n\n// 创建认证上下文\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\n// 本地存储键名\nconst TOKEN_KEY = 'auth_token';\nconst REFRESH_TOKEN_KEY = 'refresh_token';\nconst USER_KEY = 'auth_user';\n\n// 认证提供者组件\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const [authState, setAuthState] = useState<AuthState>({\n    user: null,\n    token: null,\n    isLoading: true,\n    isAuthenticated: false,\n  });\n\n  // 初始化认证状态\n  useEffect(() => {\n    initializeAuth();\n  }, []);\n\n  // 初始化认证\n  const initializeAuth = async () => {\n    try {\n      const token = localStorage.getItem(TOKEN_KEY);\n      const userStr = localStorage.getItem(USER_KEY);\n\n      if (token && userStr) {\n        const user = JSON.parse(userStr);\n        \n        // 设置API客户端的认证token\n        apiClient.setAuthToken(token);\n        \n        // 验证token是否仍然有效\n        try {\n          await AuthAPI.validateToken();\n          \n          setAuthState({\n            user,\n            token,\n            isLoading: false,\n            isAuthenticated: true,\n          });\n        } catch (error) {\n          // Token无效，尝试刷新\n          await tryRefreshToken();\n        }\n      } else {\n        setAuthState(prev => ({\n          ...prev,\n          isLoading: false,\n        }));\n      }\n    } catch (error) {\n      console.error('Initialize auth error:', error);\n      clearAuthData();\n      setAuthState(prev => ({\n        ...prev,\n        isLoading: false,\n      }));\n    }\n  };\n\n  // 尝试刷新token\n  const tryRefreshToken = async () => {\n    try {\n      const refreshToken = localStorage.getItem(REFRESH_TOKEN_KEY);\n      \n      if (refreshToken) {\n        const response = await AuthAPI.refreshToken({ refreshToken });\n        \n        if (response.success && response.data) {\n          saveAuthData(response.data);\n          setAuthState({\n            user: response.data.user,\n            token: response.data.token,\n            isLoading: false,\n            isAuthenticated: true,\n          });\n          return;\n        }\n      }\n      \n      // 刷新失败，清除认证数据\n      clearAuthData();\n      setAuthState(prev => ({\n        ...prev,\n        isLoading: false,\n      }));\n    } catch (error) {\n      clearAuthData();\n      setAuthState(prev => ({\n        ...prev,\n        isLoading: false,\n      }));\n    }\n  };\n\n  // 保存认证数据\n  const saveAuthData = (authData: AuthResponse) => {\n    localStorage.setItem(TOKEN_KEY, authData.token);\n    localStorage.setItem(USER_KEY, JSON.stringify(authData.user));\n    \n    if (authData.refreshToken) {\n      localStorage.setItem(REFRESH_TOKEN_KEY, authData.refreshToken);\n    }\n    \n    // 设置API客户端的认证token\n    apiClient.setAuthToken(authData.token);\n  };\n\n  // 清除认证数据\n  const clearAuthData = () => {\n    localStorage.removeItem(TOKEN_KEY);\n    localStorage.removeItem(REFRESH_TOKEN_KEY);\n    localStorage.removeItem(USER_KEY);\n    \n    // 移除API客户端的认证token\n    apiClient.removeAuthToken();\n  };\n\n  // 登录\n  const login = async (data: LoginRequest): Promise<boolean> => {\n    try {\n      const response = await AuthAPI.login(data);\n      \n      if (response.success && response.data) {\n        saveAuthData(response.data);\n        \n        setAuthState({\n          user: response.data.user,\n          token: response.data.token,\n          isLoading: false,\n          isAuthenticated: true,\n        });\n        \n        ErrorHandler.showSuccess('登录成功！');\n        return true;\n      }\n      \n      return false;\n    } catch (error) {\n      if (error instanceof ApiClientError) {\n        ErrorHandler.showError(error);\n      } else {\n        ErrorHandler.showError('登录失败，请重试');\n      }\n      return false;\n    }\n  };\n\n  // 注册\n  const register = async (data: RegisterRequest): Promise<boolean> => {\n    try {\n      const response = await AuthAPI.register(data);\n      \n      if (response.success && response.data) {\n        saveAuthData(response.data);\n        \n        setAuthState({\n          user: response.data.user,\n          token: response.data.token,\n          isLoading: false,\n          isAuthenticated: true,\n        });\n        \n        ErrorHandler.showSuccess('注册成功！');\n        return true;\n      }\n      \n      return false;\n    } catch (error) {\n      if (error instanceof ApiClientError) {\n        ErrorHandler.showError(error);\n      } else {\n        ErrorHandler.showError('注册失败，请重试');\n      }\n      return false;\n    }\n  };\n\n  // 登出\n  const logout = async () => {\n    try {\n      await AuthAPI.logout();\n    } catch (error) {\n      // 即使登出API失败，也要清除本地数据\n      console.error('Logout API error:', error);\n    } finally {\n      clearAuthData();\n      \n      setAuthState({\n        user: null,\n        token: null,\n        isLoading: false,\n        isAuthenticated: false,\n      });\n      \n      ErrorHandler.showSuccess('已退出登录');\n    }\n  };\n\n  // 刷新认证状态\n  const refreshAuth = async () => {\n    try {\n      const response = await AuthAPI.getCurrentUser();\n      \n      if (response.success && response.data) {\n        const updatedUser = response.data;\n        localStorage.setItem(USER_KEY, JSON.stringify(updatedUser));\n        \n        setAuthState(prev => ({\n          ...prev,\n          user: updatedUser,\n        }));\n      }\n    } catch (error) {\n      console.error('Refresh auth error:', error);\n      // 如果获取用户信息失败，可能token已过期\n      if (error instanceof ApiClientError && ErrorHandler.isAuthError(error)) {\n        await logout();\n      }\n    }\n  };\n\n  // 更新用户信息\n  const updateUser = (userData: Partial<AuthResponse['user']>) => {\n    setAuthState(prev => {\n      if (prev.user) {\n        const updatedUser = { ...prev.user, ...userData };\n        localStorage.setItem(USER_KEY, JSON.stringify(updatedUser));\n        \n        return {\n          ...prev,\n          user: updatedUser,\n        };\n      }\n      return prev;\n    });\n  };\n\n  const contextValue: AuthContextType = {\n    ...authState,\n    login,\n    register,\n    logout,\n    refreshAuth,\n    updateUser,\n  };\n\n  return (\n    <AuthContext.Provider value={contextValue}>\n      {children}\n    </AuthContext.Provider>\n  );\n}\n\n// 使用认证上下文的Hook\nexport function useAuth() {\n  const context = useContext(AuthContext);\n  \n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  \n  return context;\n}\n\n// 检查是否已认证的Hook\nexport function useRequireAuth() {\n  const { isAuthenticated, isLoading } = useAuth();\n  \n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      // 可以在这里重定向到登录页面\n      ErrorHandler.showWarning('请先登录');\n    }\n  }, [isAuthenticated, isLoading]);\n  \n  return { isAuthenticated, isLoading };\n}\n\nexport default AuthContext;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;;AAyBA,UAAU;AACV,MAAM,4BAAc,CAAA,GAAA,oUAAA,CAAA,gBAAa,AAAD,EAA+B;AAE/D,SAAS;AACT,MAAM,YAAY;AAClB,MAAM,oBAAoB;AAC1B,MAAM,WAAW;AAOV,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAa;QACpD,MAAM;QACN,OAAO;QACP,WAAW;QACX,iBAAiB;IACnB;IAEA,UAAU;IACV,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,QAAQ;IACR,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,MAAM,UAAU,aAAa,OAAO,CAAC;YAErC,IAAI,SAAS,SAAS;gBACpB,MAAM,OAAO,KAAK,KAAK,CAAC;gBAExB,mBAAmB;gBACnB,2HAAA,CAAA,UAAS,CAAC,YAAY,CAAC;gBAEvB,gBAAgB;gBAChB,IAAI;oBACF,MAAM,yHAAA,CAAA,UAAO,CAAC,aAAa;oBAE3B,aAAa;wBACX;wBACA;wBACA,WAAW;wBACX,iBAAiB;oBACnB;gBACF,EAAE,OAAO,OAAO;oBACd,eAAe;oBACf,MAAM;gBACR;YACF,OAAO;gBACL,aAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,WAAW;oBACb,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC;YACA,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,WAAW;gBACb,CAAC;QACH;IACF;IAEA,YAAY;IACZ,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,eAAe,aAAa,OAAO,CAAC;YAE1C,IAAI,cAAc;gBAChB,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAO,CAAC,YAAY,CAAC;oBAAE;gBAAa;gBAE3D,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;oBACrC,aAAa,SAAS,IAAI;oBAC1B,aAAa;wBACX,MAAM,SAAS,IAAI,CAAC,IAAI;wBACxB,OAAO,SAAS,IAAI,CAAC,KAAK;wBAC1B,WAAW;wBACX,iBAAiB;oBACnB;oBACA;gBACF;YACF;YAEA,cAAc;YACd;YACA,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,WAAW;gBACb,CAAC;QACH,EAAE,OAAO,OAAO;YACd;YACA,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,WAAW;gBACb,CAAC;QACH;IACF;IAEA,SAAS;IACT,MAAM,eAAe,CAAC;QACpB,aAAa,OAAO,CAAC,WAAW,SAAS,KAAK;QAC9C,aAAa,OAAO,CAAC,UAAU,KAAK,SAAS,CAAC,SAAS,IAAI;QAE3D,IAAI,SAAS,YAAY,EAAE;YACzB,aAAa,OAAO,CAAC,mBAAmB,SAAS,YAAY;QAC/D;QAEA,mBAAmB;QACnB,2HAAA,CAAA,UAAS,CAAC,YAAY,CAAC,SAAS,KAAK;IACvC;IAEA,SAAS;IACT,MAAM,gBAAgB;QACpB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QAExB,mBAAmB;QACnB,2HAAA,CAAA,UAAS,CAAC,eAAe;IAC3B;IAEA,KAAK;IACL,MAAM,QAAQ,OAAO;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;YAErC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,aAAa,SAAS,IAAI;gBAE1B,aAAa;oBACX,MAAM,SAAS,IAAI,CAAC,IAAI;oBACxB,OAAO,SAAS,IAAI,CAAC,KAAK;oBAC1B,WAAW;oBACX,iBAAiB;gBACnB;gBAEA,8HAAA,CAAA,eAAY,CAAC,WAAW,CAAC;gBACzB,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,2HAAA,CAAA,iBAAc,EAAE;gBACnC,8HAAA,CAAA,eAAY,CAAC,SAAS,CAAC;YACzB,OAAO;gBACL,8HAAA,CAAA,eAAY,CAAC,SAAS,CAAC;YACzB;YACA,OAAO;QACT;IACF;IAEA,KAAK;IACL,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;YAExC,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,aAAa,SAAS,IAAI;gBAE1B,aAAa;oBACX,MAAM,SAAS,IAAI,CAAC,IAAI;oBACxB,OAAO,SAAS,IAAI,CAAC,KAAK;oBAC1B,WAAW;oBACX,iBAAiB;gBACnB;gBAEA,8HAAA,CAAA,eAAY,CAAC,WAAW,CAAC;gBACzB,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,IAAI,iBAAiB,2HAAA,CAAA,iBAAc,EAAE;gBACnC,8HAAA,CAAA,eAAY,CAAC,SAAS,CAAC;YACzB,OAAO;gBACL,8HAAA,CAAA,eAAY,CAAC,SAAS,CAAC;YACzB;YACA,OAAO;QACT;IACF;IAEA,KAAK;IACL,MAAM,SAAS;QACb,IAAI;YACF,MAAM,yHAAA,CAAA,UAAO,CAAC,MAAM;QACtB,EAAE,OAAO,OAAO;YACd,qBAAqB;YACrB,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR;YAEA,aAAa;gBACX,MAAM;gBACN,OAAO;gBACP,WAAW;gBACX,iBAAiB;YACnB;YAEA,8HAAA,CAAA,eAAY,CAAC,WAAW,CAAC;QAC3B;IACF;IAEA,SAAS;IACT,MAAM,cAAc;QAClB,IAAI;YACF,MAAM,WAAW,MAAM,yHAAA,CAAA,UAAO,CAAC,cAAc;YAE7C,IAAI,SAAS,OAAO,IAAI,SAAS,IAAI,EAAE;gBACrC,MAAM,cAAc,SAAS,IAAI;gBACjC,aAAa,OAAO,CAAC,UAAU,KAAK,SAAS,CAAC;gBAE9C,aAAa,CAAA,OAAQ,CAAC;wBACpB,GAAG,IAAI;wBACP,MAAM;oBACR,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,wBAAwB;YACxB,IAAI,iBAAiB,2HAAA,CAAA,iBAAc,IAAI,8HAAA,CAAA,eAAY,CAAC,WAAW,CAAC,QAAQ;gBACtE,MAAM;YACR;QACF;IACF;IAEA,SAAS;IACT,MAAM,aAAa,CAAC;QAClB,aAAa,CAAA;YACX,IAAI,KAAK,IAAI,EAAE;gBACb,MAAM,cAAc;oBAAE,GAAG,KAAK,IAAI;oBAAE,GAAG,QAAQ;gBAAC;gBAChD,aAAa,OAAO,CAAC,UAAU,KAAK,SAAS,CAAC;gBAE9C,OAAO;oBACL,GAAG,IAAI;oBACP,MAAM;gBACR;YACF;YACA,OAAO;QACT;IACF;IAEA,MAAM,eAAgC;QACpC,GAAG,SAAS;QACZ;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6WAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,oUAAA,CAAA,aAAU,AAAD,EAAE;IAE3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG;IAEvC,CAAA,GAAA,oUAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;YAClC,gBAAgB;YAChB,8HAAA,CAAA,eAAY,CAAC,WAAW,CAAC;QAC3B;IACF,GAAG;QAAC;QAAiB;KAAU;IAE/B,OAAO;QAAE;QAAiB;IAAU;AACtC;uCAEe", "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/node_modules/.pnpm/next%4015.4.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": "AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;;KAElC;IACL,IAAIF,QAAQC,GAAG,CAACK,yBAAyB,EAAE;;SAcpC;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;QAGT,OAAO;;IAOT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/node_modules/.pnpm/next%4015.4.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,uMACRC,QAAQ,CAAC,YAAY,CAAEC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 770, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/node_modules/.pnpm/goober%402.1.16_csstype%403.1.3/node_modules/goober/dist/goober.modern.js"], "sourcesContent": ["let e={data:\"\"},t=t=>\"object\"==typeof window?((t?t.querySelector(\"#_goober\"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement(\"style\")),{innerHTML:\" \",id:\"_goober\"})).firstChild:t||e,r=e=>{let r=t(e),l=r.data;return r.data=\"\",l},l=/(?:([\\u0080-\\uFFFF\\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\\s*)/g,a=/\\/\\*[^]*?\\*\\/|  +/g,n=/\\n+/g,o=(e,t)=>{let r=\"\",l=\"\",a=\"\";for(let n in e){let c=e[n];\"@\"==n[0]?\"i\"==n[1]?r=n+\" \"+c+\";\":l+=\"f\"==n[1]?o(c,n):n+\"{\"+o(c,\"k\"==n[1]?\"\":t)+\"}\":\"object\"==typeof c?l+=o(c,t?t.replace(/([^,])+/g,e=>n.replace(/([^,]*:\\S+\\([^)]*\\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+\" \"+t:t)):n):null!=c&&(n=/^--/.test(n)?n:n.replace(/[A-Z]/g,\"-$&\").toLowerCase(),a+=o.p?o.p(n,c):n+\":\"+c+\";\")}return r+(t&&a?t+\"{\"+a+\"}\":a)+l},c={},s=e=>{if(\"object\"==typeof e){let t=\"\";for(let r in e)t+=r+s(e[r]);return t}return e},i=(e,t,r,i,p)=>{let u=s(e),d=c[u]||(c[u]=(e=>{let t=0,r=11;for(;t<e.length;)r=101*r+e.charCodeAt(t++)>>>0;return\"go\"+r})(u));if(!c[d]){let t=u!==e?e:(e=>{let t,r,o=[{}];for(;t=l.exec(e.replace(a,\"\"));)t[4]?o.shift():t[3]?(r=t[3].replace(n,\" \").trim(),o.unshift(o[0][r]=o[0][r]||{})):o[0][t[1]]=t[2].replace(n,\" \").trim();return o[0]})(e);c[d]=o(p?{[\"@keyframes \"+d]:t}:t,r?\"\":\".\"+d)}let f=r&&c.g?c.g:null;return r&&(c.g=c[d]),((e,t,r,l)=>{l?t.data=t.data.replace(l,e):-1===t.data.indexOf(e)&&(t.data=r?e+t.data:t.data+e)})(c[d],t,i,f),d},p=(e,t,r)=>e.reduce((e,l,a)=>{let n=t[a];if(n&&n.call){let e=n(r),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;n=t?\".\"+t:e&&\"object\"==typeof e?e.props?\"\":o(e,\"\"):!1===e?\"\":e}return e+l+(null==n?\"\":n)},\"\");function u(e){let r=this||{},l=e.call?e(r.p):e;return i(l.unshift?l.raw?p(l,[].slice.call(arguments,1),r.p):l.reduce((e,t)=>Object.assign(e,t&&t.call?t(r.p):t),{}):l,t(r.target),r.g,r.o,r.k)}let d,f,g,b=u.bind({g:1}),h=u.bind({k:1});function m(e,t,r,l){o.p=t,d=e,f=r,g=l}function j(e,t){let r=this||{};return function(){let l=arguments;function a(n,o){let c=Object.assign({},n),s=c.className||a.className;r.p=Object.assign({theme:f&&f()},c),r.o=/ *go\\d+/.test(s),c.className=u.apply(r,l)+(s?\" \"+s:\"\"),t&&(c.ref=o);let i=e;return e[0]&&(i=c.as||e,delete c.as),g&&i[0]&&g(c),d(i,c)}return t?t(a):a}}export{u as css,r as extractCss,b as glob,h as keyframes,m as setup,j as styled};\n"], "names": [], "mappings": ";;;;;;;;AAAA,IAAI,IAAE;IAAC,MAAK;AAAE,GAAE,IAAE,CAAA,IAAG,sCAAwB,0BAAyK,KAAG,GAAE,IAAE,CAAA;IAAI,IAAI,IAAE,EAAE,IAAG,IAAE,EAAE,IAAI;IAAC,OAAO,EAAE,IAAI,GAAC,IAAG;AAAC,GAAE,IAAE,qEAAoE,IAAE,sBAAqB,IAAE,QAAO,IAAE,CAAC,GAAE;IAAK,IAAI,IAAE,IAAG,IAAE,IAAG,IAAE;IAAG,IAAI,IAAI,KAAK,EAAE;QAAC,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,OAAK,CAAC,CAAC,EAAE,GAAC,OAAK,CAAC,CAAC,EAAE,GAAC,IAAE,IAAE,MAAI,IAAE,MAAI,KAAG,OAAK,CAAC,CAAC,EAAE,GAAC,EAAE,GAAE,KAAG,IAAE,MAAI,EAAE,GAAE,OAAK,CAAC,CAAC,EAAE,GAAC,KAAG,KAAG,MAAI,YAAU,OAAO,IAAE,KAAG,EAAE,GAAE,IAAE,EAAE,OAAO,CAAC,YAAW,CAAA,IAAG,EAAE,OAAO,CAAC,iCAAgC,CAAA,IAAG,IAAI,IAAI,CAAC,KAAG,EAAE,OAAO,CAAC,MAAK,KAAG,IAAE,IAAE,MAAI,IAAE,MAAI,KAAG,QAAM,KAAG,CAAC,IAAE,MAAM,IAAI,CAAC,KAAG,IAAE,EAAE,OAAO,CAAC,UAAS,OAAO,WAAW,IAAG,KAAG,EAAE,CAAC,GAAC,EAAE,CAAC,CAAC,GAAE,KAAG,IAAE,MAAI,IAAE,GAAG;IAAC;IAAC,OAAO,IAAE,CAAC,KAAG,IAAE,IAAE,MAAI,IAAE,MAAI,CAAC,IAAE;AAAC,GAAE,IAAE,CAAC,GAAE,IAAE,CAAA;IAAI,IAAG,YAAU,OAAO,GAAE;QAAC,IAAI,IAAE;QAAG,IAAI,IAAI,KAAK,EAAE,KAAG,IAAE,EAAE,CAAC,CAAC,EAAE;QAAE,OAAO;IAAC;IAAC,OAAO;AAAC,GAAE,IAAE,CAAC,GAAE,GAAE,GAAE,GAAE;IAAK,IAAI,IAAE,EAAE,IAAG,IAAE,CAAC,CAAC,EAAE,IAAE,CAAC,CAAC,CAAC,EAAE,GAAC,CAAC,CAAA;QAAI,IAAI,IAAE,GAAE,IAAE;QAAG,MAAK,IAAE,EAAE,MAAM,EAAE,IAAE,MAAI,IAAE,EAAE,UAAU,CAAC,SAAO;QAAE,OAAM,OAAK;IAAC,CAAC,EAAE,EAAE;IAAE,IAAG,CAAC,CAAC,CAAC,EAAE,EAAC;QAAC,IAAI,IAAE,MAAI,IAAE,IAAE,CAAC,CAAA;YAAI,IAAI,GAAE,GAAE,IAAE;gBAAC,CAAC;aAAE;YAAC,MAAK,IAAE,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC,GAAE,MAAM,CAAC,CAAC,EAAE,GAAC,EAAE,KAAK,KAAG,CAAC,CAAC,EAAE,GAAC,CAAC,IAAE,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAE,KAAK,IAAI,IAAG,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,GAAC,CAAC,CAAC,EAAE,CAAC,EAAE,IAAE,CAAC,EAAE,IAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,GAAE,KAAK,IAAI;YAAG,OAAO,CAAC,CAAC,EAAE;QAAA,CAAC,EAAE;QAAG,CAAC,CAAC,EAAE,GAAC,EAAE,IAAE;YAAC,CAAC,gBAAc,EAAE,EAAC;QAAC,IAAE,GAAE,IAAE,KAAG,MAAI;IAAE;IAAC,IAAI,IAAE,KAAG,EAAE,CAAC,GAAC,EAAE,CAAC,GAAC;IAAK,OAAO,KAAG,CAAC,EAAE,CAAC,GAAC,CAAC,CAAC,EAAE,GAAE,CAAC,CAAC,GAAE,GAAE,GAAE;QAAK,IAAE,EAAE,IAAI,GAAC,EAAE,IAAI,CAAC,OAAO,CAAC,GAAE,KAAG,CAAC,MAAI,EAAE,IAAI,CAAC,OAAO,CAAC,MAAI,CAAC,EAAE,IAAI,GAAC,IAAE,IAAE,EAAE,IAAI,GAAC,EAAE,IAAI,GAAC,CAAC;IAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAC,GAAE,GAAE,IAAG;AAAC,GAAE,IAAE,CAAC,GAAE,GAAE,IAAI,EAAE,MAAM,CAAC,CAAC,GAAE,GAAE;QAAK,IAAI,IAAE,CAAC,CAAC,EAAE;QAAC,IAAG,KAAG,EAAE,IAAI,EAAC;YAAC,IAAI,IAAE,EAAE,IAAG,IAAE,KAAG,EAAE,KAAK,IAAE,EAAE,KAAK,CAAC,SAAS,IAAE,MAAM,IAAI,CAAC,MAAI;YAAE,IAAE,IAAE,MAAI,IAAE,KAAG,YAAU,OAAO,IAAE,EAAE,KAAK,GAAC,KAAG,EAAE,GAAE,MAAI,CAAC,MAAI,IAAE,KAAG;QAAC;QAAC,OAAO,IAAE,IAAE,CAAC,QAAM,IAAE,KAAG,CAAC;IAAC,GAAE;AAAI,SAAS,EAAE,CAAC;IAAE,IAAI,IAAE,IAAI,IAAE,CAAC,GAAE,IAAE,EAAE,IAAI,GAAC,EAAE,EAAE,CAAC,IAAE;IAAE,OAAO,EAAE,EAAE,OAAO,GAAC,EAAE,GAAG,GAAC,EAAE,GAAE,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,WAAU,IAAG,EAAE,CAAC,IAAE,EAAE,MAAM,CAAC,CAAC,GAAE,IAAI,OAAO,MAAM,CAAC,GAAE,KAAG,EAAE,IAAI,GAAC,EAAE,EAAE,CAAC,IAAE,IAAG,CAAC,KAAG,GAAE,EAAE,EAAE,MAAM,GAAE,EAAE,CAAC,EAAC,EAAE,CAAC,EAAC,EAAE,CAAC;AAAC;AAAC,IAAI,GAAE,GAAE,GAAE,IAAE,EAAE,IAAI,CAAC;IAAC,GAAE;AAAC,IAAG,IAAE,EAAE,IAAI,CAAC;IAAC,GAAE;AAAC;AAAG,SAAS,EAAE,CAAC,EAAC,CAAC,EAAC,CAAC,EAAC,CAAC;IAAE,EAAE,CAAC,GAAC,GAAE,IAAE,GAAE,IAAE,GAAE,IAAE;AAAC;AAAC,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,IAAI,IAAE,IAAI,IAAE,CAAC;IAAE,OAAO;QAAW,IAAI,IAAE;QAAU,SAAS,EAAE,CAAC,EAAC,CAAC;YAAE,IAAI,IAAE,OAAO,MAAM,CAAC,CAAC,GAAE,IAAG,IAAE,EAAE,SAAS,IAAE,EAAE,SAAS;YAAC,EAAE,CAAC,GAAC,OAAO,MAAM,CAAC;gBAAC,OAAM,KAAG;YAAG,GAAE,IAAG,EAAE,CAAC,GAAC,UAAU,IAAI,CAAC,IAAG,EAAE,SAAS,GAAC,EAAE,KAAK,CAAC,GAAE,KAAG,CAAC,IAAE,MAAI,IAAE,EAAE,GAAE,KAAG,CAAC,EAAE,GAAG,GAAC,CAAC;YAAE,IAAI,IAAE;YAAE,OAAO,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,EAAE,EAAE,IAAE,GAAE,OAAO,EAAE,EAAE,GAAE,KAAG,CAAC,CAAC,EAAE,IAAE,EAAE,IAAG,EAAE,GAAE;QAAE;QAAC,OAAO,IAAE,EAAE,KAAG;IAAC;AAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 859, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/node_modules/.pnpm/react-hot-toast%402.5.2_react_4ea650e5376fd58cd959e858ce6285c0/node_modules/react-hot-toast/src/core/types.ts", "file:///E:/7mouthMission/1500/my-nextjs-app/node_modules/.pnpm/react-hot-toast%402.5.2_react_4ea650e5376fd58cd959e858ce6285c0/node_modules/react-hot-toast/src/core/utils.ts", "file:///E:/7mouthMission/1500/my-nextjs-app/node_modules/.pnpm/react-hot-toast%402.5.2_react_4ea650e5376fd58cd959e858ce6285c0/node_modules/react-hot-toast/src/core/store.ts", "file:///E:/7mouthMission/1500/my-nextjs-app/node_modules/.pnpm/react-hot-toast%402.5.2_react_4ea650e5376fd58cd959e858ce6285c0/node_modules/react-hot-toast/src/core/toast.ts", "file:///E:/7mouthMission/1500/my-nextjs-app/node_modules/.pnpm/react-hot-toast%402.5.2_react_4ea650e5376fd58cd959e858ce6285c0/node_modules/react-hot-toast/src/core/use-toaster.ts", "file:///E:/7mouthMission/1500/my-nextjs-app/node_modules/.pnpm/react-hot-toast%402.5.2_react_4ea650e5376fd58cd959e858ce6285c0/node_modules/react-hot-toast/src/components/toast-bar.tsx", "file:///E:/7mouthMission/1500/my-nextjs-app/node_modules/.pnpm/react-hot-toast%402.5.2_react_4ea650e5376fd58cd959e858ce6285c0/node_modules/react-hot-toast/src/components/toast-icon.tsx", "file:///E:/7mouthMission/1500/my-nextjs-app/node_modules/.pnpm/react-hot-toast%402.5.2_react_4ea650e5376fd58cd959e858ce6285c0/node_modules/react-hot-toast/src/components/error.tsx", "file:///E:/7mouthMission/1500/my-nextjs-app/node_modules/.pnpm/react-hot-toast%402.5.2_react_4ea650e5376fd58cd959e858ce6285c0/node_modules/react-hot-toast/src/components/loader.tsx", "file:///E:/7mouthMission/1500/my-nextjs-app/node_modules/.pnpm/react-hot-toast%402.5.2_react_4ea650e5376fd58cd959e858ce6285c0/node_modules/react-hot-toast/src/components/checkmark.tsx", "file:///E:/7mouthMission/1500/my-nextjs-app/node_modules/.pnpm/react-hot-toast%402.5.2_react_4ea650e5376fd58cd959e858ce6285c0/node_modules/react-hot-toast/src/components/toaster.tsx", "file:///E:/7mouthMission/1500/my-nextjs-app/node_modules/.pnpm/react-hot-toast%402.5.2_react_4ea650e5376fd58cd959e858ce6285c0/node_modules/react-hot-toast/src/index.ts"], "sourcesContent": ["import { CSSProperties } from 'react';\n\nexport type ToastType = 'success' | 'error' | 'loading' | 'blank' | 'custom';\nexport type ToastPosition =\n  | 'top-left'\n  | 'top-center'\n  | 'top-right'\n  | 'bottom-left'\n  | 'bottom-center'\n  | 'bottom-right';\n\nexport type Renderable = React.ReactElement | string | null;\n\nexport interface IconTheme {\n  primary: string;\n  secondary: string;\n}\n\nexport type ValueFunction<TValue, TArg> = (arg: TArg) => TValue;\nexport type ValueOrFunction<TValue, TArg> =\n  | TValue\n  | ValueFunction<TValue, TArg>;\n\nconst isFunction = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>\n): valOrFunction is ValueFunction<TValue, TArg> =>\n  typeof valOrFunction === 'function';\n\nexport const resolveValue = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>,\n  arg: TArg\n): TValue => (isFunction(valOrFunction) ? valOrFunction(arg) : valOrFunction);\n\nexport interface Toast {\n  type: ToastType;\n  id: string;\n  message: ValueOrFunction<Renderable, Toast>;\n  icon?: Renderable;\n  duration?: number;\n  pauseDuration: number;\n  position?: ToastPosition;\n  removeDelay?: number;\n\n  ariaProps: {\n    role: 'status' | 'alert';\n    'aria-live': 'assertive' | 'off' | 'polite';\n  };\n\n  style?: CSSProperties;\n  className?: string;\n  iconTheme?: IconTheme;\n\n  createdAt: number;\n  visible: boolean;\n  dismissed: boolean;\n  height?: number;\n}\n\nexport type ToastOptions = Partial<\n  Pick<\n    Toast,\n    | 'id'\n    | 'icon'\n    | 'duration'\n    | 'ariaProps'\n    | 'className'\n    | 'style'\n    | 'position'\n    | 'iconTheme'\n    | 'removeDelay'\n  >\n>;\n\nexport type DefaultToastOptions = ToastOptions & {\n  [key in ToastType]?: ToastOptions;\n};\n\nexport interface ToasterProps {\n  position?: ToastPosition;\n  toastOptions?: DefaultToastOptions;\n  reverseOrder?: boolean;\n  gutter?: number;\n  containerStyle?: React.CSSProperties;\n  containerClassName?: string;\n  children?: (toast: Toast) => React.ReactElement;\n}\n\nexport interface ToastWrapperProps {\n  id: string;\n  className?: string;\n  style?: React.CSSProperties;\n  onHeightUpdate: (id: string, height: number) => void;\n  children?: React.ReactNode;\n}\n", "export const genId = (() => {\n  let count = 0;\n  return () => {\n    return (++count).toString();\n  };\n})();\n\nexport const prefersReducedMotion = (() => {\n  // Cache result\n  let shouldReduceMotion: boolean | undefined = undefined;\n\n  return () => {\n    if (shouldReduceMotion === undefined && typeof window !== 'undefined') {\n      const mediaQuery = matchMedia('(prefers-reduced-motion: reduce)');\n      shouldReduceMotion = !mediaQuery || mediaQuery.matches;\n    }\n    return shouldReduceMotion;\n  };\n})();\n", "import { useEffect, useState, useRef } from 'react';\nimport { DefaultToastOptions, Toast, ToastType } from './types';\n\nconst TOAST_LIMIT = 20;\n\nexport enum ActionType {\n  ADD_TOAST,\n  UPDATE_TOAST,\n  UPSERT_TOAST,\n  DISMISS_TOAST,\n  REMOVE_TOAST,\n  START_PAUSE,\n  END_PAUSE,\n}\n\ntype Action =\n  | {\n      type: ActionType.ADD_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPSERT_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPDATE_TOAST;\n      toast: Partial<Toast>;\n    }\n  | {\n      type: ActionType.DISMISS_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.REMOVE_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.START_PAUSE;\n      time: number;\n    }\n  | {\n      type: ActionType.END_PAUSE;\n      time: number;\n    };\n\ninterface State {\n  toasts: Toast[];\n  pausedAt: number | undefined;\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case ActionType.ADD_TOAST:\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      };\n\n    case ActionType.UPDATE_TOAST:\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      };\n\n    case ActionType.UPSERT_TOAST:\n      const { toast } = action;\n      return reducer(state, {\n        type: state.toasts.find((t) => t.id === toast.id)\n          ? ActionType.UPDATE_TOAST\n          : ActionType.ADD_TOAST,\n        toast,\n      });\n\n    case ActionType.DISMISS_TOAST:\n      const { toastId } = action;\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                dismissed: true,\n                visible: false,\n              }\n            : t\n        ),\n      };\n    case ActionType.REMOVE_TOAST:\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n\n    case ActionType.START_PAUSE:\n      return {\n        ...state,\n        pausedAt: action.time,\n      };\n\n    case ActionType.END_PAUSE:\n      const diff = action.time - (state.pausedAt || 0);\n\n      return {\n        ...state,\n        pausedAt: undefined,\n        toasts: state.toasts.map((t) => ({\n          ...t,\n          pauseDuration: t.pauseDuration + diff,\n        })),\n      };\n  }\n};\n\nconst listeners: Array<(state: State) => void> = [];\n\nlet memoryState: State = { toasts: [], pausedAt: undefined };\n\nexport const dispatch = (action: Action) => {\n  memoryState = reducer(memoryState, action);\n  listeners.forEach((listener) => {\n    listener(memoryState);\n  });\n};\n\nexport const defaultTimeouts: {\n  [key in ToastType]: number;\n} = {\n  blank: 4000,\n  error: 4000,\n  success: 2000,\n  loading: Infinity,\n  custom: 4000,\n};\n\nexport const useStore = (toastOptions: DefaultToastOptions = {}): State => {\n  const [state, setState] = useState<State>(memoryState);\n  const initial = useRef(memoryState);\n\n  // TODO: Switch to useSyncExternalStore when targeting React 18+\n  useEffect(() => {\n    if (initial.current !== memoryState) {\n      setState(memoryState);\n    }\n    listeners.push(setState);\n    return () => {\n      const index = listeners.indexOf(setState);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, []);\n\n  const mergedToasts = state.toasts.map((t) => ({\n    ...toastOptions,\n    ...toastOptions[t.type],\n    ...t,\n    removeDelay:\n      t.removeDelay ||\n      toastOptions[t.type]?.removeDelay ||\n      toastOptions?.removeDelay,\n    duration:\n      t.duration ||\n      toastOptions[t.type]?.duration ||\n      toastOptions?.duration ||\n      defaultTimeouts[t.type],\n    style: {\n      ...toastOptions.style,\n      ...toastOptions[t.type]?.style,\n      ...t.style,\n    },\n  }));\n\n  return {\n    ...state,\n    toasts: mergedToasts,\n  };\n};\n", "import {\n  Renderable,\n  Toast,\n  ToastOptions,\n  ToastType,\n  DefaultToastOptions,\n  ValueOrFunction,\n  resolveValue,\n} from './types';\nimport { genId } from './utils';\nimport { dispatch, ActionType } from './store';\n\ntype Message = ValueOrFunction<Renderable, Toast>;\n\ntype ToastHandler = (message: Message, options?: ToastOptions) => string;\n\nconst createToast = (\n  message: Message,\n  type: ToastType = 'blank',\n  opts?: ToastOptions\n): Toast => ({\n  createdAt: Date.now(),\n  visible: true,\n  dismissed: false,\n  type,\n  ariaProps: {\n    role: 'status',\n    'aria-live': 'polite',\n  },\n  message,\n  pauseDuration: 0,\n  ...opts,\n  id: opts?.id || genId(),\n});\n\nconst createHandler =\n  (type?: ToastType): ToastHandler =>\n  (message, options) => {\n    const toast = createToast(message, type, options);\n    dispatch({ type: ActionType.UPSERT_TOAST, toast });\n    return toast.id;\n  };\n\nconst toast = (message: Message, opts?: ToastOptions) =>\n  createHandler('blank')(message, opts);\n\ntoast.error = createHandler('error');\ntoast.success = createHandler('success');\ntoast.loading = createHandler('loading');\ntoast.custom = createHandler('custom');\n\ntoast.dismiss = (toastId?: string) => {\n  dispatch({\n    type: ActionType.DISMISS_TOAST,\n    toastId,\n  });\n};\n\ntoast.remove = (toastId?: string) =>\n  dispatch({ type: ActionType.REMOVE_TOAST, toastId });\n\ntoast.promise = <T>(\n  promise: Promise<T> | (() => Promise<T>),\n  msgs: {\n    loading: Renderable;\n    success?: ValueOrFunction<Renderable, T>;\n    error?: ValueOrFunction<Renderable, any>;\n  },\n  opts?: DefaultToastOptions\n) => {\n  const id = toast.loading(msgs.loading, { ...opts, ...opts?.loading });\n\n  if (typeof promise === 'function') {\n    promise = promise();\n  }\n\n  promise\n    .then((p) => {\n      const successMessage = msgs.success\n        ? resolveValue(msgs.success, p)\n        : undefined;\n\n      if (successMessage) {\n        toast.success(successMessage, {\n          id,\n          ...opts,\n          ...opts?.success,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n      return p;\n    })\n    .catch((e) => {\n      const errorMessage = msgs.error ? resolveValue(msgs.error, e) : undefined;\n\n      if (errorMessage) {\n        toast.error(errorMessage, {\n          id,\n          ...opts,\n          ...opts?.error,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n    });\n\n  return promise;\n};\n\nexport { toast };\n", "import { useEffect, useCallback } from 'react';\nimport { dispatch, ActionType, useStore } from './store';\nimport { toast } from './toast';\nimport { DefaultToastOptions, Toast, ToastPosition } from './types';\n\nconst updateHeight = (toastId: string, height: number) => {\n  dispatch({\n    type: ActionType.UPDATE_TOAST,\n    toast: { id: toastId, height },\n  });\n};\nconst startPause = () => {\n  dispatch({\n    type: ActionType.START_PAUSE,\n    time: Date.now(),\n  });\n};\n\nconst toastTimeouts = new Map<Toast['id'], ReturnType<typeof setTimeout>>();\n\nexport const REMOVE_DELAY = 1000;\n\nconst addToRemoveQueue = (toastId: string, removeDelay = REMOVE_DELAY) => {\n  if (toastTimeouts.has(toastId)) {\n    return;\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId);\n    dispatch({\n      type: ActionType.REMOVE_TOAST,\n      toastId: toastId,\n    });\n  }, removeDelay);\n\n  toastTimeouts.set(toastId, timeout);\n};\n\nexport const useToaster = (toastOptions?: DefaultToastOptions) => {\n  const { toasts, pausedAt } = useStore(toastOptions);\n\n  useEffect(() => {\n    if (pausedAt) {\n      return;\n    }\n\n    const now = Date.now();\n    const timeouts = toasts.map((t) => {\n      if (t.duration === Infinity) {\n        return;\n      }\n\n      const durationLeft =\n        (t.duration || 0) + t.pauseDuration - (now - t.createdAt);\n\n      if (durationLeft < 0) {\n        if (t.visible) {\n          toast.dismiss(t.id);\n        }\n        return;\n      }\n      return setTimeout(() => toast.dismiss(t.id), durationLeft);\n    });\n\n    return () => {\n      timeouts.forEach((timeout) => timeout && clearTimeout(timeout));\n    };\n  }, [toasts, pausedAt]);\n\n  const endPause = useCallback(() => {\n    if (pausedAt) {\n      dispatch({ type: ActionType.END_PAUSE, time: Date.now() });\n    }\n  }, [pausedAt]);\n\n  const calculateOffset = useCallback(\n    (\n      toast: Toast,\n      opts?: {\n        reverseOrder?: boolean;\n        gutter?: number;\n        defaultPosition?: ToastPosition;\n      }\n    ) => {\n      const { reverseOrder = false, gutter = 8, defaultPosition } = opts || {};\n\n      const relevantToasts = toasts.filter(\n        (t) =>\n          (t.position || defaultPosition) ===\n            (toast.position || defaultPosition) && t.height\n      );\n      const toastIndex = relevantToasts.findIndex((t) => t.id === toast.id);\n      const toastsBefore = relevantToasts.filter(\n        (toast, i) => i < toastIndex && toast.visible\n      ).length;\n\n      const offset = relevantToasts\n        .filter((t) => t.visible)\n        .slice(...(reverseOrder ? [toastsBefore + 1] : [0, toastsBefore]))\n        .reduce((acc, t) => acc + (t.height || 0) + gutter, 0);\n\n      return offset;\n    },\n    [toasts]\n  );\n\n  useEffect(() => {\n    // Add dismissed toasts to remove queue\n    toasts.forEach((toast) => {\n      if (toast.dismissed) {\n        addToRemoveQueue(toast.id, toast.removeDelay);\n      } else {\n        // If toast becomes visible again, remove it from the queue\n        const timeout = toastTimeouts.get(toast.id);\n        if (timeout) {\n          clearTimeout(timeout);\n          toastTimeouts.delete(toast.id);\n        }\n      }\n    });\n  }, [toasts]);\n\n  return {\n    toasts,\n    handlers: {\n      updateHeight,\n      startPause,\n      endPause,\n      calculateOffset,\n    },\n  };\n};\n", "import * as React from 'react';\nimport { styled, keyframes } from 'goober';\n\nimport { Toast, ToastPosition, resolveValue, Renderable } from '../core/types';\nimport { ToastIcon } from './toast-icon';\nimport { prefersReducedMotion } from '../core/utils';\n\nconst enterAnimation = (factor: number) => `\n0% {transform: translate3d(0,${factor * -200}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`;\n\nconst exitAnimation = (factor: number) => `\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${factor * -150}%,-1px) scale(.6); opacity:0;}\n`;\n\nconst fadeInAnimation = `0%{opacity:0;} 100%{opacity:1;}`;\nconst fadeOutAnimation = `0%{opacity:1;} 100%{opacity:0;}`;\n\nconst ToastBarBase = styled('div')`\n  display: flex;\n  align-items: center;\n  background: #fff;\n  color: #363636;\n  line-height: 1.3;\n  will-change: transform;\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\n  max-width: 350px;\n  pointer-events: auto;\n  padding: 8px 10px;\n  border-radius: 8px;\n`;\n\nconst Message = styled('div')`\n  display: flex;\n  justify-content: center;\n  margin: 4px 10px;\n  color: inherit;\n  flex: 1 1 auto;\n  white-space: pre-line;\n`;\n\ninterface ToastBarProps {\n  toast: Toast;\n  position?: ToastPosition;\n  style?: React.CSSProperties;\n  children?: (components: {\n    icon: Renderable;\n    message: Renderable;\n  }) => Renderable;\n}\n\nconst getAnimationStyle = (\n  position: ToastPosition,\n  visible: boolean\n): React.CSSProperties => {\n  const top = position.includes('top');\n  const factor = top ? 1 : -1;\n\n  const [enter, exit] = prefersReducedMotion()\n    ? [fadeInAnimation, fadeOutAnimation]\n    : [enterAnimation(factor), exitAnimation(factor)];\n\n  return {\n    animation: visible\n      ? `${keyframes(enter)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`\n      : `${keyframes(exit)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`,\n  };\n};\n\nexport const ToastBar: React.FC<ToastBarProps> = React.memo(\n  ({ toast, position, style, children }) => {\n    const animationStyle: React.CSSProperties = toast.height\n      ? getAnimationStyle(\n          toast.position || position || 'top-center',\n          toast.visible\n        )\n      : { opacity: 0 };\n\n    const icon = <ToastIcon toast={toast} />;\n    const message = (\n      <Message {...toast.ariaProps}>\n        {resolveValue(toast.message, toast)}\n      </Message>\n    );\n\n    return (\n      <ToastBarBase\n        className={toast.className}\n        style={{\n          ...animationStyle,\n          ...style,\n          ...toast.style,\n        }}\n      >\n        {typeof children === 'function' ? (\n          children({\n            icon,\n            message,\n          })\n        ) : (\n          <>\n            {icon}\n            {message}\n          </>\n        )}\n      </ToastBarBase>\n    );\n  }\n);\n", "import * as React from 'react';\nimport { styled, keyframes } from 'goober';\n\nimport { Toast } from '../core/types';\nimport { ErrorIcon, ErrorTheme } from './error';\nimport { LoaderIcon, LoaderTheme } from './loader';\nimport { CheckmarkIcon, CheckmarkTheme } from './checkmark';\n\nconst StatusWrapper = styled('div')`\n  position: absolute;\n`;\n\nconst IndicatorWrapper = styled('div')`\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-width: 20px;\n  min-height: 20px;\n`;\n\nconst enter = keyframes`\nfrom {\n  transform: scale(0.6);\n  opacity: 0.4;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`;\n\nexport const AnimatedIconWrapper = styled('div')`\n  position: relative;\n  transform: scale(0.6);\n  opacity: 0.4;\n  min-width: 20px;\n  animation: ${enter} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n`;\n\nexport type IconThemes = Partial<{\n  success: CheckmarkTheme;\n  error: ErrorTheme;\n  loading: LoaderTheme;\n}>;\n\nexport const ToastIcon: React.FC<{\n  toast: Toast;\n}> = ({ toast }) => {\n  const { icon, type, iconTheme } = toast;\n  if (icon !== undefined) {\n    if (typeof icon === 'string') {\n      return <AnimatedIconWrapper>{icon}</AnimatedIconWrapper>;\n    } else {\n      return icon;\n    }\n  }\n\n  if (type === 'blank') {\n    return null;\n  }\n\n  return (\n    <IndicatorWrapper>\n      <LoaderIcon {...iconTheme} />\n      {type !== 'loading' && (\n        <StatusWrapper>\n          {type === 'error' ? (\n            <ErrorIcon {...iconTheme} />\n          ) : (\n            <CheckmarkIcon {...iconTheme} />\n          )}\n        </StatusWrapper>\n      )}\n    </IndicatorWrapper>\n  );\n};\n", "import { styled, keyframes } from 'goober';\n\nconst circleAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n transform: scale(1) rotate(45deg);\n  opacity: 1;\n}`;\n\nconst firstLineAnimation = keyframes`\nfrom {\n  transform: scale(0);\n  opacity: 0;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`;\n\nconst secondLineAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(90deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(90deg);\n\topacity: 1;\n}`;\n\nexport interface ErrorTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const ErrorIcon = styled('div')<ErrorTheme>`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(p) => p.primary || '#ff4b4b'};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${circleAnimation} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n\n  &:after,\n  &:before {\n    content: '';\n    animation: ${firstLineAnimation} 0.15s ease-out forwards;\n    animation-delay: 150ms;\n    position: absolute;\n    border-radius: 3px;\n    opacity: 0;\n    background: ${(p) => p.secondary || '#fff'};\n    bottom: 9px;\n    left: 4px;\n    height: 2px;\n    width: 12px;\n  }\n\n  &:before {\n    animation: ${secondLineAnimation} 0.15s ease-out forwards;\n    animation-delay: 180ms;\n    transform: rotate(90deg);\n  }\n`;\n", "import { styled, keyframes } from 'goober';\n\nconst rotate = keyframes`\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n`;\n\nexport interface LoaderTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const LoaderIcon = styled('div')<LoaderTheme>`\n  width: 12px;\n  height: 12px;\n  box-sizing: border-box;\n  border: 2px solid;\n  border-radius: 100%;\n  border-color: ${(p) => p.secondary || '#e0e0e0'};\n  border-right-color: ${(p) => p.primary || '#616161'};\n  animation: ${rotate} 1s linear infinite;\n`;\n", "import { styled, keyframes } from 'goober';\n\nconst circleAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(45deg);\n\topacity: 1;\n}`;\n\nconst checkmarkAnimation = keyframes`\n0% {\n\theight: 0;\n\twidth: 0;\n\topacity: 0;\n}\n40% {\n  height: 0;\n\twidth: 6px;\n\topacity: 1;\n}\n100% {\n  opacity: 1;\n  height: 10px;\n}`;\n\nexport interface CheckmarkTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const CheckmarkIcon = styled('div')<CheckmarkTheme>`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(p) => p.primary || '#61d345'};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${circleAnimation} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n  &:after {\n    content: '';\n    box-sizing: border-box;\n    animation: ${checkmarkAnimation} 0.2s ease-out forwards;\n    opacity: 0;\n    animation-delay: 200ms;\n    position: absolute;\n    border-right: 2px solid;\n    border-bottom: 2px solid;\n    border-color: ${(p) => p.secondary || '#fff'};\n    bottom: 6px;\n    left: 6px;\n    height: 10px;\n    width: 6px;\n  }\n`;\n", "import { css, setup } from 'goober';\nimport * as React from 'react';\nimport {\n  resolveValue,\n  ToasterProps,\n  ToastPosition,\n  ToastWrapperProps,\n} from '../core/types';\nimport { useToaster } from '../core/use-toaster';\nimport { prefersReducedMotion } from '../core/utils';\nimport { ToastBar } from './toast-bar';\n\nsetup(React.createElement);\n\nconst ToastWrapper = ({\n  id,\n  className,\n  style,\n  onHeightUpdate,\n  children,\n}: ToastWrapperProps) => {\n  const ref = React.useCallback(\n    (el: HTMLElement | null) => {\n      if (el) {\n        const updateHeight = () => {\n          const height = el.getBoundingClientRect().height;\n          onHeightUpdate(id, height);\n        };\n        updateHeight();\n        new MutationObserver(updateHeight).observe(el, {\n          subtree: true,\n          childList: true,\n          characterData: true,\n        });\n      }\n    },\n    [id, onHeightUpdate]\n  );\n\n  return (\n    <div ref={ref} className={className} style={style}>\n      {children}\n    </div>\n  );\n};\n\nconst getPositionStyle = (\n  position: ToastPosition,\n  offset: number\n): React.CSSProperties => {\n  const top = position.includes('top');\n  const verticalStyle: React.CSSProperties = top ? { top: 0 } : { bottom: 0 };\n  const horizontalStyle: React.CSSProperties = position.includes('center')\n    ? {\n        justifyContent: 'center',\n      }\n    : position.includes('right')\n    ? {\n        justifyContent: 'flex-end',\n      }\n    : {};\n  return {\n    left: 0,\n    right: 0,\n    display: 'flex',\n    position: 'absolute',\n    transition: prefersReducedMotion()\n      ? undefined\n      : `all 230ms cubic-bezier(.21,1.02,.73,1)`,\n    transform: `translateY(${offset * (top ? 1 : -1)}px)`,\n    ...verticalStyle,\n    ...horizontalStyle,\n  };\n};\n\nconst activeClass = css`\n  z-index: 9999;\n  > * {\n    pointer-events: auto;\n  }\n`;\n\nconst DEFAULT_OFFSET = 16;\n\nexport const Toaster: React.FC<ToasterProps> = ({\n  reverseOrder,\n  position = 'top-center',\n  toastOptions,\n  gutter,\n  children,\n  containerStyle,\n  containerClassName,\n}) => {\n  const { toasts, handlers } = useToaster(toastOptions);\n\n  return (\n    <div\n      id=\"_rht_toaster\"\n      style={{\n        position: 'fixed',\n        zIndex: 9999,\n        top: DEFAULT_OFFSET,\n        left: DEFAULT_OFFSET,\n        right: DEFAULT_OFFSET,\n        bottom: DEFAULT_OFFSET,\n        pointerEvents: 'none',\n        ...containerStyle,\n      }}\n      className={containerClassName}\n      onMouseEnter={handlers.startPause}\n      onMouseLeave={handlers.endPause}\n    >\n      {toasts.map((t) => {\n        const toastPosition = t.position || position;\n        const offset = handlers.calculateOffset(t, {\n          reverseOrder,\n          gutter,\n          defaultPosition: position,\n        });\n        const positionStyle = getPositionStyle(toastPosition, offset);\n\n        return (\n          <ToastWrapper\n            id={t.id}\n            key={t.id}\n            onHeightUpdate={handlers.updateHeight}\n            className={t.visible ? activeClass : ''}\n            style={positionStyle}\n          >\n            {t.type === 'custom' ? (\n              resolveValue(t.message, t)\n            ) : children ? (\n              children(t)\n            ) : (\n              <ToastBar toast={t} position={toastPosition} />\n            )}\n          </ToastWrapper>\n        );\n      })}\n    </div>\n  );\n};\n", "import { toast } from './core/toast';\n\nexport * from './headless';\n\nexport { ToastBar } from './components/toast-bar';\nexport { ToastIcon } from './components/toast-icon';\nexport { Toaster } from './components/toaster';\nexport { CheckmarkIcon } from './components/checkmark';\nexport { ErrorIcon } from './components/error';\nexport { LoaderIcon } from './components/loader';\n\nexport { toast };\nexport default toast;\n"], "names": ["isFunction", "valOrFunction", "resolveValue", "arg", "genId", "count", "prefersReducedMotion", "shouldReduceMotion", "mediaQuery", "useEffect", "useState", "useRef", "TOAST_LIMIT", "reducer", "state", "action", "TOAST_LIMIT", "t", "toast", "toastId", "diff", "listeners", "memoryState", "dispatch", "listener", "defaultTimeouts", "useStore", "toastOptions", "setState", "useState", "initial", "useRef", "useEffect", "index", "mergedToasts", "_a", "_b", "_c", "createToast", "message", "type", "opts", "genId", "createHandler", "options", "toast", "dispatch", "toastId", "promise", "msgs", "id", "p", "successMessage", "resolveValue", "e", "errorMessage", "useEffect", "useCallback", "updateHeight", "toastId", "height", "dispatch", "startPause", "toastTimeouts", "REMOVE_DELAY", "addToRemoveQueue", "<PERSON><PERSON><PERSON><PERSON>", "timeout", "useToaster", "toastOptions", "toasts", "pausedAt", "useStore", "useEffect", "now", "timeouts", "t", "durationLeft", "toast", "endPause", "useCallback", "calculateOffset", "opts", "reverseOrder", "gutter", "defaultPosition", "relevantToasts", "toastIndex", "toastsBefore", "i", "acc", "React", "styled", "keyframes", "React", "styled", "keyframes", "styled", "keyframes", "circleAnimation", "firstLineAnimation", "secondLineAnimation", "ErrorIcon", "p", "styled", "keyframes", "rotate", "LoaderIcon", "p", "styled", "keyframes", "circleAnimation", "checkmarkAnimation", "CheckmarkIcon", "p", "StatusWrapper", "styled", "IndicatorWrapper", "enter", "keyframes", "AnimatedIconWrapper", "ToastIcon", "toast", "icon", "type", "iconTheme", "LoaderIcon", "ErrorIcon", "CheckmarkIcon", "enterAnimation", "factor", "exitAnimation", "fadeInAnimation", "fadeOutAnimation", "ToastBarBase", "styled", "Message", "getAnimationStyle", "position", "visible", "enter", "exit", "prefersReducedMotion", "keyframes", "ToastBar", "toast", "style", "children", "animationStyle", "icon", "ToastIcon", "message", "resolveValue", "css", "setup", "React", "setup", "ToastWrapper", "id", "className", "style", "onHeightUpdate", "children", "ref", "el", "updateHeight", "height", "getPositionStyle", "position", "offset", "top", "verticalStyle", "horizontalStyle", "prefersReducedMotion", "activeClass", "css", "DEFAULT_OFFSET", "Toaster", "reverseOrder", "toastOptions", "gutter", "containerStyle", "containerClassName", "toasts", "handlers", "useToaster", "t", "toastPosition", "positionStyle", "resolveValue", "ToastBar", "src_default", "toast"], "mappings": ";;;;;;;;;;;;;AEAA,OAAS,aAAAS,EAAW,YAAAC,EAAU,UAAAC,MAAc;AGC5C,OAAS,UAAAiF,EAAQ,aAAAC,MAAiB,SCDlC,UAAYC,MAAW,QACvB,OAAS,UAAAC,EAAQ,aAAAC,OAAiB,SCDlC,OAAS,UAAAC,GAAQ,aAAAC,MAAiB;;APuBlC,IAAMlG,IACJC,KAEA,OAAOA,KAAkB,YAEdC,IAAe,CAC1BD,GACAE,IACYH,EAAWC,CAAa,IAAIA,EAAcE,CAAG,IAAIF;AC/BxD,IAAMG,IAAAA,CAAS,IAAM;IAC1B,IAAIC,IAAQ;IACZ,OAAO,IAAA,CACG,EAAEA,CAAAA,EAAO,QAAA,CAAS;AAE9B,CAAA,EAAG,GAEUC,IAAAA,CAAwB,IAAM;IAEzC,IAAIC;IAEJ,OAAO,IAAM;QACX,IAAIA,MAAuB,KAAA,KAAa,OAAO,OAAW,KAAa;YACrE,IAAMC,IAAa,WAAW,kCAAkC;YAChED,IAAqB,CAACC,KAAcA,EAAW,OAAA;QAAA;QAEjD,OAAOD;IACT;AACF,CAAA,EAAG;;ACfH,IAAMK,IAAc;AA+Cb,IAAMC,IAAU,CAACC,GAAcC,IAA0B;IAC9D,OAAQA,EAAO,IAAA,CAAM;QACnB,IAAK,CAAA;YACH,OAAO;gBACL,GAAGD,CAAAA;gBACH,QAAQ;oBAACC,EAAO,KAAA,CAAO;uBAAGD,EAAM,MAAM;iBAAA,CAAE,KAAA,CAAM,GAAGE,CAAW;YAC9D;QAEF,IAAK,CAAA;YACH,OAAO;gBACL,GAAGF,CAAAA;gBACH,QAAQA,EAAM,MAAA,CAAO,GAAA,EAAKG,IACxBA,EAAE,EAAA,KAAOF,EAAO,KAAA,CAAM,EAAA,GAAK;wBAAE,GAAGE,CAAAA;wBAAG,GAAGF,EAAO;oBAAM,IAAIE,CACzD;YACF;QAEF,IAAK,CAAA;YACH,IAAM,EAAE,OAAAC,CAAM,EAAA,GAAIH;YAClB,OAAOF,EAAQC,GAAO;gBACpB,MAAMA,EAAM,MAAA,CAAO,IAAA,EAAMG,IAAMA,EAAE,EAAA,KAAOC,EAAM,EAAE,IAC5C,IACA;gBACJ,OAAAA;YACF,CAAC;QAEH,IAAK,CAAA;YACH,IAAM,EAAE,SAAAC,CAAQ,EAAA,GAAIJ;YAEpB,OAAO;gBACL,GAAGD,CAAAA;gBACH,QAAQA,EAAM,MAAA,CAAO,GAAA,EAAKG,IACxBA,EAAE,EAAA,KAAOE,KAAWA,MAAY,KAAA,IAC5B;wBACE,GAAGF,CAAAA;wBACH,WAAW,CAAA;wBACX,SAAS,CAAA;oBACX,IACAA,CACN;YACF;QACF,IAAK,CAAA;YACH,OAAIF,EAAO,OAAA,KAAY,KAAA,IACd;gBACL,GAAGD,CAAAA;gBACH,QAAQ,CAAC;YACX,IAEK;gBACL,GAAGA,CAAAA;gBACH,QAAQA,EAAM,MAAA,CAAO,MAAA,EAAQG,IAAMA,EAAE,EAAA,KAAOF,EAAO,OAAO;YAC5D;QAEF,IAAK,CAAA;YACH,OAAO;gBACL,GAAGD,CAAAA;gBACH,UAAUC,EAAO;YACnB;QAEF,IAAK,CAAA;YACH,IAAMK,IAAOL,EAAO,IAAA,GAAA,CAAQD,EAAM,QAAA,IAAY,CAAA;YAE9C,OAAO;gBACL,GAAGA,CAAAA;gBACH,UAAU,KAAA;gBACV,QAAQA,EAAM,MAAA,CAAO,GAAA,EAAKG,IAAAA,CAAO;wBAC/B,GAAGA,CAAAA;wBACH,eAAeA,EAAE,aAAA,GAAgBG;oBACnC,CAAA,CAAE;YACJ;IACJ;AACF,GAEMC,IAA2C,CAAC,CAAA,EAE9CC,IAAqB;IAAE,QAAQ,CAAC,CAAA;IAAG,UAAU,KAAA;AAAU,GAE9CC,KAAYR,GAAmB;IAC1CO,IAAcT,EAAQS,GAAaP,CAAM,GACzCM,EAAU,OAAA,EAASG,GAAa;QAC9BA,EAASF,CAAW;IACtB,CAAC;AACH,GAEaG,IAET;IACF,OAAO;IACP,OAAO;IACP,SAAS;IACT,SAAS,IAAA;IACT,QAAQ;AACV,GAEaC,IAAW,CAACC,IAAoC,CAAC,CAAA,GAAa;IACzE,IAAM,CAACb,GAAOc,CAAQ,CAAA,uVAAIC,EAAgBP,CAAW,GAC/CQ,KAAUC,iVAAAA,EAAOT,CAAW;yVAGlCU,EAAU,IAAA,CACJF,EAAQ,OAAA,KAAYR,KACtBM,EAASN,CAAW,GAEtBD,EAAU,IAAA,CAAKO,CAAQ,GAChB,IAAM;YACX,IAAMK,IAAQZ,EAAU,OAAA,CAAQO,CAAQ;YACpCK,IAAQ,CAAA,KACVZ,EAAU,MAAA,CAAOY,GAAO,CAAC;QAE7B,CAAA,GACC,CAAC,CAAC;IAEL,IAAMC,IAAepB,EAAM,MAAA,CAAO,GAAA,EAAKG,GAAG;QAjK5C,IAAAkB,GAAAC,GAAAC;QAiKgD,OAAA;YAC5C,GAAGV,CAAAA;YACH,GAAGA,CAAAA,CAAaV,EAAE,IAAI,CAAA;YACtB,GAAGA,CAAAA;YACH,aACEA,EAAE,WAAA,IAAA,CAAA,CACFkB,IAAAR,CAAAA,CAAaV,EAAE,IAAI,CAAA,KAAnB,OAAA,KAAA,IAAAkB,EAAsB,WAAA,KAAA,CACtBR,KAAA,OAAA,KAAA,IAAAA,EAAc,WAAA;YAChB,UACEV,EAAE,QAAA,IAAA,CAAA,CACFmB,IAAAT,CAAAA,CAAaV,EAAE,IAAI,CAAA,KAAnB,OAAA,KAAA,IAAAmB,EAAsB,QAAA,KAAA,CACtBT,KAAA,OAAA,KAAA,IAAAA,EAAc,QAAA,KACdF,CAAAA,CAAgBR,EAAE,IAAI,CAAA;YACxB,OAAO;gBACL,GAAGU,EAAa,KAAA;gBAChB,GAAA,CAAGU,IAAAV,CAAAA,CAAaV,EAAE,IAAI,CAAA,KAAnB,OAAA,KAAA,IAAAoB,EAAsB,KAAA;gBACzB,GAAGpB,EAAE;YACP;QACF;IAAA,CAAE;IAEF,OAAO;QACL,GAAGH,CAAAA;QACH,QAAQoB;IACV;AACF;ACzKA,IAAMI,IAAc,CAClBC,GACAC,IAAkB,OAAA,EAClBC,IAAAA,CACW;QACX,WAAW,KAAK,GAAA,CAAI;QACpB,SAAS,CAAA;QACT,WAAW,CAAA;QACX,MAAAD;QACA,WAAW;YACT,MAAM;YACN,aAAa;QACf;QACA,SAAAD;QACA,eAAe;QACf,GAAGE,CAAAA;QACH,IAAA,CAAIA,KAAA,OAAA,KAAA,IAAAA,EAAM,EAAA,KAAMC,EAAM;IACxB,CAAA,GAEMC,KACHH,IACD,CAACD,GAASK,IAAY;QACpB,IAAMC,IAAQP,EAAYC,GAASC,GAAMI,CAAO;QAChD,OAAAE,EAAS;YAAE,MAAA;YAA+B,OAAAD;QAAM,CAAC,GAC1CA,EAAM;IACf,GAEIA,IAAQ,CAACN,GAAkBE,IAC/BE,EAAc,OAAO,EAAEJ,GAASE,CAAI;AAEtCI,EAAM,KAAA,GAAQF,EAAc,OAAO;AACnCE,EAAM,OAAA,GAAUF,EAAc,SAAS;AACvCE,EAAM,OAAA,GAAUF,EAAc,SAAS;AACvCE,EAAM,MAAA,GAASF,EAAc,QAAQ;AAErCE,EAAM,OAAA,IAAWE,GAAqB;IACpCD,EAAS;QACP,MAAA;QACA,SAAAC;IACF,CAAC;AACH;AAEAF,EAAM,MAAA,GAAUE,KACdD,EAAS;QAAE,MAAA;QAA+B,SAAAC;IAAQ,CAAC;AAErDF,EAAM,OAAA,GAAU,CACdG,GACAC,GAKAR,IACG;IACH,IAAMS,IAAKL,EAAM,OAAA,CAAQI,EAAK,OAAA,EAAS;QAAE,GAAGR,CAAAA;QAAM,GAAGA,KAAA,OAAA,KAAA,IAAAA,EAAM;IAAQ,CAAC;IAEpE,OAAI,OAAOO,KAAY,cAAA,CACrBA,IAAUA,EAAQ,CAAA,GAGpBA,EACG,IAAA,EAAMG,GAAM;QACX,IAAMC,IAAiBH,EAAK,OAAA,GACxBI,EAAaJ,EAAK,OAAA,EAASE,CAAC,IAC5B,KAAA;QAEJ,OAAIC,IACFP,EAAM,OAAA,CAAQO,GAAgB;YAC5B,IAAAF;YACA,GAAGT,CAAAA;YACH,GAAGA,KAAA,OAAA,KAAA,IAAAA,EAAM;QACX,CAAC,IAEDI,EAAM,OAAA,CAAQK,CAAE,GAEXC;IACT,CAAC,EACA,KAAA,EAAOG,GAAM;QACZ,IAAMC,IAAeN,EAAK,KAAA,GAAQI,EAAaJ,EAAK,KAAA,EAAOK,CAAC,IAAI,KAAA;QAE5DC,IACFV,EAAM,KAAA,CAAMU,GAAc;YACxB,IAAAL;YACA,GAAGT,CAAAA;YACH,GAAGA,KAAA,OAAA,KAAA,IAAAA,EAAM,KACX,CAAC;aAEDI,EAAM,OAAA,CAAQK,CAAE;IAEpB,CAAC,GAEIF;AACT,EC5GA,OAAS,aAAAQ,EAAW,eAAAC,MAAmB;;AAKvC,IAAMC,IAAe,CAACC,GAAiBC,IAAmB;IACxDC,EAAS;QACP,MAAA;QACA,OAAO;YAAE,IAAIF;YAAS,QAAAC;QAAO;IAC/B,CAAC;AACH,GACME,IAAa,IAAM;IACvBD,EAAS;QACP,MAAA;QACA,MAAM,KAAK,GAAA,CAAI;IACjB,CAAC;AACH,GAEME,IAAgB,IAAI,KAEbC,IAAe,KAEtBC,KAAmB,CAACN,GAAiBO,IAAcF,CAAAA,GAAiB;IACxE,IAAID,EAAc,GAAA,CAAIJ,CAAO,GAC3B;IAGF,IAAMQ,IAAU,WAAW,IAAM;QAC/BJ,EAAc,MAAA,CAAOJ,CAAO,GAC5BE,EAAS;YACP,MAAA;YACA,SAASF;QACX,CAAC;IACH,GAAGO,CAAW;IAEdH,EAAc,GAAA,CAAIJ,GAASQ,CAAO;AACpC,GAEaC,KAAcC,GAAuC;IAChE,IAAM,EAAE,QAAAC,CAAAA,EAAQ,UAAAC,CAAS,EAAA,GAAIC,EAASH,CAAY;KAElDI,oVAAAA,EAAU,IAAM;QACd,IAAIF,GACF;QAGF,IAAMG,IAAM,KAAK,GAAA,CAAI,GACfC,IAAWL,EAAO,GAAA,EAAKM,GAAM;YACjC,IAAIA,EAAE,QAAA,KAAa,IAAA,GACjB;YAGF,IAAMC,IAAAA,CACHD,EAAE,QAAA,IAAY,CAAA,IAAKA,EAAE,aAAA,GAAA,CAAiBF,IAAME,EAAE,SAAA;YAEjD,IAAIC,IAAe,GAAG;gBAChBD,EAAE,OAAA,IACJE,EAAM,OAAA,CAAQF,EAAE,EAAE;gBAEpB;YAAA;YAEF,OAAO,WAAW,IAAME,EAAM,OAAA,CAAQF,EAAE,EAAE,GAAGC,CAAY;QAC3D,CAAC;QAED,OAAO,IAAM;YACXF,EAAS,OAAA,EAASR,IAAYA,KAAW,aAAaA,CAAO,CAAC;QAChE;IACF,GAAG;QAACG;QAAQC,CAAQ;KAAC;IAErB,IAAMQ,2VAAWC,EAAY,IAAM;QAC7BT,KACFV,EAAS;YAAE,MAAA;YAA4B,MAAM,KAAK,GAAA,CAAI;QAAE,CAAC;IAE7D,GAAG;QAACU,CAAQ;KAAC,GAEPU,2VAAkBD,EACtB,CACEF,GACAI,IAKG;QACH,IAAM,EAAE,cAAAC,IAAe,CAAA,CAAA,EAAO,QAAAC,IAAS,CAAA,EAAG,iBAAAC,CAAgB,EAAA,GAAIH,KAAQ,CAAC,GAEjEI,IAAiBhB,EAAO,MAAA,EAC3BM,IAAAA,CACEA,EAAE,QAAA,IAAYS,CAAAA,MAAAA,CACZP,EAAM,QAAA,IAAYO,CAAAA,KAAoBT,EAAE,MAC/C,GACMW,IAAaD,EAAe,SAAA,EAAWV,IAAMA,EAAE,EAAA,KAAOE,EAAM,EAAE,GAC9DU,IAAeF,EAAe,MAAA,CAClC,CAACR,GAAOW,IAAMA,IAAIF,KAAcT,EAAM,OACxC,EAAE,MAAA;QAOF,OALeQ,EACZ,MAAA,EAAQV,IAAMA,EAAE,OAAO,EACvB,KAAA,CAAM,GAAIO,IAAe;YAACK,IAAe,CAAC;SAAA,GAAI;YAAC;YAAGA,CAAY;SAAE,EAChE,MAAA,CAAO,CAACE,GAAKd,IAAMc,IAAAA,CAAOd,EAAE,MAAA,IAAU,CAAA,IAAKQ,GAAQ,CAAC;IAGzD,GACA;QAACd,CAAM;KACT;IAEA,4VAAAG,EAAU,IAAM;QAEdH,EAAO,OAAA,EAASQ,GAAU;YACxB,IAAIA,EAAM,SAAA,EACRb,GAAiBa,EAAM,EAAA,EAAIA,EAAM,WAAW;iBACvC;gBAEL,IAAMX,IAAUJ,EAAc,GAAA,CAAIe,EAAM,EAAE;gBACtCX,KAAAA,CACF,aAAaA,CAAO,GACpBJ,EAAc,MAAA,CAAOe,EAAM,EAAE,CAAA;YAAA;QAGnC,CAAC;IACH,GAAG;QAACR,CAAM;KAAC,GAEJ;QACL,QAAAA;QACA,UAAU;YACR,cAAAZ;YACA,YAAAI;YACA,UAAAiB;YACA,iBAAAE;QACF;IACF;AACF,ECnIA,UAAYU,MAAW;;;;;;AEEvB,IAAMQ,mOAAkBD,YAAAA,CAAAA;;;;;;;;CAAA,CAAA,EAUlBE,mOAAqBF,YAAAA,CAAAA;;;;;;;;CAAA,CAAA,EAUrBG,mOAAsBH,YAAAA,CAAAA;;;;;;;;CAAA,CAAA,EAefI,sOAAYL,SAAAA,EAAO,KAAK,CAAA,CAAA;;;;;cAAA,GAKpBM,IAAMA,EAAE,OAAA,IAAW,UAAA;;;;aAAA,EAIrBJ,GAAAA;;;;;;;eAAA,EAOEC,GAAAA;;;;;gBAAA,GAKEG,IAAMA,EAAE,SAAA,IAAa,OAAA;;;;;;;;eAAA,EAQvBF,GAAAA;;;;EClEjB,OAAS,UAAAG,GAAQ,aAAAC,OAAiB;;AAElC,IAAMC,mOAASD,YAAAA,CAAAA;;;;;;;AAAA,CAAA,EAcFE,IAAaH,2OAAAA,EAAO,KAAK,CAAA,CAAA;;;;;;gBAAA,GAMnBI,IAAMA,EAAE,SAAA,IAAa,UAAA;sBAAA,GACfA,IAAMA,EAAE,OAAA,IAAW,UAAA;aAAA,EAC7BF,GAAAA;ECxBf,OAAS,UAAAG,GAAQ,aAAAC,MAAiB;;AAElC,IAAMC,mOAAkBD,YAAAA,CAAAA;;;;;;;;CAAA,CAAA,EAUlBE,mOAAqBF,YAAAA,CAAAA;;;;;;;;;;;;;;CAAA,CAAA,EAqBdG,sOAAgBJ,SAAAA,EAAO,KAAK,CAAA,CAAA;;;;;cAAA,GAKxBK,IAAMA,EAAE,OAAA,IAAW,UAAA;;;;aAAA,EAIrBH,GAAAA;;;;;;eAAA,EAMEC,GAAAA;;;;;;kBAAA,EAMIE,KAAMA,EAAE,SAAA,IAAa,OAAA;;;;;;;AH9C1C,IAAMC,gPAAgBC,EAAO,KAAK,CAAA,CAAA;;AAAA,CAAA,EAI5BC,sOAAmBD,UAAAA,EAAO,KAAK,CAAA,CAAA;;;;;;;AAAA,CAAA,EAS/BE,mOAAQC,YAAAA,CAAAA;;;;;;;;CAAA,CAAA,EAUDC,SAAsBJ,uOAAAA,EAAO,KAAK,CAAA,CAAA;;;;;aAAA,EAKhCE,GAAAA;;AAAA,CAAA,EAUFG,IAER,CAAC,EAAE,OAAAC,CAAM,EAAA,GAAM;IAClB,IAAM,EAAE,MAAAC,CAAAA,EAAM,MAAAC,CAAAA,EAAM,WAAAC,CAAU,EAAA,GAAIH;IAClC,OAAIC,MAAS,KAAA,IACP,OAAOA,KAAS,+UACX,iBAAA,CAACH,IAAA,MAAqBG,CAAK,IAE3BA,IAIPC,MAAS,UACJ,4UAIP,gBAAA,CAACP,IAAA,2UACC,gBAAA,CAACS,GAAA;QAAY,GAAGD,CAAAA;IAAAA,CAAW,GAC1BD,MAAS,aACR,qVAAA,CAACT,IAAA,MACES,MAAS,+UACR,gBAAA,CAACG,GAAA;QAAW,GAAGF,CAAAA;IAAAA,CAAW,yUAE1B,gBAAA,CAACG,GAAA;QAAe,GAAGH,CAAAA;IAAAA,CAAW,CAElC,CAEJ;AAEJ;ADrEA,IAAMI,MAAkBC,IAAmB,CAAA;6BAAA,EACZA,IAAS,CAAA,IAAA;;AAAA,CAAA,EAIlCC,MAAiBD,IAAmB,CAAA;;+BAAA,EAETA,IAAS,CAAA,IAAA;AAAA,CAAA,EAGpCE,KAAkB,mCAClBC,KAAmB,mCAEnBC,SAAeC,uOAAAA,EAAO,KAAK,CAAA,CAAA;;;;;;;;;;;;AAAA,CAAA,EAc3BC,gPAAUD,EAAO,KAAK,CAAA,CAAA;;;;;;;AAAA,CAAA,EAmBtBE,KAAoB,CACxBC,GACAC,IACwB;IAExB,IAAMT,IADMQ,EAAS,QAAA,CAAS,KAAK,IACd,IAAI,CAAA,GAEnB,CAACE,GAAOC,CAAI,CAAA,GAAIC,EAAqB,IACvC;QAACV;QAAiBC,EAAgB;KAAA,GAClC;QAACJ,GAAeC,CAAM;QAAGC,GAAcD,CAAM,CAAC;KAAA;IAElD,OAAO;QACL,WAAWS,IACP,IAAGI,6OAAAA,EAAUH,CAAK,EAAA,4CAAA,CAAA,GAClB,iPAAGG,EAAUF,CAAI,EAAA,0CAAA;IACvB;AACF,GAEaG,yUAA0C,OAAA,CACrD,CAAC,EAAE,OAAAC,CAAAA,EAAO,UAAAP,CAAAA,EAAU,OAAAQ,CAAAA,EAAO,UAAAC,CAAS,EAAA,GAAM;IACxC,IAAMC,IAAsCH,EAAM,MAAA,GAC9CR,GACEQ,EAAM,QAAA,IAAYP,KAAY,cAC9BO,EAAM,OACR,IACA;QAAE,SAAS;IAAE,GAEXI,yUAAO,gBAAA,CAACC,GAAA;QAAU,OAAOL;IAAAA,CAAO,GAChCM,wUACJ,iBAAA,CAACf,IAAA;QAAS,GAAGS,EAAM,SAAA;IAAA,GAChBO,EAAaP,EAAM,OAAA,EAASA,CAAK,CACpC;IAGF,4UACE,gBAAA,CAACX,IAAA;QACC,WAAWW,EAAM,SAAA;QACjB,OAAO;YACL,GAAGG,CAAAA;YACH,GAAGF,CAAAA;YACH,GAAGD,EAAM;QACX;IAAA,GAEC,OAAOE,KAAa,aACnBA,EAAS;QACP,MAAAE;QACA,SAAAE;IACF,CAAC,yUAED,gBAAA,CAAA,oUAAA,CAAA,WAAA,EAAA,MACGF,GACAE,CACH,CAEJ;AAEJ,CACF,EK9GA,OAAS,OAAAE,GAAK,SAAAC,OAAa,SAC3B,UAAYC,MAAW;;;kOAWvBC,QAAAA,uUAAY,gBAAa;AAEzB,IAAMC,KAAe,CAAC,EACpB,IAAAC,CAAAA,EACA,WAAAC,CAAAA,EACA,OAAAC,CAAAA,EACA,gBAAAC,CAAAA,EACA,UAAAC,CACF,EAAA,GAAyB;IACvB,IAAMC,yUAAY,cAAA,EACfC,GAA2B;QAC1B,IAAIA,GAAI;YACN,IAAMC,IAAe,IAAM;gBACzB,IAAMC,IAASF,EAAG,qBAAA,CAAsB,EAAE,MAAA;gBAC1CH,EAAeH,GAAIQ,CAAM;YAC3B;YACAD,EAAa,GACb,IAAI,iBAAiBA,CAAY,EAAE,OAAA,CAAQD,GAAI;gBAC7C,SAAS,CAAA;gBACT,WAAW,CAAA;gBACX,eAAe,CAAA;YACjB,CAAC;QAAA;IAEL,GACA;QAACN;QAAIG,CAAc;KACrB;IAEA,OACE,qVAAA,CAAC,OAAA;QAAI,KAAKE;QAAK,WAAWJ;QAAW,OAAOC;IAAAA,GACzCE,CACH;AAEJ,GAEMK,KAAmB,CACvBC,GACAC,IACwB;IACxB,IAAMC,IAAMF,EAAS,QAAA,CAAS,KAAK,GAC7BG,IAAqCD,IAAM;QAAE,KAAK;IAAE,IAAI;QAAE,QAAQ;IAAE,GACpEE,IAAuCJ,EAAS,QAAA,CAAS,QAAQ,IACnE;QACE,gBAAgB;IAClB,IACAA,EAAS,QAAA,CAAS,OAAO,IACzB;QACE,gBAAgB;IAClB,IACA,CAAC;IACL,OAAO;QACL,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,YAAYK,EAAqB,IAC7B,KAAA,IACA;QACJ,WAAW,CAAA,WAAA,EAAcJ,IAAAA,CAAUC,IAAM,IAAI,CAAA,CAAA,EAAA,GAAA,CAAA;QAC7C,GAAGC,CAAAA;QACH,GAAGC;IACL;AACF,GAEME,kOAAcC,OAAAA,CAAAA;;;;;AAAA,CAAA,EAOdC,IAAiB,IAEVC,KAAkC,CAAC,EAC9C,cAAAC,CAAAA,EACA,UAAAV,IAAW,YAAA,EACX,cAAAW,CAAAA,EACA,QAAAC,CAAAA,EACA,UAAAlB,CAAAA,EACA,gBAAAmB,CAAAA,EACA,oBAAAC,CACF,EAAA,GAAM;IACJ,IAAM,EAAE,QAAAC,CAAAA,EAAQ,UAAAC,CAAS,EAAA,GAAIC,EAAWN,CAAY;IAEpD,4UACE,gBAAA,CAAC,OAAA;QACC,IAAG;QACH,OAAO;YACL,UAAU;YACV,QAAQ;YACR,KAAKH;YACL,MAAMA;YACN,OAAOA;YACP,QAAQA;YACR,eAAe;YACf,GAAGK,CACL;;QACA,WAAWC;QACX,cAAcE,EAAS,UAAA;QACvB,cAAcA,EAAS,QAAA;IAAA,GAEtBD,EAAO,GAAA,EAAKG,GAAM;QACjB,IAAMC,IAAgBD,EAAE,QAAA,IAAYlB,GAC9BC,IAASe,EAAS,eAAA,CAAgBE,GAAG;YACzC,cAAAR;YACA,QAAAE;YACA,iBAAiBZ;QACnB,CAAC,GACKoB,IAAgBrB,GAAiBoB,GAAelB,CAAM;QAE5D,2UACE,iBAAA,CAACZ,IAAA;YACC,IAAI6B,EAAE,EAAA;YACN,KAAKA,EAAE,EAAA;YACP,gBAAgBF,EAAS,YAAA;YACzB,WAAWE,EAAE,OAAA,GAAUZ,KAAc;YACrC,OAAOc;QAAAA,GAENF,EAAE,IAAA,KAAS,WACVG,EAAaH,EAAE,OAAA,EAASA,CAAC,IACvBxB,IACFA,EAASwB,CAAC,yUAEV,gBAAA,CAACI,GAAA;YAAS,OAAOJ;YAAG,UAAUC;QAAAA,CAAe,CAEjD;IAEJ,CAAC,CACH;AAEJ;ACjIA,IAAOI,KAAQC", "debugId": null}}, {"offset": {"line": 1440, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/7mouthMission/1500/my-nextjs-app/node_modules/.pnpm/next%4015.4.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-ssr']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,uMACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}