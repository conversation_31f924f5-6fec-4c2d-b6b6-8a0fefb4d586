"use strict";exports.id=674,exports.ids=[674],exports.modules={8674:(a,b,c)=>{c.d(b,{Q:()=>s});var d=c(568),e=c(5561),f=c(8535);function g(a){let[b,c]=(0,e.useState)(!1),{src:f,alt:g,style:h,className:i,...j}=a;return b?(0,d.jsx)("div",{className:`inline-block bg-gray-100 text-center align-middle ${i??""}`,style:h,children:(0,d.jsx)("div",{className:"flex items-center justify-center w-full h-full",children:(0,d.jsx)("img",{src:"data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODgiIGhlaWdodD0iODgiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgc3Ryb2tlPSIjMDAwIiBzdHJva2UtbGluZWpvaW49InJvdW5kIiBvcGFjaXR5PSIuMyIgZmlsbD0ibm9uZSIgc3Ryb2tlLXdpZHRoPSIzLjciPjxyZWN0IHg9IjE2IiB5PSIxNiIgd2lkdGg9IjU2IiBoZWlnaHQ9IjU2IiByeD0iNiIvPjxwYXRoIGQ9Im0xNiA1OCAxNi0xOCAzMiAzMiIvPjxjaXJjbGUgY3g9IjUzIiBjeT0iMzUiIHI9IjciLz48L3N2Zz4KCg==",alt:"Error loading image",...j,"data-original-url":f})})}):(0,d.jsx)("img",{src:f,alt:g,className:i,style:h,...j,onError:()=>{c(!0)}})}var h=c(9069),i=c(7098),j=c(1469),k=c(8353),l=c(3807),m=c(6845),n=c(3380),o=c(3607),p=c(6690),q=c(1508),r=c(7324);function s({currentPage:a="services",onNavigate:b,onOpenChat:c}){let[s,t]=(0,e.useState)(!1),[u,v]=(0,e.useState)(""),[w]=(0,e.useState)(3),x=(0,e.useRef)(null),y=a=>{b?.(a),t(!1)};return(0,d.jsx)("nav",{className:"bg-white/90 backdrop-blur-sm border-b border-slate-200/60 sticky top-0 z-[70]",children:(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-4",children:(0,d.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,d.jsx)("div",{className:"flex items-center gap-8",children:(0,d.jsxs)("div",{className:"flex items-center gap-3 cursor-pointer",onClick:()=>y("home"),children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"absolute inset-0 bg-blue-500/10 rounded-xl blur-sm"}),(0,d.jsx)("div",{className:"relative p-2 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-xl border border-blue-200/30",children:(0,d.jsx)(h.A,{className:"h-6 w-6 text-blue-600"})})]}),(0,d.jsx)("span",{className:"text-slate-900 text-lg font-bold tracking-tight",children:"U-bund"})]})}),(0,d.jsxs)("div",{className:"flex-1 max-w-4xl mx-8 hidden md:flex items-center gap-4",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)(f.$,{variant:"ghost",size:"sm",onClick:()=>y("support"),className:`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${"support"===a?"bg-blue-50/80 text-blue-700 shadow-sm":"text-slate-600 hover:text-slate-900 hover:bg-slate-100/80"}`,children:[(0,d.jsx)(i.A,{className:"h-4 w-4"}),"定制服务*"]}),(0,d.jsxs)(f.$,{variant:"ghost",size:"sm",onClick:()=>y("services"),className:`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${"services"===a?"bg-blue-50/80 text-blue-700 shadow-sm":"text-slate-600 hover:text-slate-900 hover:bg-slate-100/80"}`,children:[(0,d.jsx)(j.A,{className:"h-4 w-4"}),"找服务"]}),(0,d.jsxs)(f.$,{variant:"ghost",size:"sm",onClick:()=>y("gallery"),className:`flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${"gallery"===a?"bg-blue-50/80 text-blue-700 shadow-sm":"text-slate-600 hover:text-slate-900 hover:bg-slate-100/80"}`,children:[(0,d.jsx)(k.A,{className:"h-4 w-4"}),"作品广场*"]})]}),(0,d.jsxs)("div",{className:"flex-1 max-w-md relative",children:[(0,d.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,d.jsx)("input",{type:"text",placeholder:"搜索服务、商家...",value:u,onChange:a=>v(a.target.value),className:"w-full pl-10 pr-4 py-2 bg-slate-50/80 border border-slate-200/60 rounded-lg text-sm placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-300 transition-all duration-200"})]})]}),(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[(0,d.jsxs)(f.$,{variant:"ghost",size:"sm",onClick:()=>y("orders"),className:`hidden sm:flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${"orders"===a?"bg-blue-50/80 text-blue-700 shadow-sm":"text-slate-600 hover:text-slate-900 hover:bg-slate-100/80"}`,children:[(0,d.jsx)(l.A,{className:"h-4 w-4"}),"我的订单"]}),(0,d.jsxs)(f.$,{variant:"ghost",size:"sm",onClick:()=>y("favorites"),className:`hidden sm:flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200 ${"favorites"===a?"bg-blue-50/80 text-blue-700 shadow-sm":"text-slate-600 hover:text-slate-900 hover:bg-slate-100/80"}`,children:[(0,d.jsx)(m.A,{className:"h-4 w-4"}),"我的收藏"]}),(0,d.jsx)("div",{className:"relative",children:(0,d.jsxs)(f.$,{variant:"ghost",size:"sm",onClick:()=>{c?.()},className:"relative p-2 rounded-lg text-slate-600 hover:text-slate-900 hover:bg-slate-100/80 transition-all duration-200",children:[(0,d.jsx)(n.A,{className:"h-5 w-5"}),w>0&&(0,d.jsx)("span",{className:"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium",children:w})]})}),(0,d.jsxs)("div",{className:"relative",ref:x,children:[(0,d.jsxs)(f.$,{variant:"ghost",size:"sm",onClick:()=>t(!s),className:"flex items-center gap-2 p-1 pr-2 hover:bg-slate-100/80 transition-colors duration-200",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(g,{src:"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=faces",alt:"用户头像",className:"w-8 h-8 rounded-full object-cover border border-slate-200/60"}),(0,d.jsx)("div",{className:"absolute -top-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white"}),w>0&&(0,d.jsx)("div",{className:"absolute -top-2 -right-2 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center text-white text-xs font-medium border-2 border-white",children:w>9?"9+":w})]}),(0,d.jsx)("span",{className:"hidden sm:block text-slate-700 text-sm",children:"张小明"}),(0,d.jsx)(o.A,{className:`h-4 w-4 text-slate-400 transition-transform duration-200 ${s?"rotate-180":""}`})]}),s&&(0,d.jsxs)("div",{className:"absolute right-0 top-full mt-2 w-48 bg-white/95 backdrop-blur-sm border border-slate-200/60 rounded-xl shadow-lg py-2 z-[200]",children:[(0,d.jsxs)("div",{className:"px-4 py-3 border-b border-slate-200/60",children:[(0,d.jsx)("p",{className:"text-slate-900 text-sm",children:"张小明"}),(0,d.jsx)("p",{className:"text-slate-500 text-xs",children:"<EMAIL>"})]}),(0,d.jsxs)("div",{className:"py-1",children:[(0,d.jsxs)(f.$,{variant:"ghost",size:"sm",onClick:()=>{c?.(),t(!1)},className:"w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80 relative",children:[(0,d.jsx)(n.A,{className:"h-4 w-4 mr-3"}),"通知消息",w>0&&(0,d.jsx)("span",{className:"ml-auto bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] text-center",children:w>9?"9+":w})]}),(0,d.jsxs)(f.$,{variant:"ghost",size:"sm",onClick:()=>y("profile"),className:"w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-3"}),"个人资料*"]}),(0,d.jsxs)(f.$,{variant:"ghost",size:"sm",className:"w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80",children:[(0,d.jsx)(q.A,{className:"h-4 w-4 mr-3"}),"认证专区*"]}),(0,d.jsxs)(f.$,{variant:"ghost",size:"sm",className:"w-full justify-start px-4 py-2 text-slate-600 hover:text-slate-900 hover:bg-slate-50/80",children:[(0,d.jsx)(r.A,{className:"h-4 w-4 mr-3"}),"账户设置*"]})]}),(0,d.jsx)("div",{className:"border-t border-slate-200/60 pt-1",children:(0,d.jsx)(f.$,{variant:"ghost",size:"sm",className:"w-full justify-start px-4 py-2 text-red-600 hover:text-red-700 hover:bg-red-50/80",children:"退出登录"})})]})]})]})]})})})}}};