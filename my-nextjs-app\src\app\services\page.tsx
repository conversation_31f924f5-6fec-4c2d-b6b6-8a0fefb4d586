"use client";

import { useState } from "react";
import { NavigationV78 } from "@/components/NavigationV78";
import { CategoryNavigation } from "@/components/CategoryNavigation";
import { ServicesPage } from "@/components/ServicesPage";
import { ChatWindow } from "@/components/ChatWindow";
import { ChatFloatingButton } from "@/components/ChatFloatingButton";
import { useRouter } from "next/navigation";

export default function Services() {
  const router = useRouter();
  const [activeCategory, setActiveCategory] = useState("popular");
  const [activeSubCategory, setActiveSubCategory] = useState<string | null>(null);
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [notificationCount] = useState(3);

  const handleNavigate = (page: string) => {
    switch (page) {
      case "home":
      case "landing":
        router.push("/");
        break;
      case "services":
        router.push("/services");
        break;
      case "orders":
        router.push("/orders");
        break;
      case "favorites":
        router.push("/favorites");
        break;
      case "support":
        router.push("/support");
        break;
      case "gallery":
        router.push("/gallery");
        break;
      case "login":
        router.push("/login");
        break;
      case "register":
        router.push("/register");
        break;
      default:
        break;
    }
  };

  const handleSubCategoryClick = (category: string, subCategory: string) => {
    setActiveCategory(category);
    setActiveSubCategory(subCategory);
    console.log(`已选择子分类: ${subCategory} 在分类: ${category}`);
  };

  const handleCategoryChange = (category: string) => {
    setActiveCategory(category);
    setActiveSubCategory(null);
    console.log(`已切换到分类: ${category}`);
  };

  const handleOpenChat = () => {
    setIsChatOpen(true);
  };

  const handleCloseChat = () => {
    setIsChatOpen(false);
  };

  const handleMinimizeChat = () => {
    setIsChatOpen(false);
  };

  const handleViewProductDetail = (productId: string) => {
    router.push(`/product/${productId}`);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30">
      <NavigationV78 
        currentPage="services" 
        onNavigate={handleNavigate}
        onOpenChat={handleOpenChat}
      />
      
      <CategoryNavigation 
        activeCategory={activeCategory}
        onCategoryChange={handleCategoryChange}
        onSubCategoryClick={handleSubCategoryClick}
      />
      
      <ServicesPage 
        activeCategory={activeCategory} 
        activeSubCategory={activeSubCategory}
        onCategoryChange={handleCategoryChange}
        onSubCategoryChange={setActiveSubCategory}
        onViewProductDetail={handleViewProductDetail}
      />

      {/* 聊天窗口 */}
      <ChatWindow isOpen={isChatOpen} onClose={handleCloseChat} onMinimize={handleMinimizeChat} />
      
      {/* 聊天悬浮球 */}
      <ChatFloatingButton 
        onClick={handleOpenChat}
        notificationCount={notificationCount}
        isVisible={!isChatOpen}
      />
    </div>
  );
}
