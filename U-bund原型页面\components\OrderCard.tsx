import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "./ui/card";
import { OrderStatusBadge } from "./OrderStatusBadge";
import { Button } from "./ui/button";
import { ImageWithFallback } from "./figma/ImageWithFallback";
import { MoreHorizontal, Truck, Eye, Receipt, Shield, Settings, MessageCircle, Clock, RotateCcw, Star, Trash2 } from "lucide-react";

interface OrderItem {
  id: string;
  name: string;
  image: string;
  quantity: number;
  price: number;
}

interface Merchant {
  id: string;
  name: string;
  avatar: string;
  isOnline: boolean;
}

interface Order {
  id: string;
  orderNumber: string;
  date: string;
  status: "pending" | "processing" | "shipped" | "delivered" | "cancelled";
  items: OrderItem[];
  total: number;
  shippingAddress: string;
  completedAt?: string;
  estimatedCompletionTime?: string;
  merchant: Merchant;
}

interface OrderCardProps {
  order: Order;
  onViewDetail?: () => void;
}

export function OrderCard({ order, onViewDetail }: OrderCardProps) {
  const [showDeleteButton, setShowDeleteButton] = useState(false);
  const [isDeleteHovered, setIsDeleteHovered] = useState(false);

  const formatPrice = (price: number) => {
    return `¥${price.toFixed(2)}`;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleInvoiceRequest = () => {
    // 处理开发票请求
    console.log(`开发票请求：订单 ${order.orderNumber}`);
  };

  const handleAfterSalesRequest = () => {
    // 处理申请售后请求
    console.log(`申请售后：订单 ${order.orderNumber}`);
  };

  const handleContactMerchant = () => {
    // 处理联系商家请求
    console.log(`联系商家：订单 ${order.orderNumber}`);
  };

  const handleReorder = () => {
    // 处理重新下单请求
    console.log(`重新下单：订单 ${order.orderNumber}`);
  };

  const handleRateOrder = () => {
    // 处理评价订单请求
    console.log(`评价订单：订单 ${order.orderNumber}`);
  };

  const handleMoreOptions = () => {
    // 切换显示删除按钮
    setShowDeleteButton(true);
  };

  const handleDeleteOrder = () => {
    // 处理删除订单请求
    console.log(`删除订单：订单 ${order.orderNumber}`);
    // 这里可以添加确认对话框或直接删除
    // 删除后重置状态
    setShowDeleteButton(false);
  };

  // 检查是否为定制服务
  const isCustomService = order.shippingAddress === "";

  // 检查是否为未完成状态
  const isIncompleteOrder = ["pending", "processing", "shipped"].includes(order.status);

  return (
    <Card className={`relative group overflow-hidden backdrop-blur-sm border-slate-200/60 transition-all duration-300 ${
      isDeleteHovered 
        ? 'bg-slate-200/90 shadow-lg shadow-slate-300/50 border-slate-300/60' 
        : 'bg-white/70 hover:bg-white/90 hover:shadow-lg hover:shadow-slate-200/50 hover:-translate-y-0.5'
    }`}>
      {/* 装饰性渐变背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50/30 via-transparent to-blue-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      
      <CardHeader className="relative pb-4 border-b border-slate-100/80">
        <div className="flex items-start justify-between gap-4">
          <div className="space-y-1 flex-1">
            {/* 第一行：订单号 + 商家信息 */}
            <div className="flex items-center gap-3 flex-wrap">
              <h3 className="text-slate-900">订单号: {order.orderNumber}</h3>
              
              {/* 商家信息 - 头像和名字一排，无背景框 */}
              <div className="flex items-center gap-2">
                <div className="relative">
                  <ImageWithFallback
                    src={order.merchant.avatar}
                    alt={order.merchant.name}
                    className="w-6 h-6 rounded-full object-cover"
                  />
                  {/* 在线状态指示器 */}
                  <div className={`absolute -top-0.5 -right-0.5 w-2 h-2 rounded-full border border-white ${
                    order.merchant.isOnline 
                      ? 'bg-green-500' 
                      : 'bg-slate-400'
                  }`} />
                </div>
                <span className="text-sm text-slate-700 truncate max-w-32">
                  {order.merchant.name}
                </span>
                <span className={`text-xs ${order.merchant.isOnline ? 'text-green-600' : 'text-slate-400'}`}>
                  {order.merchant.isOnline ? '在线' : '离线'}
                </span>
              </div>
            </div>
            
            {/* 第二行：订单日期 - 减少上方间距 */}
            <p className="text-slate-600 text-sm">{formatDate(order.date)}</p>
          </div>
          
          <div className="flex flex-col items-end gap-2 flex-shrink-0">
            {/* 三个点按钮/删除按钮 - 移到最顶部 */}
            {showDeleteButton ? (
              <Button 
                variant="ghost" 
                size="sm"
                onClick={handleDeleteOrder}
                onMouseEnter={() => setIsDeleteHovered(true)}
                onMouseLeave={() => {
                  setIsDeleteHovered(false);
                  setShowDeleteButton(false);
                }}
                className="h-8 px-3 py-1 hover:bg-red-100/80 text-red-500 hover:text-red-600 transition-all duration-200 gap-1.5"
              >
                <Trash2 className="h-4 w-4" />
                <span className="text-sm">删除</span>
              </Button>
            ) : (
              <Button 
                variant="ghost" 
                size="sm"
                onClick={handleMoreOptions}
                className="h-8 w-8 p-0 hover:bg-slate-100/80 transition-colors duration-200"
              >
                <MoreHorizontal className="h-4 w-4 text-slate-500" />
              </Button>
            )}
            
            {/* 时间信息和状态标签 - 同一行显示 */}
            <div className="flex items-center gap-2">
              {/* 预计完成时间 - 只在未完成状态显示 */}
              {isIncompleteOrder && order.estimatedCompletionTime && (
                <div className="flex items-center gap-1.5 text-xs text-slate-500 bg-slate-50/80 px-2 py-1 rounded-full">
                  <Clock className="h-3 w-3" />
                  <span>预计 {formatDate(order.estimatedCompletionTime)} 完成</span>
                </div>
              )}
              {/* 完成时间 - 只在已完成状态显示 */}
              {order.status === "delivered" && order.completedAt && (
                <span className="text-xs text-slate-500 bg-slate-50/80 px-2 py-1 rounded-full">
                  {formatDate(order.completedAt)} 完成
                </span>
              )}
              
              <OrderStatusBadge status={order.status} />
            </div>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="relative space-y-5 pt-5">
        {/* 商品列表 */}
        <div className="space-y-4">
          {order.items.map((item) => (
            <div key={item.id} className="flex items-center gap-4 group/item">
              <div className="relative">
                <ImageWithFallback
                  src={item.image}
                  alt={item.name}
                  className="w-14 h-14 rounded-lg object-cover bg-slate-100 border border-slate-200/60"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/5 to-transparent rounded-lg opacity-0 group-hover/item:opacity-100 transition-opacity duration-200" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-slate-900 truncate">{item.name}</p>
                <p className="text-sm text-slate-500">
                  数量: {item.quantity} × {formatPrice(item.price)}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* 配送地址或服务类型 */}
        {isCustomService ? (
          <div className="flex items-center gap-2 text-sm text-slate-600 bg-blue-50/50 px-3 py-2 rounded-lg border border-blue-100/60">
            <Settings className="h-4 w-4 text-blue-600" />
            <span>定制服务</span>
          </div>
        ) : (
          <div className="flex items-center gap-2 text-sm text-slate-600 bg-slate-50/50 px-3 py-2 rounded-lg border border-slate-200/60">
            <Truck className="h-4 w-4 text-slate-500" />
            <span className="truncate">{order.shippingAddress}</span>
          </div>
        )}

        {/* 总价和操作按钮 */}
        <div className="flex items-end justify-between pt-4 border-t border-slate-100/80">
          <div className="space-y-1">
            <p className="text-sm text-slate-500">订单总额</p>
            <p className="text-slate-900 text-xl">{formatPrice(order.total)}</p>
          </div>
          <div className="flex flex-wrap gap-2">
            {/* 查看详情按钮 - 所有订单都有 */}
            <Button 
              variant="outline" 
              size="sm"
              onClick={onViewDetail}
              className="bg-white/80 hover:bg-white border-slate-200/80 hover:border-slate-300 text-slate-700 hover:text-slate-900 backdrop-blur-sm transition-all duration-200"
            >
              <Eye className="h-4 w-4 mr-2" />
              查看详情
            </Button>
            
            {/* 联系商家按钮 - 所有订单都有 */}
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleContactMerchant}
              className="bg-white/80 hover:bg-white border-slate-200/80 hover:border-slate-300 text-slate-700 hover:text-slate-900 backdrop-blur-sm transition-all duration-200"
            >
              <MessageCircle className="h-4 w-4 mr-2" />
              联系商家
            </Button>
            
            {/* 评价按钮 - 只有已完成的订单显示，使用中性灰色 */}
            {order.status === "delivered" && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleRateOrder}
                className="bg-white/80 hover:bg-white border-slate-200/80 hover:border-slate-300 text-slate-700 hover:text-slate-900 backdrop-blur-sm transition-all duration-200"
              >
                <Star className="h-4 w-4 mr-2" />
                评价
              </Button>
            )}
            
            {/* 开发票/重新下单按钮 - 根据订单状态显示 */}
            {order.status === "cancelled" ? (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleReorder}
                className="bg-blue-50/80 hover:bg-blue-100/80 border-blue-200/80 hover:border-blue-300 text-blue-700 hover:text-blue-800 backdrop-blur-sm transition-all duration-200"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                重新下单
              </Button>
            ) : order.status === "delivered" && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleInvoiceRequest}
                className="bg-white/80 hover:bg-white border-slate-200/80 hover:border-slate-300 text-slate-700 hover:text-slate-900 backdrop-blur-sm transition-all duration-200"
              >
                <Receipt className="h-4 w-4 mr-2" />
                开发票
              </Button>
            )}
            
            {/* 根据订单状态显示不同按钮 */}
            {order.status === "pending" && (
              <Button 
                size="sm"
                className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 active:scale-95"
              >
                立即付款
              </Button>
            )}
            
            {order.status === "shipped" && (
              <Button 
                size="sm"
                className="bg-green-600 hover:bg-green-700 text-white shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 active:scale-95"
              >
                确认收���
              </Button>
            )}
            
            {/* 申请售后按钮 - 只有已完成的订单显示，使用中性灰色 */}
            {order.status === "delivered" && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={handleAfterSalesRequest}
                className="bg-white/80 hover:bg-white border-slate-200/80 hover:border-slate-300 text-slate-700 hover:text-slate-900 backdrop-blur-sm transition-all duration-200"
              >
                <Shield className="h-4 w-4 mr-2" />
                申请售后
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}