{"name": "my-nextjs-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.6", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.13.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@stripe/stripe-js": "^7.7.0", "@tanstack/react-query": "^5.83.0", "axios": "^1.11.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.534.0", "multer": "^2.0.2", "mysql2": "^3.14.3", "next": "15.4.5", "next-auth": "^4.24.11", "nodemailer": "^7.0.5", "prisma": "^6.13.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.61.1", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.16.0", "react-loading-skeleton": "^3.5.0", "react-query": "^3.39.3", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "redis": "^5.6.1", "sharp": "^0.34.3", "stripe": "^18.4.0", "swr": "^2.3.4", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}