import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Clock, 
  Package, 
  MessageCircle, 
  Star,
  ChevronRight,
  Calendar,
  User
} from "lucide-react";
import { Order } from "@/types/order";

interface OrderCardProps {
  order: Order;
  onViewDetail: () => void;
}

export function OrderCard({ order, onViewDetail }: OrderCardProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "delivered":
        return "bg-green-100 text-green-800 border-green-200";
      case "shipped":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "processing":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "pending":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "cancelled":
        return "bg-red-100 text-red-800 border-red-200";
      case "aftersale":
        return "bg-purple-100 text-purple-800 border-purple-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "delivered":
        return "已完成";
      case "shipped":
        return "待交付";
      case "processing":
        return "进行中";
      case "pending":
        return "待处理";
      case "cancelled":
        return "已取消";
      case "aftersale":
        return "售后中";
      default:
        return "未知状态";
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getCurrentStepInfo = () => {
    const currentStep = order.workProgress.steps[order.workProgress.currentStep];
    return currentStep || order.workProgress.steps[0];
  };

  const currentStep = getCurrentStepInfo();

  return (
    <Card className="group bg-white/80 backdrop-blur-sm border-slate-200/60 hover:shadow-lg hover:shadow-slate-200/40 transition-all duration-300 cursor-pointer">
      <CardContent className="p-6" onClick={onViewDetail}>
        {/* 订单头部信息 */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h3 className="font-semibold text-slate-900">订单号: {order.orderNumber}</h3>
              <Badge className={`text-xs px-2 py-1 ${getStatusColor(order.status)}`}>
                {getStatusText(order.status)}
              </Badge>
            </div>
            <div className="flex items-center gap-4 text-sm text-slate-600">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>{formatDate(order.date)}</span>
              </div>
              {order.estimatedCompletionTime && (
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>预计 {formatDate(order.estimatedCompletionTime)}</span>
                </div>
              )}
            </div>
          </div>
          
          <div className="text-right">
            <div className="text-2xl font-bold text-slate-900 mb-1">
              ¥{order.total.toFixed(2)}
            </div>
            <ChevronRight className="h-5 w-5 text-slate-400 group-hover:text-slate-600 transition-colors ml-auto" />
          </div>
        </div>

        {/* 商家信息 */}
        <div className="flex items-center gap-3 mb-4 p-3 bg-slate-50/80 rounded-lg">
          <Avatar className="h-10 w-10">
            <AvatarImage src={order.merchant.avatar} />
            <AvatarFallback>{order.merchant.name[0]}</AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <span className="font-medium text-slate-900">{order.merchant.name}</span>
              <div className={`w-2 h-2 rounded-full ${
                order.merchant.isOnline ? 'bg-green-500' : 'bg-slate-400'
              }`}></div>
            </div>
            <div className="flex items-center gap-3 text-sm text-slate-600">
              <div className="flex items-center gap-1">
                <Star className="h-3 w-3 text-yellow-500 fill-current" />
                <span>{order.merchant.rating}</span>
              </div>
              <span>{order.merchant.phone}</span>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            className="flex items-center gap-1"
            onClick={(e) => {
              e.stopPropagation();
              console.log("联系商家");
            }}
          >
            <MessageCircle className="h-4 w-4" />
            联系
          </Button>
        </div>

        {/* 商品列表 */}
        <div className="space-y-3 mb-4">
          {order.items.map((item, index) => (
            <div key={item.id} className="flex items-center gap-4">
              <div className="relative">
                <img 
                  src={item.image} 
                  alt={item.name}
                  className="w-16 h-16 object-cover rounded-lg bg-slate-100 border border-slate-200/60"
                />
                {item.quantity > 1 && (
                  <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center text-xs bg-blue-600">
                    {item.quantity}
                  </Badge>
                )}
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-slate-900 line-clamp-1 mb-1">{item.name}</h4>
                {item.specifications && item.specifications.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {item.specifications.slice(0, 2).map((spec, idx) => (
                      <Badge key={idx} variant="secondary" className="text-xs px-2 py-0.5">
                        {spec}
                      </Badge>
                    ))}
                    {item.specifications.length > 2 && (
                      <Badge variant="secondary" className="text-xs px-2 py-0.5">
                        +{item.specifications.length - 2}
                      </Badge>
                    )}
                  </div>
                )}
              </div>
              <div className="text-right">
                <div className="font-semibold text-slate-900">¥{item.price.toFixed(2)}</div>
              </div>
            </div>
          ))}
        </div>

        {/* 工作进度 */}
        {order.status === "processing" && currentStep && (
          <div className="bg-blue-50/80 rounded-lg p-3">
            <div className="flex items-center gap-2 mb-2">
              <Package className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">当前进度</span>
            </div>
            <div className="text-sm text-blue-800">
              <span className="font-medium">{currentStep.label}</span>
              <span className="text-blue-600 ml-2">{currentStep.description}</span>
            </div>
            
            {/* 进度条 */}
            <div className="mt-3">
              <div className="flex justify-between text-xs text-blue-600 mb-1">
                <span>进度</span>
                <span>{Math.round(((order.workProgress.currentStep + 1) / order.workProgress.steps.length) * 100)}%</span>
              </div>
              <div className="w-full bg-blue-200/50 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ 
                    width: `${((order.workProgress.currentStep + 1) / order.workProgress.steps.length) * 100}%` 
                  }}
                ></div>
              </div>
            </div>
          </div>
        )}

        {/* 已完成状态 */}
        {order.status === "delivered" && order.completedAt && (
          <div className="bg-green-50/80 rounded-lg p-3">
            <div className="flex items-center gap-2 text-sm text-green-800">
              <Package className="h-4 w-4 text-green-600" />
              <span>已于 {formatDate(order.completedAt)} 完成交付</span>
            </div>
          </div>
        )}

        {/* 待交付状态 */}
        {order.status === "shipped" && order.trackingNumber && (
          <div className="bg-blue-50/80 rounded-lg p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-sm text-blue-800">
                <Package className="h-4 w-4 text-blue-600" />
                <span>等待交付确认</span>
              </div>
              <Badge variant="outline" className="text-xs">
                {order.trackingNumber}
              </Badge>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
