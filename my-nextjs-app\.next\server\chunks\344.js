"use strict";exports.id=344,exports.ids=[344],exports.modules={1508:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(1209).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},2125:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(1209).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5207:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(1209).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},5587:(a,b,c)=>{c.d(b,{UC:()=>db,In:()=>c9,q7:()=>dd,VF:()=>df,p4:()=>de,ZL:()=>da,bL:()=>c6,wn:()=>dh,PP:()=>dg,l9:()=>c7,WT:()=>c8,LM:()=>dc});var d,e,f,g=c(5561),h=c.t(g,2),i=c(5628);function j(a,[b,c]){return Math.min(c,Math.max(b,a))}function k(a,b,{checkForDefaultPrevented:c=!0}={}){return function(d){if(a?.(d),!1===c||!d.defaultPrevented)return b?.(d)}}var l=c(589),m=c(8329),n=c(4955),o=c(568),p=new WeakMap;function q(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=r(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function r(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],p.set(this,!0)}set(a,b){return p.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=r(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let j=this.size+ +!e;g<0&&h++;let k=[...this.#a],l=!1;for(let a=h;a<j;a++)if(h===a){let f=k[a];k[a]===b&&(f=k[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{l||k[a-1]!==b||(l=!0);let c=k[l?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=q(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=q(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return q(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}});var s=g.createContext(void 0),t=c(5530),u=c(2983),v="dismissableLayer.update",w=g.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),x=g.forwardRef((a,b)=>{let{disableOutsidePointerEvents:c=!1,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:h,onInteractOutside:i,onDismiss:j,...l}=a,n=g.useContext(w),[p,q]=g.useState(null),r=p?.ownerDocument??globalThis?.document,[,s]=g.useState({}),x=(0,m.s)(b,a=>q(a)),A=Array.from(n.layers),[B]=[...n.layersWithOutsidePointerEventsDisabled].slice(-1),C=A.indexOf(B),D=p?A.indexOf(p):-1,E=n.layersWithOutsidePointerEventsDisabled.size>0,F=D>=C,G=function(a,b=globalThis?.document){let c=(0,u.c)(a),d=g.useRef(!1),e=g.useRef(()=>{});return g.useEffect(()=>{let a=a=>{if(a.target&&!d.current){let d=function(){z("dismissableLayer.pointerDownOutside",c,f,{discrete:!0})},f={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",e.current),e.current=d,b.addEventListener("click",e.current,{once:!0})):d()}else b.removeEventListener("click",e.current);d.current=!1},f=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(f),b.removeEventListener("pointerdown",a),b.removeEventListener("click",e.current)}},[b,c]),{onPointerDownCapture:()=>d.current=!0}}(a=>{let b=a.target,c=[...n.branches].some(a=>a.contains(b));F&&!c&&(f?.(a),i?.(a),a.defaultPrevented||j?.())},r),H=function(a,b=globalThis?.document){let c=(0,u.c)(a),d=g.useRef(!1);return g.useEffect(()=>{let a=a=>{a.target&&!d.current&&z("dismissableLayer.focusOutside",c,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,c]),{onFocusCapture:()=>d.current=!0,onBlurCapture:()=>d.current=!1}}(a=>{let b=a.target;![...n.branches].some(a=>a.contains(b))&&(h?.(a),i?.(a),a.defaultPrevented||j?.())},r);return!function(a,b=globalThis?.document){let c=(0,u.c)(a);g.useEffect(()=>{let a=a=>{"Escape"===a.key&&c(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[c,b])}(a=>{D===n.layers.size-1&&(d?.(a),!a.defaultPrevented&&j&&(a.preventDefault(),j()))},r),g.useEffect(()=>{if(p)return c&&(0===n.layersWithOutsidePointerEventsDisabled.size&&(e=r.body.style.pointerEvents,r.body.style.pointerEvents="none"),n.layersWithOutsidePointerEventsDisabled.add(p)),n.layers.add(p),y(),()=>{c&&1===n.layersWithOutsidePointerEventsDisabled.size&&(r.body.style.pointerEvents=e)}},[p,r,c,n]),g.useEffect(()=>()=>{p&&(n.layers.delete(p),n.layersWithOutsidePointerEventsDisabled.delete(p),y())},[p,n]),g.useEffect(()=>{let a=()=>s({});return document.addEventListener(v,a),()=>document.removeEventListener(v,a)},[]),(0,o.jsx)(t.sG.div,{...l,ref:x,style:{pointerEvents:E?F?"auto":"none":void 0,...a.style},onFocusCapture:k(a.onFocusCapture,H.onFocusCapture),onBlurCapture:k(a.onBlurCapture,H.onBlurCapture),onPointerDownCapture:k(a.onPointerDownCapture,G.onPointerDownCapture)})});function y(){let a=new CustomEvent(v);document.dispatchEvent(a)}function z(a,b,c,{discrete:d}){let e=c.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});b&&e.addEventListener(a,b,{once:!0}),d?(0,t.hO)(e,f):e.dispatchEvent(f)}x.displayName="DismissableLayer",g.forwardRef((a,b)=>{let c=g.useContext(w),d=g.useRef(null),e=(0,m.s)(b,d);return g.useEffect(()=>{let a=d.current;if(a)return c.branches.add(a),()=>{c.branches.delete(a)}},[c.branches]),(0,o.jsx)(t.sG.div,{...a,ref:e})}).displayName="DismissableLayerBranch";var A=0;function B(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}var C="focusScope.autoFocusOnMount",D="focusScope.autoFocusOnUnmount",E={bubbles:!1,cancelable:!0},F=g.forwardRef((a,b)=>{let{loop:c=!1,trapped:d=!1,onMountAutoFocus:e,onUnmountAutoFocus:f,...h}=a,[i,j]=g.useState(null),k=(0,u.c)(e),l=(0,u.c)(f),n=g.useRef(null),p=(0,m.s)(b,a=>j(a)),q=g.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;g.useEffect(()=>{if(d){let a=function(a){if(q.paused||!i)return;let b=a.target;i.contains(b)?n.current=b:I(n.current,{select:!0})},b=function(a){if(q.paused||!i)return;let b=a.relatedTarget;null!==b&&(i.contains(b)||I(n.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&I(i)});return i&&c.observe(i,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[d,i,q.paused]),g.useEffect(()=>{if(i){J.add(q);let a=document.activeElement;if(!i.contains(a)){let b=new CustomEvent(C,E);i.addEventListener(C,k),i.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(I(d,{select:b}),document.activeElement!==c)return}(G(i).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&I(i))}return()=>{i.removeEventListener(C,k),setTimeout(()=>{let b=new CustomEvent(D,E);i.addEventListener(D,l),i.dispatchEvent(b),b.defaultPrevented||I(a??document.body,{select:!0}),i.removeEventListener(D,l),J.remove(q)},0)}}},[i,k,l,q]);let r=g.useCallback(a=>{if(!c&&!d||q.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,e=document.activeElement;if(b&&e){let b=a.currentTarget,[d,f]=function(a){let b=G(a);return[H(b,a),H(b.reverse(),a)]}(b);d&&f?a.shiftKey||e!==f?a.shiftKey&&e===d&&(a.preventDefault(),c&&I(f,{select:!0})):(a.preventDefault(),c&&I(d,{select:!0})):e===b&&a.preventDefault()}},[c,d,q.paused]);return(0,o.jsx)(t.sG.div,{tabIndex:-1,...h,ref:p,onKeyDown:r})});function G(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function H(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function I(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}F.displayName="FocusScope";var J=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=K(a,b)).unshift(b)},remove(b){a=K(a,b),a[0]?.resume()}}}();function K(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}var L=c(4393),M=h[" useId ".trim().toString()]||(()=>void 0),N=0;function O(a){let[b,c]=g.useState(M());return(0,L.N)(()=>{a||c(a=>a??String(N++))},[a]),a||(b?`radix-${b}`:"")}let P=["top","right","bottom","left"],Q=Math.min,R=Math.max,S=Math.round,T=Math.floor,U=a=>({x:a,y:a}),V={left:"right",right:"left",bottom:"top",top:"bottom"},W={start:"end",end:"start"};function X(a,b){return"function"==typeof a?a(b):a}function Y(a){return a.split("-")[0]}function Z(a){return a.split("-")[1]}function $(a){return"x"===a?"y":"x"}function _(a){return"y"===a?"height":"width"}let aa=new Set(["top","bottom"]);function ab(a){return aa.has(Y(a))?"y":"x"}function ac(a){return a.replace(/start|end/g,a=>W[a])}let ad=["left","right"],ae=["right","left"],af=["top","bottom"],ag=["bottom","top"];function ah(a){return a.replace(/left|right|bottom|top/g,a=>V[a])}function ai(a){return"number"!=typeof a?{top:0,right:0,bottom:0,left:0,...a}:{top:a,right:a,bottom:a,left:a}}function aj(a){let{x:b,y:c,width:d,height:e}=a;return{width:d,height:e,top:c,left:b,right:b+d,bottom:c+e,x:b,y:c}}function ak(a,b,c){let d,{reference:e,floating:f}=a,g=ab(b),h=$(ab(b)),i=_(h),j=Y(b),k="y"===g,l=e.x+e.width/2-f.width/2,m=e.y+e.height/2-f.height/2,n=e[i]/2-f[i]/2;switch(j){case"top":d={x:l,y:e.y-f.height};break;case"bottom":d={x:l,y:e.y+e.height};break;case"right":d={x:e.x+e.width,y:m};break;case"left":d={x:e.x-f.width,y:m};break;default:d={x:e.x,y:e.y}}switch(Z(b)){case"start":d[h]-=n*(c&&k?-1:1);break;case"end":d[h]+=n*(c&&k?-1:1)}return d}let al=async(a,b,c)=>{let{placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c,h=f.filter(Boolean),i=await (null==g.isRTL?void 0:g.isRTL(b)),j=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:l}=ak(j,d,i),m=d,n={},o=0;for(let c=0;c<h.length;c++){let{name:f,fn:p}=h[c],{x:q,y:r,data:s,reset:t}=await p({x:k,y:l,initialPlacement:d,placement:m,strategy:e,middlewareData:n,rects:j,platform:g,elements:{reference:a,floating:b}});k=null!=q?q:k,l=null!=r?r:l,n={...n,[f]:{...n[f],...s}},t&&o<=50&&(o++,"object"==typeof t&&(t.placement&&(m=t.placement),t.rects&&(j=!0===t.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):t.rects),{x:k,y:l}=ak(j,m,i)),c=-1)}return{x:k,y:l,placement:m,strategy:e,middlewareData:n}};async function am(a,b){var c;void 0===b&&(b={});let{x:d,y:e,platform:f,rects:g,elements:h,strategy:i}=a,{boundary:j="clippingAncestors",rootBoundary:k="viewport",elementContext:l="floating",altBoundary:m=!1,padding:n=0}=X(b,a),o=ai(n),p=h[m?"floating"===l?"reference":"floating":l],q=aj(await f.getClippingRect({element:null==(c=await (null==f.isElement?void 0:f.isElement(p)))||c?p:p.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:j,rootBoundary:k,strategy:i})),r="floating"===l?{x:d,y:e,width:g.floating.width,height:g.floating.height}:g.reference,s=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating)),t=await (null==f.isElement?void 0:f.isElement(s))&&await (null==f.getScale?void 0:f.getScale(s))||{x:1,y:1},u=aj(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:r,offsetParent:s,strategy:i}):r);return{top:(q.top-u.top+o.top)/t.y,bottom:(u.bottom-q.bottom+o.bottom)/t.y,left:(q.left-u.left+o.left)/t.x,right:(u.right-q.right+o.right)/t.x}}function an(a,b){return{top:a.top-b.height,right:a.right-b.width,bottom:a.bottom-b.height,left:a.left-b.width}}function ao(a){return P.some(b=>a[b]>=0)}let ap=new Set(["left","top"]);async function aq(a,b){let{placement:c,platform:d,elements:e}=a,f=await (null==d.isRTL?void 0:d.isRTL(e.floating)),g=Y(c),h=Z(c),i="y"===ab(c),j=ap.has(g)?-1:1,k=f&&i?-1:1,l=X(b,a),{mainAxis:m,crossAxis:n,alignmentAxis:o}="number"==typeof l?{mainAxis:l,crossAxis:0,alignmentAxis:null}:{mainAxis:l.mainAxis||0,crossAxis:l.crossAxis||0,alignmentAxis:l.alignmentAxis};return h&&"number"==typeof o&&(n="end"===h?-1*o:o),i?{x:n*k,y:m*j}:{x:m*j,y:n*k}}function ar(){return"undefined"!=typeof window}function as(a){return av(a)?(a.nodeName||"").toLowerCase():"#document"}function at(a){var b;return(null==a||null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function au(a){var b;return null==(b=(av(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function av(a){return!!ar()&&(a instanceof Node||a instanceof at(a).Node)}function aw(a){return!!ar()&&(a instanceof Element||a instanceof at(a).Element)}function ax(a){return!!ar()&&(a instanceof HTMLElement||a instanceof at(a).HTMLElement)}function ay(a){return!!ar()&&"undefined"!=typeof ShadowRoot&&(a instanceof ShadowRoot||a instanceof at(a).ShadowRoot)}let az=new Set(["inline","contents"]);function aA(a){let{overflow:b,overflowX:c,overflowY:d,display:e}=aL(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!az.has(e)}let aB=new Set(["table","td","th"]),aC=[":popover-open",":modal"];function aD(a){return aC.some(b=>{try{return a.matches(b)}catch(a){return!1}})}let aE=["transform","translate","scale","rotate","perspective"],aF=["transform","translate","scale","rotate","perspective","filter"],aG=["paint","layout","strict","content"];function aH(a){let b=aI(),c=aw(a)?aL(a):a;return aE.some(a=>!!c[a]&&"none"!==c[a])||!!c.containerType&&"normal"!==c.containerType||!b&&!!c.backdropFilter&&"none"!==c.backdropFilter||!b&&!!c.filter&&"none"!==c.filter||aF.some(a=>(c.willChange||"").includes(a))||aG.some(a=>(c.contain||"").includes(a))}function aI(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let aJ=new Set(["html","body","#document"]);function aK(a){return aJ.has(as(a))}function aL(a){return at(a).getComputedStyle(a)}function aM(a){return aw(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function aN(a){if("html"===as(a))return a;let b=a.assignedSlot||a.parentNode||ay(a)&&a.host||au(a);return ay(b)?b.host:b}function aO(a,b,c){var d;void 0===b&&(b=[]),void 0===c&&(c=!0);let e=function a(b){let c=aN(b);return aK(c)?b.ownerDocument?b.ownerDocument.body:b.body:ax(c)&&aA(c)?c:a(c)}(a),f=e===(null==(d=a.ownerDocument)?void 0:d.body),g=at(e);if(f){let a=aP(g);return b.concat(g,g.visualViewport||[],aA(e)?e:[],a&&c?aO(a):[])}return b.concat(e,aO(e,[],c))}function aP(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function aQ(a){let b=aL(a),c=parseFloat(b.width)||0,d=parseFloat(b.height)||0,e=ax(a),f=e?a.offsetWidth:c,g=e?a.offsetHeight:d,h=S(c)!==f||S(d)!==g;return h&&(c=f,d=g),{width:c,height:d,$:h}}function aR(a){return aw(a)?a:a.contextElement}function aS(a){let b=aR(a);if(!ax(b))return U(1);let c=b.getBoundingClientRect(),{width:d,height:e,$:f}=aQ(b),g=(f?S(c.width):c.width)/d,h=(f?S(c.height):c.height)/e;return g&&Number.isFinite(g)||(g=1),h&&Number.isFinite(h)||(h=1),{x:g,y:h}}let aT=U(0);function aU(a){let b=at(a);return aI()&&b.visualViewport?{x:b.visualViewport.offsetLeft,y:b.visualViewport.offsetTop}:aT}function aV(a,b,c,d){var e;void 0===b&&(b=!1),void 0===c&&(c=!1);let f=a.getBoundingClientRect(),g=aR(a),h=U(1);b&&(d?aw(d)&&(h=aS(d)):h=aS(a));let i=(void 0===(e=c)&&(e=!1),d&&(!e||d===at(g))&&e)?aU(g):U(0),j=(f.left+i.x)/h.x,k=(f.top+i.y)/h.y,l=f.width/h.x,m=f.height/h.y;if(g){let a=at(g),b=d&&aw(d)?at(d):d,c=a,e=aP(c);for(;e&&d&&b!==c;){let a=aS(e),b=e.getBoundingClientRect(),d=aL(e),f=b.left+(e.clientLeft+parseFloat(d.paddingLeft))*a.x,g=b.top+(e.clientTop+parseFloat(d.paddingTop))*a.y;j*=a.x,k*=a.y,l*=a.x,m*=a.y,j+=f,k+=g,e=aP(c=at(e))}}return aj({width:l,height:m,x:j,y:k})}function aW(a,b){let c=aM(a).scrollLeft;return b?b.left+c:aV(au(a)).left+c}function aX(a,b,c){void 0===c&&(c=!1);let d=a.getBoundingClientRect();return{x:d.left+b.scrollLeft-(c?0:aW(a,d)),y:d.top+b.scrollTop}}let aY=new Set(["absolute","fixed"]);function aZ(a,b,c){let d;if("viewport"===b)d=function(a,b){let c=at(a),d=au(a),e=c.visualViewport,f=d.clientWidth,g=d.clientHeight,h=0,i=0;if(e){f=e.width,g=e.height;let a=aI();(!a||a&&"fixed"===b)&&(h=e.offsetLeft,i=e.offsetTop)}return{width:f,height:g,x:h,y:i}}(a,c);else if("document"===b)d=function(a){let b=au(a),c=aM(a),d=a.ownerDocument.body,e=R(b.scrollWidth,b.clientWidth,d.scrollWidth,d.clientWidth),f=R(b.scrollHeight,b.clientHeight,d.scrollHeight,d.clientHeight),g=-c.scrollLeft+aW(a),h=-c.scrollTop;return"rtl"===aL(d).direction&&(g+=R(b.clientWidth,d.clientWidth)-e),{width:e,height:f,x:g,y:h}}(au(a));else if(aw(b))d=function(a,b){let c=aV(a,!0,"fixed"===b),d=c.top+a.clientTop,e=c.left+a.clientLeft,f=ax(a)?aS(a):U(1),g=a.clientWidth*f.x,h=a.clientHeight*f.y;return{width:g,height:h,x:e*f.x,y:d*f.y}}(b,c);else{let c=aU(a);d={x:b.x-c.x,y:b.y-c.y,width:b.width,height:b.height}}return aj(d)}function a$(a){return"static"===aL(a).position}function a_(a,b){if(!ax(a)||"fixed"===aL(a).position)return null;if(b)return b(a);let c=a.offsetParent;return au(a)===c&&(c=c.ownerDocument.body),c}function a0(a,b){var c;let d=at(a);if(aD(a))return d;if(!ax(a)){let b=aN(a);for(;b&&!aK(b);){if(aw(b)&&!a$(b))return b;b=aN(b)}return d}let e=a_(a,b);for(;e&&(c=e,aB.has(as(c)))&&a$(e);)e=a_(e,b);return e&&aK(e)&&a$(e)&&!aH(e)?d:e||function(a){let b=aN(a);for(;ax(b)&&!aK(b);){if(aH(b))return b;if(aD(b))break;b=aN(b)}return null}(a)||d}let a1=async function(a){let b=this.getOffsetParent||a0,c=this.getDimensions,d=await c(a.floating);return{reference:function(a,b,c){let d=ax(b),e=au(b),f="fixed"===c,g=aV(a,!0,f,b),h={scrollLeft:0,scrollTop:0},i=U(0);if(d||!d&&!f)if(("body"!==as(b)||aA(e))&&(h=aM(b)),d){let a=aV(b,!0,f,b);i.x=a.x+b.clientLeft,i.y=a.y+b.clientTop}else e&&(i.x=aW(e));f&&!d&&e&&(i.x=aW(e));let j=!e||d||f?U(0):aX(e,h);return{x:g.left+h.scrollLeft-i.x-j.x,y:g.top+h.scrollTop-i.y-j.y,width:g.width,height:g.height}}(a.reference,await b(a.floating),a.strategy),floating:{x:0,y:0,width:d.width,height:d.height}}},a2={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){let{elements:b,rect:c,offsetParent:d,strategy:e}=a,f="fixed"===e,g=au(d),h=!!b&&aD(b.floating);if(d===g||h&&f)return c;let i={scrollLeft:0,scrollTop:0},j=U(1),k=U(0),l=ax(d);if((l||!l&&!f)&&(("body"!==as(d)||aA(g))&&(i=aM(d)),ax(d))){let a=aV(d);j=aS(d),k.x=a.x+d.clientLeft,k.y=a.y+d.clientTop}let m=!g||l||f?U(0):aX(g,i,!0);return{width:c.width*j.x,height:c.height*j.y,x:c.x*j.x-i.scrollLeft*j.x+k.x+m.x,y:c.y*j.y-i.scrollTop*j.y+k.y+m.y}},getDocumentElement:au,getClippingRect:function(a){let{element:b,boundary:c,rootBoundary:d,strategy:e}=a,f=[..."clippingAncestors"===c?aD(b)?[]:function(a,b){let c=b.get(a);if(c)return c;let d=aO(a,[],!1).filter(a=>aw(a)&&"body"!==as(a)),e=null,f="fixed"===aL(a).position,g=f?aN(a):a;for(;aw(g)&&!aK(g);){let b=aL(g),c=aH(g);c||"fixed"!==b.position||(e=null),(f?!c&&!e:!c&&"static"===b.position&&!!e&&aY.has(e.position)||aA(g)&&!c&&function a(b,c){let d=aN(b);return!(d===c||!aw(d)||aK(d))&&("fixed"===aL(d).position||a(d,c))}(a,g))?d=d.filter(a=>a!==g):e=b,g=aN(g)}return b.set(a,d),d}(b,this._c):[].concat(c),d],g=f[0],h=f.reduce((a,c)=>{let d=aZ(b,c,e);return a.top=R(d.top,a.top),a.right=Q(d.right,a.right),a.bottom=Q(d.bottom,a.bottom),a.left=R(d.left,a.left),a},aZ(b,g,e));return{width:h.right-h.left,height:h.bottom-h.top,x:h.left,y:h.top}},getOffsetParent:a0,getElementRects:a1,getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){let{width:b,height:c}=aQ(a);return{width:b,height:c}},getScale:aS,isElement:aw,isRTL:function(a){return"rtl"===aL(a).direction}};function a3(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}let a4=a=>({name:"arrow",options:a,async fn(b){let{x:c,y:d,placement:e,rects:f,platform:g,elements:h,middlewareData:i}=b,{element:j,padding:k=0}=X(a,b)||{};if(null==j)return{};let l=ai(k),m={x:c,y:d},n=$(ab(e)),o=_(n),p=await g.getDimensions(j),q="y"===n,r=q?"clientHeight":"clientWidth",s=f.reference[o]+f.reference[n]-m[n]-f.floating[o],t=m[n]-f.reference[n],u=await (null==g.getOffsetParent?void 0:g.getOffsetParent(j)),v=u?u[r]:0;v&&await (null==g.isElement?void 0:g.isElement(u))||(v=h.floating[r]||f.floating[o]);let w=v/2-p[o]/2-1,x=Q(l[q?"top":"left"],w),y=Q(l[q?"bottom":"right"],w),z=v-p[o]-y,A=v/2-p[o]/2+(s/2-t/2),B=R(x,Q(A,z)),C=!i.arrow&&null!=Z(e)&&A!==B&&f.reference[o]/2-(A<x?x:y)-p[o]/2<0,D=C?A<x?A-x:A-z:0;return{[n]:m[n]+D,data:{[n]:B,centerOffset:A-B-D,...C&&{alignmentOffset:D}},reset:C}}});var a5="undefined"!=typeof document?g.useLayoutEffect:function(){};function a6(a,b){let c,d,e;if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if((c=a.length)!==b.length)return!1;for(d=c;0!=d--;)if(!a6(a[d],b[d]))return!1;return!0}if((c=(e=Object.keys(a)).length)!==Object.keys(b).length)return!1;for(d=c;0!=d--;)if(!({}).hasOwnProperty.call(b,e[d]))return!1;for(d=c;0!=d--;){let c=e[d];if(("_owner"!==c||!a.$$typeof)&&!a6(a[c],b[c]))return!1}return!0}return a!=a&&b!=b}function a7(a){return"undefined"==typeof window?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function a8(a,b){let c=a7(a);return Math.round(b*c)/c}function a9(a){let b=g.useRef(a);return a5(()=>{b.current=a}),b}var ba=g.forwardRef((a,b)=>{let{children:c,width:d=10,height:e=5,...f}=a;return(0,o.jsx)(t.sG.svg,{...f,ref:b,width:d,height:e,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,o.jsx)("polygon",{points:"0,0 30,0 15,10"})})});ba.displayName="Arrow";var bb="Popper",[bc,bd]=(0,l.A)(bb),[be,bf]=bc(bb),bg=a=>{let{__scopePopper:b,children:c}=a,[d,e]=g.useState(null);return(0,o.jsx)(be,{scope:b,anchor:d,onAnchorChange:e,children:c})};bg.displayName=bb;var bh="PopperAnchor",bi=g.forwardRef((a,b)=>{let{__scopePopper:c,virtualRef:d,...e}=a,f=bf(bh,c),h=g.useRef(null),i=(0,m.s)(b,h);return g.useEffect(()=>{f.onAnchorChange(d?.current||h.current)}),d?null:(0,o.jsx)(t.sG.div,{...e,ref:i})});bi.displayName=bh;var bj="PopperContent",[bk,bl]=bc(bj),bm=g.forwardRef((a,b)=>{let{__scopePopper:c,side:d="bottom",sideOffset:e=0,align:f="center",alignOffset:h=0,arrowPadding:j=0,avoidCollisions:k=!0,collisionBoundary:l=[],collisionPadding:n=0,sticky:p="partial",hideWhenDetached:q=!1,updatePositionStrategy:r="optimized",onPlaced:s,...v}=a,w=bf(bj,c),[x,y]=g.useState(null),z=(0,m.s)(b,a=>y(a)),[A,B]=g.useState(null),C=function(a){let[b,c]=g.useState(void 0);return(0,L.N)(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let d,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;d=b.inlineSize,e=b.blockSize}else d=a.offsetWidth,e=a.offsetHeight;c({width:d,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}c(void 0)},[a]),b}(A),D=C?.width??0,E=C?.height??0,F="number"==typeof n?n:{top:0,right:0,bottom:0,left:0,...n},G=Array.isArray(l)?l:[l],H=G.length>0,I={padding:F,boundary:G.filter(bq),altBoundary:H},{refs:J,floatingStyles:K,placement:M,isPositioned:N,middlewareData:O}=function(a){void 0===a&&(a={});let{placement:b="bottom",strategy:c="absolute",middleware:d=[],platform:e,elements:{reference:f,floating:h}={},transform:j=!0,whileElementsMounted:k,open:l}=a,[m,n]=g.useState({x:0,y:0,strategy:c,placement:b,middlewareData:{},isPositioned:!1}),[o,p]=g.useState(d);a6(o,d)||p(d);let[q,r]=g.useState(null),[s,t]=g.useState(null),u=g.useCallback(a=>{a!==y.current&&(y.current=a,r(a))},[]),v=g.useCallback(a=>{a!==z.current&&(z.current=a,t(a))},[]),w=f||q,x=h||s,y=g.useRef(null),z=g.useRef(null),A=g.useRef(m),B=null!=k,C=a9(k),D=a9(e),E=a9(l),F=g.useCallback(()=>{if(!y.current||!z.current)return;let a={placement:b,strategy:c,middleware:o};D.current&&(a.platform=D.current),((a,b,c)=>{let d=new Map,e={platform:a2,...c},f={...e.platform,_c:d};return al(a,b,{...e,platform:f})})(y.current,z.current,a).then(a=>{let b={...a,isPositioned:!1!==E.current};G.current&&!a6(A.current,b)&&(A.current=b,i.flushSync(()=>{n(b)}))})},[o,b,c,D,E]);a5(()=>{!1===l&&A.current.isPositioned&&(A.current.isPositioned=!1,n(a=>({...a,isPositioned:!1})))},[l]);let G=g.useRef(!1);a5(()=>(G.current=!0,()=>{G.current=!1}),[]),a5(()=>{if(w&&(y.current=w),x&&(z.current=x),w&&x){if(C.current)return C.current(w,x,F);F()}},[w,x,F,C,B]);let H=g.useMemo(()=>({reference:y,floating:z,setReference:u,setFloating:v}),[u,v]),I=g.useMemo(()=>({reference:w,floating:x}),[w,x]),J=g.useMemo(()=>{let a={position:c,left:0,top:0};if(!I.floating)return a;let b=a8(I.floating,m.x),d=a8(I.floating,m.y);return j?{...a,transform:"translate("+b+"px, "+d+"px)",...a7(I.floating)>=1.5&&{willChange:"transform"}}:{position:c,left:b,top:d}},[c,j,I.floating,m.x,m.y]);return g.useMemo(()=>({...m,update:F,refs:H,elements:I,floatingStyles:J}),[m,F,H,I,J])}({strategy:"fixed",placement:d+("center"!==f?"-"+f:""),whileElementsMounted:(...a)=>(function(a,b,c,d){let e;void 0===d&&(d={});let{ancestorScroll:f=!0,ancestorResize:g=!0,elementResize:h="function"==typeof ResizeObserver,layoutShift:i="function"==typeof IntersectionObserver,animationFrame:j=!1}=d,k=aR(a),l=f||g?[...k?aO(k):[],...aO(b)]:[];l.forEach(a=>{f&&a.addEventListener("scroll",c,{passive:!0}),g&&a.addEventListener("resize",c)});let m=k&&i?function(a,b){let c,d=null,e=au(a);function f(){var a;clearTimeout(c),null==(a=d)||a.disconnect(),d=null}return!function g(h,i){void 0===h&&(h=!1),void 0===i&&(i=1),f();let j=a.getBoundingClientRect(),{left:k,top:l,width:m,height:n}=j;if(h||b(),!m||!n)return;let o=T(l),p=T(e.clientWidth-(k+m)),q={rootMargin:-o+"px "+-p+"px "+-T(e.clientHeight-(l+n))+"px "+-T(k)+"px",threshold:R(0,Q(1,i))||1},r=!0;function s(b){let d=b[0].intersectionRatio;if(d!==i){if(!r)return g();d?g(!1,d):c=setTimeout(()=>{g(!1,1e-7)},1e3)}1!==d||a3(j,a.getBoundingClientRect())||g(),r=!1}try{d=new IntersectionObserver(s,{...q,root:e.ownerDocument})}catch(a){d=new IntersectionObserver(s,q)}d.observe(a)}(!0),f}(k,c):null,n=-1,o=null;h&&(o=new ResizeObserver(a=>{let[d]=a;d&&d.target===k&&o&&(o.unobserve(b),cancelAnimationFrame(n),n=requestAnimationFrame(()=>{var a;null==(a=o)||a.observe(b)})),c()}),k&&!j&&o.observe(k),o.observe(b));let p=j?aV(a):null;return j&&function b(){let d=aV(a);p&&!a3(p,d)&&c(),p=d,e=requestAnimationFrame(b)}(),c(),()=>{var a;l.forEach(a=>{f&&a.removeEventListener("scroll",c),g&&a.removeEventListener("resize",c)}),null==m||m(),null==(a=o)||a.disconnect(),o=null,j&&cancelAnimationFrame(e)}})(...a,{animationFrame:"always"===r}),elements:{reference:w.anchor},middleware:[((a,b)=>({...function(a){return void 0===a&&(a=0),{name:"offset",options:a,async fn(b){var c,d;let{x:e,y:f,placement:g,middlewareData:h}=b,i=await aq(b,a);return g===(null==(c=h.offset)?void 0:c.placement)&&null!=(d=h.arrow)&&d.alignmentOffset?{}:{x:e+i.x,y:f+i.y,data:{...i,placement:g}}}}}(a),options:[a,b]}))({mainAxis:e+E,alignmentAxis:h}),k&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"shift",options:a,async fn(b){let{x:c,y:d,placement:e}=b,{mainAxis:f=!0,crossAxis:g=!1,limiter:h={fn:a=>{let{x:b,y:c}=a;return{x:b,y:c}}},...i}=X(a,b),j={x:c,y:d},k=await am(b,i),l=ab(Y(e)),m=$(l),n=j[m],o=j[l];if(f){let a="y"===m?"top":"left",b="y"===m?"bottom":"right",c=n+k[a],d=n-k[b];n=R(c,Q(n,d))}if(g){let a="y"===l?"top":"left",b="y"===l?"bottom":"right",c=o+k[a],d=o-k[b];o=R(c,Q(o,d))}let p=h.fn({...b,[m]:n,[l]:o});return{...p,data:{x:p.x-c,y:p.y-d,enabled:{[m]:f,[l]:g}}}}}}(a),options:[a,b]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===p?((a,b)=>({...function(a){return void 0===a&&(a={}),{options:a,fn(b){let{x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:i=!0,crossAxis:j=!0}=X(a,b),k={x:c,y:d},l=ab(e),m=$(l),n=k[m],o=k[l],p=X(h,b),q="number"==typeof p?{mainAxis:p,crossAxis:0}:{mainAxis:0,crossAxis:0,...p};if(i){let a="y"===m?"height":"width",b=f.reference[m]-f.floating[a]+q.mainAxis,c=f.reference[m]+f.reference[a]-q.mainAxis;n<b?n=b:n>c&&(n=c)}if(j){var r,s;let a="y"===m?"width":"height",b=ap.has(Y(e)),c=f.reference[l]-f.floating[a]+(b&&(null==(r=g.offset)?void 0:r[l])||0)+(b?0:q.crossAxis),d=f.reference[l]+f.reference[a]+(b?0:(null==(s=g.offset)?void 0:s[l])||0)-(b?q.crossAxis:0);o<c?o=c:o>d&&(o=d)}return{[m]:n,[l]:o}}}}(a),options:[a,b]}))():void 0,...I}),k&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"flip",options:a,async fn(b){var c,d,e,f,g;let{placement:h,middlewareData:i,rects:j,initialPlacement:k,platform:l,elements:m}=b,{mainAxis:n=!0,crossAxis:o=!0,fallbackPlacements:p,fallbackStrategy:q="bestFit",fallbackAxisSideDirection:r="none",flipAlignment:s=!0,...t}=X(a,b);if(null!=(c=i.arrow)&&c.alignmentOffset)return{};let u=Y(h),v=ab(k),w=Y(k)===k,x=await (null==l.isRTL?void 0:l.isRTL(m.floating)),y=p||(w||!s?[ah(k)]:function(a){let b=ah(a);return[ac(a),b,ac(b)]}(k)),z="none"!==r;!p&&z&&y.push(...function(a,b,c,d){let e=Z(a),f=function(a,b,c){switch(a){case"top":case"bottom":if(c)return b?ae:ad;return b?ad:ae;case"left":case"right":return b?af:ag;default:return[]}}(Y(a),"start"===c,d);return e&&(f=f.map(a=>a+"-"+e),b&&(f=f.concat(f.map(ac)))),f}(k,s,r,x));let A=[k,...y],B=await am(b,t),C=[],D=(null==(d=i.flip)?void 0:d.overflows)||[];if(n&&C.push(B[u]),o){let a=function(a,b,c){void 0===c&&(c=!1);let d=Z(a),e=$(ab(a)),f=_(e),g="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";return b.reference[f]>b.floating[f]&&(g=ah(g)),[g,ah(g)]}(h,j,x);C.push(B[a[0]],B[a[1]])}if(D=[...D,{placement:h,overflows:C}],!C.every(a=>a<=0)){let a=((null==(e=i.flip)?void 0:e.index)||0)+1,b=A[a];if(b&&("alignment"!==o||v===ab(b)||D.every(a=>ab(a.placement)!==v||a.overflows[0]>0)))return{data:{index:a,overflows:D},reset:{placement:b}};let c=null==(f=D.filter(a=>a.overflows[0]<=0).sort((a,b)=>a.overflows[1]-b.overflows[1])[0])?void 0:f.placement;if(!c)switch(q){case"bestFit":{let a=null==(g=D.filter(a=>{if(z){let b=ab(a.placement);return b===v||"y"===b}return!0}).map(a=>[a.placement,a.overflows.filter(a=>a>0).reduce((a,b)=>a+b,0)]).sort((a,b)=>a[1]-b[1])[0])?void 0:g[0];a&&(c=a);break}case"initialPlacement":c=k}if(h!==c)return{reset:{placement:c}}}return{}}}}(a),options:[a,b]}))({...I}),((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"size",options:a,async fn(b){var c,d;let e,f,{placement:g,rects:h,platform:i,elements:j}=b,{apply:k=()=>{},...l}=X(a,b),m=await am(b,l),n=Y(g),o=Z(g),p="y"===ab(g),{width:q,height:r}=h.floating;"top"===n||"bottom"===n?(e=n,f=o===(await (null==i.isRTL?void 0:i.isRTL(j.floating))?"start":"end")?"left":"right"):(f=n,e="end"===o?"top":"bottom");let s=r-m.top-m.bottom,t=q-m.left-m.right,u=Q(r-m[e],s),v=Q(q-m[f],t),w=!b.middlewareData.shift,x=u,y=v;if(null!=(c=b.middlewareData.shift)&&c.enabled.x&&(y=t),null!=(d=b.middlewareData.shift)&&d.enabled.y&&(x=s),w&&!o){let a=R(m.left,0),b=R(m.right,0),c=R(m.top,0),d=R(m.bottom,0);p?y=q-2*(0!==a||0!==b?a+b:R(m.left,m.right)):x=r-2*(0!==c||0!==d?c+d:R(m.top,m.bottom))}await k({...b,availableWidth:y,availableHeight:x});let z=await i.getDimensions(j.floating);return q!==z.width||r!==z.height?{reset:{rects:!0}}:{}}}}(a),options:[a,b]}))({...I,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),A&&((a,b)=>({...(a=>({name:"arrow",options:a,fn(b){let{element:c,padding:d}="function"==typeof a?a(b):a;return c&&({}).hasOwnProperty.call(c,"current")?null!=c.current?a4({element:c.current,padding:d}).fn(b):{}:c?a4({element:c,padding:d}).fn(b):{}}}))(a),options:[a,b]}))({element:A,padding:j}),br({arrowWidth:D,arrowHeight:E}),q&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"hide",options:a,async fn(b){let{rects:c}=b,{strategy:d="referenceHidden",...e}=X(a,b);switch(d){case"referenceHidden":{let a=an(await am(b,{...e,elementContext:"reference"}),c.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:ao(a)}}}case"escaped":{let a=an(await am(b,{...e,altBoundary:!0}),c.floating);return{data:{escapedOffsets:a,escaped:ao(a)}}}default:return{}}}}}(a),options:[a,b]}))({strategy:"referenceHidden",...I})]}),[P,S]=bs(M),U=(0,u.c)(s);(0,L.N)(()=>{N&&U?.()},[N,U]);let V=O.arrow?.x,W=O.arrow?.y,aa=O.arrow?.centerOffset!==0,[ai,aj]=g.useState();return(0,L.N)(()=>{x&&aj(window.getComputedStyle(x).zIndex)},[x]),(0,o.jsx)("div",{ref:J.setFloating,"data-radix-popper-content-wrapper":"",style:{...K,transform:N?K.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ai,"--radix-popper-transform-origin":[O.transformOrigin?.x,O.transformOrigin?.y].join(" "),...O.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,o.jsx)(bk,{scope:c,placedSide:P,onArrowChange:B,arrowX:V,arrowY:W,shouldHideArrow:aa,children:(0,o.jsx)(t.sG.div,{"data-side":P,"data-align":S,...v,ref:z,style:{...v.style,animation:N?void 0:"none"}})})})});bm.displayName=bj;var bn="PopperArrow",bo={top:"bottom",right:"left",bottom:"top",left:"right"},bp=g.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,e=bl(bn,c),f=bo[e.placedSide];return(0,o.jsx)("span",{ref:e.onArrowChange,style:{position:"absolute",left:e.arrowX,top:e.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[e.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[e.placedSide],visibility:e.shouldHideArrow?"hidden":void 0},children:(0,o.jsx)(ba,{...d,ref:b,style:{...d.style,display:"block"}})})});function bq(a){return null!==a}bp.displayName=bn;var br=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=bs(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function bs(a){let[b,c="center"]=a.split("-");return[b,c]}var bt=g.forwardRef((a,b)=>{let{container:c,...d}=a,[e,f]=g.useState(!1);(0,L.N)(()=>f(!0),[]);let h=c||e&&globalThis?.document?.body;return h?i.createPortal((0,o.jsx)(t.sG.div,{...d,ref:b}),h):null});bt.displayName="Portal";var bu=h[" useInsertionEffect ".trim().toString()]||L.N;function bv({prop:a,defaultProp:b,onChange:c=()=>{},caller:d}){let[e,f,h]=function({defaultProp:a,onChange:b}){let[c,d]=g.useState(a),e=g.useRef(c),f=g.useRef(b);return bu(()=>{f.current=b},[b]),g.useEffect(()=>{e.current!==c&&(f.current?.(c),e.current=c)},[c,e]),[c,d,f]}({defaultProp:b,onChange:c}),i=void 0!==a,j=i?a:e;{let b=g.useRef(void 0!==a);g.useEffect(()=>{let a=b.current;if(a!==i){let b=i?"controlled":"uncontrolled";console.warn(`${d} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}b.current=i},[i,d])}return[j,g.useCallback(b=>{if(i){let c="function"==typeof b?b(a):b;c!==a&&h.current?.(c)}else f(b)},[i,a,f,h])]}Symbol("RADIX:SYNC_STATE");var bw=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"});g.forwardRef((a,b)=>(0,o.jsx)(t.sG.span,{...a,ref:b,style:{...bw,...a.style}})).displayName="VisuallyHidden";var bx=new WeakMap,by=new WeakMap,bz={},bA=0,bB=function(a){return a&&(a.host||bB(a.parentNode))},bC=function(a,b,c,d){var e=(Array.isArray(a)?a:[a]).map(function(a){if(b.contains(a))return a;var c=bB(a);return c&&b.contains(c)?c:(console.error("aria-hidden",a,"in not contained inside",b,". Doing nothing"),null)}).filter(function(a){return!!a});bz[c]||(bz[c]=new WeakMap);var f=bz[c],g=[],h=new Set,i=new Set(e),j=function(a){!a||h.has(a)||(h.add(a),j(a.parentNode))};e.forEach(j);var k=function(a){!a||i.has(a)||Array.prototype.forEach.call(a.children,function(a){if(h.has(a))k(a);else try{var b=a.getAttribute(d),e=null!==b&&"false"!==b,i=(bx.get(a)||0)+1,j=(f.get(a)||0)+1;bx.set(a,i),f.set(a,j),g.push(a),1===i&&e&&by.set(a,!0),1===j&&a.setAttribute(c,"true"),e||a.setAttribute(d,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return k(b),h.clear(),bA++,function(){g.forEach(function(a){var b=bx.get(a)-1,e=f.get(a)-1;bx.set(a,b),f.set(a,e),b||(by.has(a)||a.removeAttribute(d),by.delete(a)),e||a.removeAttribute(c)}),--bA||(bx=new WeakMap,bx=new WeakMap,by=new WeakMap,bz={})}},bD=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),bC(d,e,c,"aria-hidden")):function(){return null}},bE=function(){return(bE=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function bF(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var bG=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),bH="width-before-scroll-bar";function bI(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var bJ="undefined"!=typeof window?g.useLayoutEffect:g.useEffect,bK=new WeakMap;function bL(a){return a}var bM=function(a){void 0===a&&(a={});var b,c,d,e=(void 0===b&&(b=bL),c=[],d=!1,{read:function(){if(d)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var e=b(a,d);return c.push(e),function(){c=c.filter(function(a){return a!==e})}},assignSyncMedium:function(a){for(d=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){d=!0;var b=[];if(c.length){var e=c;c=[],e.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return e.options=bE({async:!0,ssr:!1},a),e}(),bN=function(){},bO=g.forwardRef(function(a,b){var c,d,e,f,h=g.useRef(null),i=g.useState({onScrollCapture:bN,onWheelCapture:bN,onTouchMoveCapture:bN}),j=i[0],k=i[1],l=a.forwardProps,m=a.children,n=a.className,o=a.removeScrollBar,p=a.enabled,q=a.shards,r=a.sideCar,s=a.noRelative,t=a.noIsolation,u=a.inert,v=a.allowPinchZoom,w=a.as,x=a.gapMode,y=bF(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),z=(c=[h,b],d=function(a){return c.forEach(function(b){return bI(b,a)})},(e=(0,g.useState)(function(){return{value:null,callback:d,facade:{get current(){return e.value},set current(value){var a=e.value;a!==value&&(e.value=value,e.callback(value,a))}}}})[0]).callback=d,f=e.facade,bJ(function(){var a=bK.get(f);if(a){var b=new Set(a),d=new Set(c),e=f.current;b.forEach(function(a){d.has(a)||bI(a,null)}),d.forEach(function(a){b.has(a)||bI(a,e)})}bK.set(f,c)},[c]),f),A=bE(bE({},y),j);return g.createElement(g.Fragment,null,p&&g.createElement(r,{sideCar:bM,removeScrollBar:o,shards:q,noRelative:s,noIsolation:t,inert:u,setCallbacks:k,allowPinchZoom:!!v,lockRef:h,gapMode:x}),l?g.cloneElement(g.Children.only(m),bE(bE({},A),{ref:z})):g.createElement(void 0===w?"div":w,bE({},A,{className:n,ref:z}),m))});bO.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},bO.classNames={fullWidth:bH,zeroRight:bG};var bP=function(a){var b=a.sideCar,c=bF(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var d=b.read();if(!d)throw Error("Sidecar medium not found");return g.createElement(d,bE({},c))};bP.isSideCarExport=!0;var bQ=function(){var a=0,b=null;return{add:function(d){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=f||c.nc;return b&&a.setAttribute("nonce",b),a}())){var e,g;(e=b).styleSheet?e.styleSheet.cssText=d:e.appendChild(document.createTextNode(d)),g=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(g)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},bR=function(){var a=bQ();return function(b,c){g.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},bS=function(){var a=bR();return function(b){return a(b.styles,b.dynamic),null}},bT={left:0,top:0,right:0,gap:0},bU=function(a){return parseInt(a||"",10)||0},bV=function(a){var b=window.getComputedStyle(document.body),c=b["padding"===a?"paddingLeft":"marginLeft"],d=b["padding"===a?"paddingTop":"marginTop"],e=b["padding"===a?"paddingRight":"marginRight"];return[bU(c),bU(d),bU(e)]},bW=function(a){if(void 0===a&&(a="margin"),"undefined"==typeof window)return bT;var b=bV(a),c=document.documentElement.clientWidth,d=window.innerWidth;return{left:b[0],top:b[1],right:b[2],gap:Math.max(0,d-c+b[2]-b[0])}},bX=bS(),bY="data-scroll-locked",bZ=function(a,b,c,d){var e=a.left,f=a.top,g=a.right,h=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(h,"px ").concat(d,";\n  }\n  body[").concat(bY,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(g,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(h,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(bG," {\n    right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(bH," {\n    margin-right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(bG," .").concat(bG," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(bH," .").concat(bH," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(bY,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(h,"px;\n  }\n")},b$=function(){var a=parseInt(document.body.getAttribute(bY)||"0",10);return isFinite(a)?a:0},b_=function(){g.useEffect(function(){return document.body.setAttribute(bY,(b$()+1).toString()),function(){var a=b$()-1;a<=0?document.body.removeAttribute(bY):document.body.setAttribute(bY,a.toString())}},[])},b0=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;b_();var f=g.useMemo(function(){return bW(e)},[e]);return g.createElement(bX,{styles:bZ(f,!b,e,c?"":"!important")})},b1=!1;if("undefined"!=typeof window)try{var b2=Object.defineProperty({},"passive",{get:function(){return b1=!0,!0}});window.addEventListener("test",b2,b2),window.removeEventListener("test",b2,b2)}catch(a){b1=!1}var b3=!!b1&&{passive:!1},b4=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},b5=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),b6(a,d)){var e=b7(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body);return!1},b6=function(a,b){return"v"===a?b4(b,"overflowY"):b4(b,"overflowX")},b7=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},b8=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=b7(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&b6(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i));return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},b9=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},ca=function(a){return[a.deltaX,a.deltaY]},cb=function(a){return a&&"current"in a?a.current:a},cc=0,cd=[];let ce=(d=function(a){var b=g.useRef([]),c=g.useRef([0,0]),d=g.useRef(),e=g.useState(cc++)[0],f=g.useState(bS)[0],h=g.useRef(a);g.useEffect(function(){h.current=a},[a]),g.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(cb),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var i=g.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!h.current.allowPinchZoom;var e,f=b9(a),g=c.current,i="deltaX"in a?a.deltaX:g[0]-f[0],j="deltaY"in a?a.deltaY:g[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=b5(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=b5(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return b8(n,b,a,"h"===n?i:j,!0)},[]),j=g.useCallback(function(a){if(cd.length&&cd[cd.length-1]===f){var c="deltaY"in a?ca(a):b9(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(h.current.shards||[]).map(cb).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?i(a,e[0]):!h.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=g.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=g.useCallback(function(a){c.current=b9(a),d.current=void 0},[]),m=g.useCallback(function(b){k(b.type,ca(b),b.target,i(b,a.lockRef.current))},[]),n=g.useCallback(function(b){k(b.type,b9(b),b.target,i(b,a.lockRef.current))},[]);g.useEffect(function(){return cd.push(f),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,b3),document.addEventListener("touchmove",j,b3),document.addEventListener("touchstart",l,b3),function(){cd=cd.filter(function(a){return a!==f}),document.removeEventListener("wheel",j,b3),document.removeEventListener("touchmove",j,b3),document.removeEventListener("touchstart",l,b3)}},[]);var o=a.removeScrollBar,p=a.inert;return g.createElement(g.Fragment,null,p?g.createElement(f,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?g.createElement(b0,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},bM.useMedium(d),bP);var cf=g.forwardRef(function(a,b){return g.createElement(bO,bE({},a,{ref:b,sideCar:ce}))});cf.classNames=bO.classNames;var cg=[" ","Enter","ArrowUp","ArrowDown"],ch=[" ","Enter"],ci="Select",[cj,ck,cl]=function(a){let b=a+"CollectionProvider",[c,d]=(0,l.A)(b),[e,f]=c(b,{collectionRef:{current:null},itemMap:new Map}),h=a=>{let{scope:b,children:c}=a,d=g.useRef(null),f=g.useRef(new Map).current;return(0,o.jsx)(e,{scope:b,itemMap:f,collectionRef:d,children:c})};h.displayName=b;let i=a+"CollectionSlot",j=(0,n.TL)(i),k=g.forwardRef((a,b)=>{let{scope:c,children:d}=a,e=f(i,c),g=(0,m.s)(b,e.collectionRef);return(0,o.jsx)(j,{ref:g,children:d})});k.displayName=i;let p=a+"CollectionItemSlot",q="data-radix-collection-item",r=(0,n.TL)(p),s=g.forwardRef((a,b)=>{let{scope:c,children:d,...e}=a,h=g.useRef(null),i=(0,m.s)(b,h),j=f(p,c);return g.useEffect(()=>(j.itemMap.set(h,{ref:h,...e}),()=>void j.itemMap.delete(h))),(0,o.jsx)(r,{...{[q]:""},ref:i,children:d})});return s.displayName=p,[{Provider:h,Slot:k,ItemSlot:s},function(b){let c=f(a+"CollectionConsumer",b);return g.useCallback(()=>{let a=c.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${q}]`));return Array.from(c.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[c.collectionRef,c.itemMap])},d]}(ci),[cm,cn]=(0,l.A)(ci,[cl,bd]),co=bd(),[cp,cq]=cm(ci),[cr,cs]=cm(ci),ct=a=>{let{__scopeSelect:b,children:c,open:d,defaultOpen:e,onOpenChange:f,value:h,defaultValue:i,onValueChange:j,dir:k,name:l,autoComplete:m,disabled:n,required:p,form:q}=a,r=co(b),[t,u]=g.useState(null),[v,w]=g.useState(null),[x,y]=g.useState(!1),z=function(a){let b=g.useContext(s);return a||b||"ltr"}(k),[A,B]=bv({prop:d,defaultProp:e??!1,onChange:f,caller:ci}),[C,D]=bv({prop:h,defaultProp:i,onChange:j,caller:ci}),E=g.useRef(null),F=!t||q||!!t.closest("form"),[G,H]=g.useState(new Set),I=Array.from(G).map(a=>a.props.value).join(";");return(0,o.jsx)(bg,{...r,children:(0,o.jsxs)(cp,{required:p,scope:b,trigger:t,onTriggerChange:u,valueNode:v,onValueNodeChange:w,valueNodeHasChildren:x,onValueNodeHasChildrenChange:y,contentId:O(),value:C,onValueChange:D,open:A,onOpenChange:B,dir:z,triggerPointerDownPosRef:E,disabled:n,children:[(0,o.jsx)(cj.Provider,{scope:b,children:(0,o.jsx)(cr,{scope:a.__scopeSelect,onNativeOptionAdd:g.useCallback(a=>{H(b=>new Set(b).add(a))},[]),onNativeOptionRemove:g.useCallback(a=>{H(b=>{let c=new Set(b);return c.delete(a),c})},[]),children:c})}),F?(0,o.jsxs)(c2,{"aria-hidden":!0,required:p,tabIndex:-1,name:l,autoComplete:m,value:C,onChange:a=>D(a.target.value),disabled:n,form:q,children:[void 0===C?(0,o.jsx)("option",{value:""}):null,Array.from(G)]},I):null]})})};ct.displayName=ci;var cu="SelectTrigger",cv=g.forwardRef((a,b)=>{let{__scopeSelect:c,disabled:d=!1,...e}=a,f=co(c),h=cq(cu,c),i=h.disabled||d,j=(0,m.s)(b,h.onTriggerChange),l=ck(c),n=g.useRef("touch"),[p,q,r]=c4(a=>{let b=l().filter(a=>!a.disabled),c=b.find(a=>a.value===h.value),d=c5(b,a,c);void 0!==d&&h.onValueChange(d.value)}),s=a=>{i||(h.onOpenChange(!0),r()),a&&(h.triggerPointerDownPosRef.current={x:Math.round(a.pageX),y:Math.round(a.pageY)})};return(0,o.jsx)(bi,{asChild:!0,...f,children:(0,o.jsx)(t.sG.button,{type:"button",role:"combobox","aria-controls":h.contentId,"aria-expanded":h.open,"aria-required":h.required,"aria-autocomplete":"none",dir:h.dir,"data-state":h.open?"open":"closed",disabled:i,"data-disabled":i?"":void 0,"data-placeholder":c3(h.value)?"":void 0,...e,ref:j,onClick:k(e.onClick,a=>{a.currentTarget.focus(),"mouse"!==n.current&&s(a)}),onPointerDown:k(e.onPointerDown,a=>{n.current=a.pointerType;let b=a.target;b.hasPointerCapture(a.pointerId)&&b.releasePointerCapture(a.pointerId),0===a.button&&!1===a.ctrlKey&&"mouse"===a.pointerType&&(s(a),a.preventDefault())}),onKeyDown:k(e.onKeyDown,a=>{let b=""!==p.current;a.ctrlKey||a.altKey||a.metaKey||1!==a.key.length||q(a.key),(!b||" "!==a.key)&&cg.includes(a.key)&&(s(),a.preventDefault())})})})});cv.displayName=cu;var cw="SelectValue",cx=g.forwardRef((a,b)=>{let{__scopeSelect:c,className:d,style:e,children:f,placeholder:g="",...h}=a,i=cq(cw,c),{onValueNodeHasChildrenChange:j}=i,k=void 0!==f,l=(0,m.s)(b,i.onValueNodeChange);return(0,L.N)(()=>{j(k)},[j,k]),(0,o.jsx)(t.sG.span,{...h,ref:l,style:{pointerEvents:"none"},children:c3(i.value)?(0,o.jsx)(o.Fragment,{children:g}):f})});cx.displayName=cw;var cy=g.forwardRef((a,b)=>{let{__scopeSelect:c,children:d,...e}=a;return(0,o.jsx)(t.sG.span,{"aria-hidden":!0,...e,ref:b,children:d||"▼"})});cy.displayName="SelectIcon";var cz=a=>(0,o.jsx)(bt,{asChild:!0,...a});cz.displayName="SelectPortal";var cA="SelectContent",cB=g.forwardRef((a,b)=>{let c=cq(cA,a.__scopeSelect),[d,e]=g.useState();return((0,L.N)(()=>{e(new DocumentFragment)},[]),c.open)?(0,o.jsx)(cF,{...a,ref:b}):d?i.createPortal((0,o.jsx)(cC,{scope:a.__scopeSelect,children:(0,o.jsx)(cj.Slot,{scope:a.__scopeSelect,children:(0,o.jsx)("div",{children:a.children})})}),d):null});cB.displayName=cA;var[cC,cD]=cm(cA),cE=(0,n.TL)("SelectContent.RemoveScroll"),cF=g.forwardRef((a,b)=>{let{__scopeSelect:c,position:d="item-aligned",onCloseAutoFocus:e,onEscapeKeyDown:f,onPointerDownOutside:h,side:i,sideOffset:j,align:l,alignOffset:n,arrowPadding:p,collisionBoundary:q,collisionPadding:r,sticky:s,hideWhenDetached:t,avoidCollisions:u,...v}=a,w=cq(cA,c),[y,z]=g.useState(null),[C,D]=g.useState(null),E=(0,m.s)(b,a=>z(a)),[G,H]=g.useState(null),[I,J]=g.useState(null),K=ck(c),[L,M]=g.useState(!1),N=g.useRef(!1);g.useEffect(()=>{if(y)return bD(y)},[y]),g.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??B()),document.body.insertAdjacentElement("beforeend",a[1]??B()),A++,()=>{1===A&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),A--}},[]);let O=g.useCallback(a=>{let[b,...c]=K().map(a=>a.ref.current),[d]=c.slice(-1),e=document.activeElement;for(let c of a)if(c===e||(c?.scrollIntoView({block:"nearest"}),c===b&&C&&(C.scrollTop=0),c===d&&C&&(C.scrollTop=C.scrollHeight),c?.focus(),document.activeElement!==e))return},[K,C]),P=g.useCallback(()=>O([G,y]),[O,G,y]);g.useEffect(()=>{L&&P()},[L,P]);let{onOpenChange:Q,triggerPointerDownPosRef:R}=w;g.useEffect(()=>{if(y){let a={x:0,y:0},b=b=>{a={x:Math.abs(Math.round(b.pageX)-(R.current?.x??0)),y:Math.abs(Math.round(b.pageY)-(R.current?.y??0))}},c=c=>{a.x<=10&&a.y<=10?c.preventDefault():y.contains(c.target)||Q(!1),document.removeEventListener("pointermove",b),R.current=null};return null!==R.current&&(document.addEventListener("pointermove",b),document.addEventListener("pointerup",c,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",b),document.removeEventListener("pointerup",c,{capture:!0})}}},[y,Q,R]),g.useEffect(()=>{let a=()=>Q(!1);return window.addEventListener("blur",a),window.addEventListener("resize",a),()=>{window.removeEventListener("blur",a),window.removeEventListener("resize",a)}},[Q]);let[S,T]=c4(a=>{let b=K().filter(a=>!a.disabled),c=b.find(a=>a.ref.current===document.activeElement),d=c5(b,a,c);d&&setTimeout(()=>d.ref.current.focus())}),U=g.useCallback((a,b,c)=>{let d=!N.current&&!c;(void 0!==w.value&&w.value===b||d)&&(H(a),d&&(N.current=!0))},[w.value]),V=g.useCallback(()=>y?.focus(),[y]),W=g.useCallback((a,b,c)=>{let d=!N.current&&!c;(void 0!==w.value&&w.value===b||d)&&J(a)},[w.value]),X="popper"===d?cH:cG,Y=X===cH?{side:i,sideOffset:j,align:l,alignOffset:n,arrowPadding:p,collisionBoundary:q,collisionPadding:r,sticky:s,hideWhenDetached:t,avoidCollisions:u}:{};return(0,o.jsx)(cC,{scope:c,content:y,viewport:C,onViewportChange:D,itemRefCallback:U,selectedItem:G,onItemLeave:V,itemTextRefCallback:W,focusSelectedItem:P,selectedItemText:I,position:d,isPositioned:L,searchRef:S,children:(0,o.jsx)(cf,{as:cE,allowPinchZoom:!0,children:(0,o.jsx)(F,{asChild:!0,trapped:w.open,onMountAutoFocus:a=>{a.preventDefault()},onUnmountAutoFocus:k(e,a=>{w.trigger?.focus({preventScroll:!0}),a.preventDefault()}),children:(0,o.jsx)(x,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:f,onPointerDownOutside:h,onFocusOutside:a=>a.preventDefault(),onDismiss:()=>w.onOpenChange(!1),children:(0,o.jsx)(X,{role:"listbox",id:w.contentId,"data-state":w.open?"open":"closed",dir:w.dir,onContextMenu:a=>a.preventDefault(),...v,...Y,onPlaced:()=>M(!0),ref:E,style:{display:"flex",flexDirection:"column",outline:"none",...v.style},onKeyDown:k(v.onKeyDown,a=>{let b=a.ctrlKey||a.altKey||a.metaKey;if("Tab"===a.key&&a.preventDefault(),b||1!==a.key.length||T(a.key),["ArrowUp","ArrowDown","Home","End"].includes(a.key)){let b=K().filter(a=>!a.disabled).map(a=>a.ref.current);if(["ArrowUp","End"].includes(a.key)&&(b=b.slice().reverse()),["ArrowUp","ArrowDown"].includes(a.key)){let c=a.target,d=b.indexOf(c);b=b.slice(d+1)}setTimeout(()=>O(b)),a.preventDefault()}})})})})})})});cF.displayName="SelectContentImpl";var cG=g.forwardRef((a,b)=>{let{__scopeSelect:c,onPlaced:d,...e}=a,f=cq(cA,c),h=cD(cA,c),[i,k]=g.useState(null),[l,n]=g.useState(null),p=(0,m.s)(b,a=>n(a)),q=ck(c),r=g.useRef(!1),s=g.useRef(!0),{viewport:u,selectedItem:v,selectedItemText:w,focusSelectedItem:x}=h,y=g.useCallback(()=>{if(f.trigger&&f.valueNode&&i&&l&&u&&v&&w){let a=f.trigger.getBoundingClientRect(),b=l.getBoundingClientRect(),c=f.valueNode.getBoundingClientRect(),e=w.getBoundingClientRect();if("rtl"!==f.dir){let d=e.left-b.left,f=c.left-d,g=a.left-f,h=a.width+g,k=Math.max(h,b.width),l=j(f,[10,Math.max(10,window.innerWidth-10-k)]);i.style.minWidth=h+"px",i.style.left=l+"px"}else{let d=b.right-e.right,f=window.innerWidth-c.right-d,g=window.innerWidth-a.right-f,h=a.width+g,k=Math.max(h,b.width),l=j(f,[10,Math.max(10,window.innerWidth-10-k)]);i.style.minWidth=h+"px",i.style.right=l+"px"}let g=q(),h=window.innerHeight-20,k=u.scrollHeight,m=window.getComputedStyle(l),n=parseInt(m.borderTopWidth,10),o=parseInt(m.paddingTop,10),p=parseInt(m.borderBottomWidth,10),s=n+o+k+parseInt(m.paddingBottom,10)+p,t=Math.min(5*v.offsetHeight,s),x=window.getComputedStyle(u),y=parseInt(x.paddingTop,10),z=parseInt(x.paddingBottom,10),A=a.top+a.height/2-10,B=v.offsetHeight/2,C=n+o+(v.offsetTop+B);if(C<=A){let a=g.length>0&&v===g[g.length-1].ref.current;i.style.bottom="0px";let b=Math.max(h-A,B+(a?z:0)+(l.clientHeight-u.offsetTop-u.offsetHeight)+p);i.style.height=C+b+"px"}else{let a=g.length>0&&v===g[0].ref.current;i.style.top="0px";let b=Math.max(A,n+u.offsetTop+(a?y:0)+B);i.style.height=b+(s-C)+"px",u.scrollTop=C-A+u.offsetTop}i.style.margin="10px 0",i.style.minHeight=t+"px",i.style.maxHeight=h+"px",d?.(),requestAnimationFrame(()=>r.current=!0)}},[q,f.trigger,f.valueNode,i,l,u,v,w,f.dir,d]);(0,L.N)(()=>y(),[y]);let[z,A]=g.useState();(0,L.N)(()=>{l&&A(window.getComputedStyle(l).zIndex)},[l]);let B=g.useCallback(a=>{a&&!0===s.current&&(y(),x?.(),s.current=!1)},[y,x]);return(0,o.jsx)(cI,{scope:c,contentWrapper:i,shouldExpandOnScrollRef:r,onScrollButtonChange:B,children:(0,o.jsx)("div",{ref:k,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:z},children:(0,o.jsx)(t.sG.div,{...e,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...e.style}})})})});cG.displayName="SelectItemAlignedPosition";var cH=g.forwardRef((a,b)=>{let{__scopeSelect:c,align:d="start",collisionPadding:e=10,...f}=a,g=co(c);return(0,o.jsx)(bm,{...g,...f,ref:b,align:d,collisionPadding:e,style:{boxSizing:"border-box",...f.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});cH.displayName="SelectPopperPosition";var[cI,cJ]=cm(cA,{}),cK="SelectViewport",cL=g.forwardRef((a,b)=>{let{__scopeSelect:c,nonce:d,...e}=a,f=cD(cK,c),h=cJ(cK,c),i=(0,m.s)(b,f.onViewportChange),j=g.useRef(0);return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:d}),(0,o.jsx)(cj.Slot,{scope:c,children:(0,o.jsx)(t.sG.div,{"data-radix-select-viewport":"",role:"presentation",...e,ref:i,style:{position:"relative",flex:1,overflow:"hidden auto",...e.style},onScroll:k(e.onScroll,a=>{let b=a.currentTarget,{contentWrapper:c,shouldExpandOnScrollRef:d}=h;if(d?.current&&c){let a=Math.abs(j.current-b.scrollTop);if(a>0){let d=window.innerHeight-20,e=Math.max(parseFloat(c.style.minHeight),parseFloat(c.style.height));if(e<d){let f=e+a,g=Math.min(d,f),h=f-g;c.style.height=g+"px","0px"===c.style.bottom&&(b.scrollTop=h>0?h:0,c.style.justifyContent="flex-end")}}}j.current=b.scrollTop})})})]})});cL.displayName=cK;var cM="SelectGroup",[cN,cO]=cm(cM);g.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=O();return(0,o.jsx)(cN,{scope:c,id:e,children:(0,o.jsx)(t.sG.div,{role:"group","aria-labelledby":e,...d,ref:b})})}).displayName=cM;var cP="SelectLabel";g.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=cO(cP,c);return(0,o.jsx)(t.sG.div,{id:e.id,...d,ref:b})}).displayName=cP;var cQ="SelectItem",[cR,cS]=cm(cQ),cT=g.forwardRef((a,b)=>{let{__scopeSelect:c,value:d,disabled:e=!1,textValue:f,...h}=a,i=cq(cQ,c),j=cD(cQ,c),l=i.value===d,[n,p]=g.useState(f??""),[q,r]=g.useState(!1),s=(0,m.s)(b,a=>j.itemRefCallback?.(a,d,e)),u=O(),v=g.useRef("touch"),w=()=>{e||(i.onValueChange(d),i.onOpenChange(!1))};if(""===d)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,o.jsx)(cR,{scope:c,value:d,disabled:e,textId:u,isSelected:l,onItemTextChange:g.useCallback(a=>{p(b=>b||(a?.textContent??"").trim())},[]),children:(0,o.jsx)(cj.ItemSlot,{scope:c,value:d,disabled:e,textValue:n,children:(0,o.jsx)(t.sG.div,{role:"option","aria-labelledby":u,"data-highlighted":q?"":void 0,"aria-selected":l&&q,"data-state":l?"checked":"unchecked","aria-disabled":e||void 0,"data-disabled":e?"":void 0,tabIndex:e?void 0:-1,...h,ref:s,onFocus:k(h.onFocus,()=>r(!0)),onBlur:k(h.onBlur,()=>r(!1)),onClick:k(h.onClick,()=>{"mouse"!==v.current&&w()}),onPointerUp:k(h.onPointerUp,()=>{"mouse"===v.current&&w()}),onPointerDown:k(h.onPointerDown,a=>{v.current=a.pointerType}),onPointerMove:k(h.onPointerMove,a=>{v.current=a.pointerType,e?j.onItemLeave?.():"mouse"===v.current&&a.currentTarget.focus({preventScroll:!0})}),onPointerLeave:k(h.onPointerLeave,a=>{a.currentTarget===document.activeElement&&j.onItemLeave?.()}),onKeyDown:k(h.onKeyDown,a=>{(j.searchRef?.current===""||" "!==a.key)&&(ch.includes(a.key)&&w()," "===a.key&&a.preventDefault())})})})})});cT.displayName=cQ;var cU="SelectItemText",cV=g.forwardRef((a,b)=>{let{__scopeSelect:c,className:d,style:e,...f}=a,h=cq(cU,c),j=cD(cU,c),k=cS(cU,c),l=cs(cU,c),[n,p]=g.useState(null),q=(0,m.s)(b,a=>p(a),k.onItemTextChange,a=>j.itemTextRefCallback?.(a,k.value,k.disabled)),r=n?.textContent,s=g.useMemo(()=>(0,o.jsx)("option",{value:k.value,disabled:k.disabled,children:r},k.value),[k.disabled,k.value,r]),{onNativeOptionAdd:u,onNativeOptionRemove:v}=l;return(0,L.N)(()=>(u(s),()=>v(s)),[u,v,s]),(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(t.sG.span,{id:k.textId,...f,ref:q}),k.isSelected&&h.valueNode&&!h.valueNodeHasChildren?i.createPortal(f.children,h.valueNode):null]})});cV.displayName=cU;var cW="SelectItemIndicator",cX=g.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return cS(cW,c).isSelected?(0,o.jsx)(t.sG.span,{"aria-hidden":!0,...d,ref:b}):null});cX.displayName=cW;var cY="SelectScrollUpButton",cZ=g.forwardRef((a,b)=>{let c=cD(cY,a.__scopeSelect),d=cJ(cY,a.__scopeSelect),[e,f]=g.useState(!1),h=(0,m.s)(b,d.onScrollButtonChange);return(0,L.N)(()=>{if(c.viewport&&c.isPositioned){let a=function(){f(b.scrollTop>0)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),e?(0,o.jsx)(c0,{...a,ref:h,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop-b.offsetHeight)}}):null});cZ.displayName=cY;var c$="SelectScrollDownButton",c_=g.forwardRef((a,b)=>{let c=cD(c$,a.__scopeSelect),d=cJ(c$,a.__scopeSelect),[e,f]=g.useState(!1),h=(0,m.s)(b,d.onScrollButtonChange);return(0,L.N)(()=>{if(c.viewport&&c.isPositioned){let a=function(){let a=b.scrollHeight-b.clientHeight;f(Math.ceil(b.scrollTop)<a)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),e?(0,o.jsx)(c0,{...a,ref:h,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop+b.offsetHeight)}}):null});c_.displayName=c$;var c0=g.forwardRef((a,b)=>{let{__scopeSelect:c,onAutoScroll:d,...e}=a,f=cD("SelectScrollButton",c),h=g.useRef(null),i=ck(c),j=g.useCallback(()=>{null!==h.current&&(window.clearInterval(h.current),h.current=null)},[]);return g.useEffect(()=>()=>j(),[j]),(0,L.N)(()=>{let a=i().find(a=>a.ref.current===document.activeElement);a?.ref.current?.scrollIntoView({block:"nearest"})},[i]),(0,o.jsx)(t.sG.div,{"aria-hidden":!0,...e,ref:b,style:{flexShrink:0,...e.style},onPointerDown:k(e.onPointerDown,()=>{null===h.current&&(h.current=window.setInterval(d,50))}),onPointerMove:k(e.onPointerMove,()=>{f.onItemLeave?.(),null===h.current&&(h.current=window.setInterval(d,50))}),onPointerLeave:k(e.onPointerLeave,()=>{j()})})});g.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return(0,o.jsx)(t.sG.div,{"aria-hidden":!0,...d,ref:b})}).displayName="SelectSeparator";var c1="SelectArrow";g.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=co(c),f=cq(c1,c),g=cD(c1,c);return f.open&&"popper"===g.position?(0,o.jsx)(bp,{...e,...d,ref:b}):null}).displayName=c1;var c2=g.forwardRef(({__scopeSelect:a,value:b,...c},d)=>{let e=g.useRef(null),f=(0,m.s)(d,e),h=function(a){let b=g.useRef({value:a,previous:a});return g.useMemo(()=>(b.current.value!==a&&(b.current.previous=b.current.value,b.current.value=a),b.current.previous),[a])}(b);return g.useEffect(()=>{let a=e.current;if(!a)return;let c=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(h!==b&&c){let d=new Event("change",{bubbles:!0});c.call(a,b),a.dispatchEvent(d)}},[h,b]),(0,o.jsx)(t.sG.select,{...c,style:{...bw,...c.style},ref:f,defaultValue:b})});function c3(a){return""===a||void 0===a}function c4(a){let b=(0,u.c)(a),c=g.useRef(""),d=g.useRef(0),e=g.useCallback(a=>{let e=c.current+a;b(e),function a(b){c.current=b,window.clearTimeout(d.current),""!==b&&(d.current=window.setTimeout(()=>a(""),1e3))}(e)},[b]),f=g.useCallback(()=>{c.current="",window.clearTimeout(d.current)},[]);return g.useEffect(()=>()=>window.clearTimeout(d.current),[]),[c,e,f]}function c5(a,b,c){var d,e;let f=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,g=c?a.indexOf(c):-1,h=(d=a,e=Math.max(g,0),d.map((a,b)=>d[(e+b)%d.length]));1===f.length&&(h=h.filter(a=>a!==c));let i=h.find(a=>a.textValue.toLowerCase().startsWith(f.toLowerCase()));return i!==c?i:void 0}c2.displayName="SelectBubbleInput";var c6=ct,c7=cv,c8=cx,c9=cy,da=cz,db=cB,dc=cL,dd=cT,de=cV,df=cX,dg=cZ,dh=c_},7324:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(1209).A)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9802:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(1209).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])}};