"use client";

import { useState } from "react";
import { NavigationV78 } from "@/components/NavigationV78";
import { ChatWindow } from "@/components/ChatWindow";
import { ChatFloatingButton } from "@/components/ChatFloatingButton";
import { useRouter } from "next/navigation";

export default function Login() {
  const router = useRouter();
  const [isChatOpen, setIsChatOpen] = useState(false);
  const [notificationCount] = useState(3);

  const handleNavigate = (page: string) => {
    switch (page) {
      case "home":
      case "landing":
        router.push("/");
        break;
      case "services":
        router.push("/services");
        break;
      case "orders":
        router.push("/orders");
        break;
      case "favorites":
        router.push("/favorites");
        break;
      case "support":
        router.push("/support");
        break;
      case "gallery":
        router.push("/gallery");
        break;
      case "login":
        router.push("/login");
        break;
      case "register":
        router.push("/register");
        break;
      default:
        break;
    }
  };

  const handleOpenChat = () => {
    setIsChatOpen(true);
  };

  const handleCloseChat = () => {
    setIsChatOpen(false);
  };

  const handleMinimizeChat = () => {
    setIsChatOpen(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50/30">
      <NavigationV78 
        currentPage="services" 
        onNavigate={handleNavigate}
        onOpenChat={handleOpenChat}
      />
      
      <div className="max-w-md mx-auto px-4 py-20">
        <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-8">
          <h2 className="text-2xl font-bold text-slate-900 mb-6 text-center">
            登录
          </h2>
          <p className="text-slate-600 text-center">功能开发中，敬请期待...</p>
        </div>
      </div>

      {/* 聊天窗口 */}
      <ChatWindow isOpen={isChatOpen} onClose={handleCloseChat} onMinimize={handleMinimizeChat} />
      
      {/* 聊天悬浮球 */}
      <ChatFloatingButton 
        onClick={handleOpenChat}
        notificationCount={notificationCount}
        isVisible={!isChatOpen}
      />
    </div>
  );
}
